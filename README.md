# Secure `.env` File Sharing with `git-crypt` + GPG

This repo uses **[`git-crypt`](https://github.com/AGWA/git-crypt)** to store `.env` files in Git **encrypted**.
Only developers with an authorized **GPG key** can decrypt and use them.

---

## 🔒 Why?
- Keep `.env` files version-controlled without leaking secrets.
- Restrict access to authorized team members.
- Easily revoke access if someone leaves.

---

## 📦 Prerequisites

- **GPG** installed:
  - macOS: `brew install gnupg`
  - Linux: `sudo apt-get install gnupg`
  - Windows: Install [GPG4Win](https://gpg4win.org/) or use WSL.

- **git-crypt** installed:
  - macOS: `brew install git-crypt`
  - Linux: `sudo apt-get install git-crypt`
  - Windows: [Download binary](https://github.com/AGWA/git-crypt/releases) or use WSL.

---

## 🛠 Setup

### 1. Check for an Existing GPG Key
```bash
gpg --list-secret-keys --keyid-format=long
```
If you see something like:
```
sec   rsa4096/ABC123DEF4567890 2025-01-01 [SC]
```
you already have a key. Skip to step 3.

---

### 2. Create a New GPG Key
```bash
gpg --full-generate-key
```
- **Key type:** RSA and RSA
- **Key size:** 4096
- **Expiration:** 0 (never) or choose a renewal date
- **Name:** Your name
- **Email:** Your Git/GitHub email
- **Passphrase:** Optional but recommended

---

### 3. Export Your Public Key
Find your key ID:
```bash
gpg --list-secret-keys --keyid-format=long
```
Copy the value after `rsa4096/` (e.g., `ABC123DEF4567890`).

Export it:
```bash
gpg --armor --export ABC123DEF4567890 > mykey.pub
```

---

### 4. Send Your Public Key to the Maintainer
Share `mykey.pub` securely (Slack DM, email, 1Password, etc.).

---

### 5. Maintainer: Add the Key to `git-crypt`
The maintainer runs:
```bash
gpg --import mykey.pub
git-crypt add-gpg-user --trusted <EMAIL>
git push
```

---

### 6. Unlock Encrypted Files
Once you’re added, clone the repo and run:
```bash
git-crypt unlock
```
You now have access to `.env` and other encrypted files.

---

## 🔄 Rotating Keys
If a key is compromised:
1. Remove the user from `git-crypt`.
2. Rotate any exposed secrets.
3. Re-encrypt and push.

---

## 📁 Files Encrypted in This Repo
Configured in `.gitattributes`:
```
.env filter=git-crypt diff=git-crypt
.env.* filter=git-crypt diff=git-crypt
```

---

## 💡 Tips
- **Backup your private key**:
  ```bash
  gpg --export-secret-keys --armor ABC123DEF4567890 > my-private-key.asc
  ```
  Store it in a secure password manager or offline.

- If you switch machines, import your private key to regain access.

---

## 📚 Resources
- [git-crypt Documentation](https://github.com/AGWA/git-crypt)
- [GPG Manual](https://www.gnupg.org/documentation/manuals/gnupg/)

---

## 🏗 Project Structure

This project is organized into `backend` (FastAPI) and `frontend` (Next.js) applications.

### Backend (`backend/`)

The backend follows a modular architecture for better maintainability and separation of concerns.

-   **`src/core/`**: Contains core application components.
    -   `config.py`: Application settings and configuration.
    -   `db/`: Database-related files (engine, session, base, models, schemas, crud).
    -   `middleware/`: Custom FastAPI middleware.
    -   `utils/`: Common utilities (logging, request context, performance tracking, event handling).
    -   `websocket/`: WebSocket manager for real-time communication.
-   **`src/modules/`**: Feature-based modules, each encapsulating its own logic.
    -   Each module (e.g., `auth`, `customers`, `jobs`, `twilio`, `dashboard`, `numbers`, `voice_calls`, `phone_stream`, `frontend_ws`, `performance`, `calendar`, `stt`, `tts`) typically contains:
        -   `router.py`: FastAPI endpoints for the module.
        -   `schemas.py`: Pydantic models for data validation and serialization.
        -   `service.py`: Business logic and interactions with other services/CRUDS.
        -   `crud.py`: Database Create, Read, Update, Delete operations specific to the module's models (if applicable).
-   **`src/agent/`**: Components related to the AI agent.
    -   `graph.py`: LangGraph definition.
    -   `models.py`: Agent-specific models.
    -   `nodes.py`: LangGraph nodes.
    -   `tools/`: Custom tools for the agent.
    -   `services/`: Services used by the agent (e.g., `langchain_callbacks.py`, `langchain_service.py`, `langgraph_service.py`).
-   **`src/services/llm_integrations/`**: Abstractions and implementations for various LLM providers.
    -   `base.py`: Base class for LLM services.
    -   `llm_factory.py`: Factory for instantiating LLM services.
    -   `gemini.py`, `openai.py`: Specific LLM implementations.
-   **`src/scripts/`**: Utility scripts (e.g., `download_article.py`, `enable_pgvector.py`).
-   **`main.py`**: Main FastAPI application entry point, registers routers from modules.

### Frontend (`frontend/`)

The frontend is a Next.js application.

-   **`src/app/`**: Next.js App Router structure for pages and layouts.
    -   `(auth)/`: Authentication-related pages (login, register, forgot password).
    -   `(dashboard)/`: Dashboard-related pages (analytics, calls, customers, jobs, numbers).
-   **`src/components/`**: React components.
    -   `ui/`: Shadcn UI components.
    -   `common/`: Reusable common components.
    -   `features/`: Feature-specific components.
-   **`src/contexts/`**: React Context API providers.
-   **`src/lib/`**: Utility functions and helpers.
-   **`src/services/`**: API service calls.
