Script started on 2025-08-21 00:12:52+02:00 [TERM="tmux-256color" TTY="/dev/pts/6" COLUMNS="212" LINES="28"]
[1m[7m%[27m[1m[0m                                                                                                                                                                                                                   
 
k../leanchain/hs\]7;file://pankaj-rog/home/<USER>/Projects/leanchain/hs\
[0m[27m[24m[J[0m[49m[39m[0m[47m[38;5;232m [0m[38;5;232m[47m[47m[38;5;232m [0m[38;5;232m[47m[44m[37m[0m[37m[44m[44m[38;5;254m  [1m[38;5;254m[44m[38;5;255m~[0m[38;5;255m[44m[44m[38;5;254m/Projects/leanchain/[1m[38;5;254m[44m[38;5;255mhs[0m[38;5;255m[44m[44m[38;5;254m[0m[38;5;254m[44m[44m[38;5;254m [0m[38;5;254m[44m[43m[34m[0m[34m[43m[43m[30m  [30m dev [30m!8 [30m?10[0m[30m[43m[43m[30m [0m[30m[43m[49m[33m[0m[33m[49m[39m [0m[49m[39m[K[144C[0m[49m[30m[0m[30m[40m[32m [0m[32m[40m[40m[32m✔[0m[32m[40m[40m[32m [0m[32m[40m[40m[32m[37m[0m[37m[40m[47m[30m 00:12:52[0m[30m[47m[47m[30m [0m[30m[47m[47m[30m [0m[30m[47m[49m[39m[161D[?1h=[?2004hscriptlscript[1Cscript script[?1l>[?2004l

kls\ls: cannot access 'script': No such file or directory
[1m[7m%[27m[1m[0m                                                                                                                                                                                                                   
 
k../leanchain/hs\]7;file://pankaj-rog/home/<USER>/Projects/leanchain/hs\
[0m[27m[24m[J[0m[49m[39m[0m[47m[38;5;232m [0m[38;5;232m[47m[47m[38;5;232m [0m[38;5;232m[47m[44m[37m[0m[37m[44m[44m[38;5;254m  [1m[38;5;254m[44m[38;5;255m~[0m[38;5;255m[44m[44m[38;5;254m/Projects/leanchain/[1m[38;5;254m[44m[38;5;255mhs[0m[38;5;255m[44m[44m[38;5;254m[0m[38;5;254m[44m[44m[38;5;254m [0m[38;5;254m[44m[43m[34m[0m[34m[43m[43m[30m  [30m dev [30m!8 [30m?10[0m[30m[43m[43m[30m [0m[30m[43m[49m[33m[0m[33m[49m[39m [0m[49m[39m[K[142C[0m[49m[31m[0m[31m[41m[33m 2[0m[33m[41m[41m[33m ✘[0m[33m[41m[41m[33m [0m[33m[41m[41m[33m[37m[0m[37m[41m[47m[30m 00:12:55[0m[30m[47m[47m[30m [0m[30m[47m[47m[30m [0m[30m[47m[49m[39m[161D[?1h=[?2004hsscript

[J[0mscript        [Jscriptlive    [Jscriptreplay  [J[01;34mscripts[0m/    [JM[0m[27m[24m[0m[39m[49m
[50Cscript[K[136C[0m[49m[31m[0m[31m[41m[33m 2[0m[33m[41m[41m[33m ✘[0m[33m[41m[41m[33m [0m[33m[41m[41m[33m[37m[0m[37m[41m[47m[30m 00:12:55[0m[30m[47m[47m[30m [0m[30m[47m[47m[30m [0m[30m[47m[49m[39m[0m[39m[49m[155Ds[1m/[0m[0m/dbs[1m/[0m[0m/i 

[JM[62C0

[J[J[01;32m01_init_dbs.sh[0m*     [J[01;32m02_restore_dbs.sh[0m*  [J[01;32m03_dump_dbs.sh[0m*   [JM[0m[27m[24m[0m[39m[49m
[50Cscripts/dbs/0[K[129C[0m[49m[31m[0m[31m[41m[33m 2[0m[33m[41m[41m[33m ✘[0m[33m[41m[41m[33m [0m[33m[41m[41m[33m[37m[0m[37m[41m[47m[30m 00:12:55[0m[30m[47m[47m[30m [0m[30m[47m[47m[30m [0m[30m[47m[49m[39m[0m[39m[49m[148D1_init_dbs.sh

[J[7m01_init_dbs.sh*   [0m  [J[01;32m02_restore_dbs.sh[0m*  [J[01;32m03_dump_dbs.sh[0m*   [JM[0m[27m[24m[0m[39m[49m
[50Cscripts/dbs/01_init_dbs.sh[K[116C[0m[49m[31m[0m[31m[41m[33m 2[0m[33m[41m[41m[33m ✘[0m[33m[41m[41m[33m [0m[33m[41m[41m[33m[37m[0m[37m[41m[47m[30m 00:12:55[0m[30m[47m[47m[30m [0m[30m[47m[47m[30m [0m[30m[47m[49m[39m[0m[39m[49m[135D

[7m01_init_dbs.sh*   [0m  
[7m01_init_dbs.sh*   [0m  
M[0m[27m[24m[0m[39m[49m
[50Cscripts/dbs/01_init_dbs.sh[K[116C[0m[49m[31m[0m[31m[41m[33m 2[0m[33m[41m[41m[33m ✘[0m[33m[41m[41m[33m [0m[33m[41m[41m[33m[37m[0m[37m[41m[47m[30m 00:12:55[0m[30m[47m[47m[30m [0m[30m[47m[47m[30m [0m[30m[47m[49m[39m[0m[39m[49m[135D

[JM[76C[1m [0m[0m [?1l>[?2004l

[Jkscripts/dbs/01_init_dbs.sh\localhost:5432 - accepting connections
Creating databases: homeservice, n8n, and test_db
Database homeservice already exists. Skipping creation.
CREATE EXTENSION
Creating database n8n...
CREATE DATABASE
Creating database test_db...
CREATE DATABASE
[1m[7m%[27m[1m[0m                                                                                                                                                                                                                   
 
k../leanchain/hs\]7;file://pankaj-rog/home/<USER>/Projects/leanchain/hs\
[0m[27m[24m[J[0m[49m[39m[0m[47m[38;5;232m [0m[38;5;232m[47m[47m[38;5;232m [0m[38;5;232m[47m[44m[37m[0m[37m[44m[44m[38;5;254m  [1m[38;5;254m[44m[38;5;255m~[0m[38;5;255m[44m[44m[38;5;254m/Projects/leanchain/[1m[38;5;254m[44m[38;5;255mhs[0m[38;5;255m[44m[44m[38;5;254m[0m[38;5;254m[44m[44m[38;5;254m [0m[38;5;254m[44m[43m[34m[0m[34m[43m[43m[30m  [30m dev [30m!8 [30m?10[0m[30m[43m[43m[30m [0m[30m[43m[49m[33m[0m[33m[49m[39m [0m[49m[39m[K[144C[0m[49m[30m[0m[30m[40m[32m [0m[32m[40m[40m[32m✔[0m[32m[40m[40m[32m [0m[32m[40m[40m[32m[37m[0m[37m[40m[47m[30m 00:13:05[0m[30m[47m[47m[30m [0m[30m[47m[47m[30m [0m[30m[47m[49m[39m[161D[?1h=[?2004hmmake draft[?1l>[?2004l

kmake\--- Creating timestamped zip archive of current work in 'drafts/' ---
--- Creating timestamped zip archive of current work in 'drafts/' ---
Creating drafts/wip-20250821-005834.zip
Including files from submodule: landing
Including files from submodule: backend
Including files from submodule: frontend
	zip warning: name not matched: data/homeservice_data.sql
	zip warning: name not matched: data/homeservice_dump.sql
	zip warning: name not matched: scripts/dump_db.sh
	zip warning: name not matched: scripts/restore_db.sh
	zip warning: name not matched: backend/alembic/versions/11da272034a5_initial_migration.py
	zip warning: name not matched: backend/alembic/versions/7afe0bb9a1e3_refactor_knowledge_items_to_documents.py
	zip warning: name not matched: backend/src/orchestrator/config_loader.py
	zip warning: name not matched: backend/src/orchestrator/configs/default_agent.json
	zip warning: name not matched: frontend/src/app/(app)/test-center/page.tsx
	zip warning: name not matched: frontend/src/components/features/TestCenter/OrderHistoryModal.tsx
  adding: AGENT_BUILDER_COMPLETE.md (deflated 62%)
  adding: AGENT_BUILDER_README.md (deflated 62%)
  adding: IMPLEMENTATION_COMPLETE.md (deflated 68%)
  adding: SETUP_GUIDE.md (deflated 59%)
  adding: data/homeservice_dump_20250820-233345.sql (deflated 44%)
  adding: data/n8n_dump_20250820-233345.sql (deflated 83%)
  adding: data/test_db_dump_20250820-233345.sql (deflated 44%)
  adding: scripts/dbs/01_init_dbs.sh (deflated 54%)
  adding: scripts/dbs/02_restore_dbs.sh (deflated 54%)
  adding: scripts/dbs/03_dump_dbs.sh (deflated 57%)
  adding: scripts/zip-wip.sh (deflated 51%)
  adding: typescript (deflated 82%)
  adding: .git-crypt/.gitattributes (deflated 28%)
  adding: .git-crypt/keys/default/0/E556646840791A0F16DA342FAD4428D8EFF2A8F4.gpg (stored 0%)
  adding: .gitattributes (deflated 50%)
  adding: .gitignore (deflated 59%)
  adding: .gitmodules (deflated 59%)
  adding: .pre-commit-config.yaml (deflated 50%)
  adding: ERROR_FIXES_SUMMARY.md (deflated 65%)
  adding: Makefile (deflated 63%)
  adding: PROJECT_PLAN.md (deflated 62%)
  adding: README.md (deflated 56%)
  adding: backend/ (stored 0%)
  adding: docker-compose.yml (deflated 64%)
  adding: download_images.sh (deflated 79%)
  adding: frontend/ (stored 0%)
  adding: landing/ (stored 0%)
  adding: package.json (deflated 17%)
  adding: scripts/cleanup_test_dbs.sh (deflated 45%)
  adding: scripts/git-commit-all.sh (deflated 60%)
  adding: scripts/git-pull-all.sh (deflated 53%)
  adding: scripts/git-push-all.sh (deflated 54%)
  adding: scripts/git-status-all.sh (deflated 45%)
  adding: landing/.env (deflated 2%)
  adding: landing/.gitattributes (deflated 50%)
  adding: landing/.gitignore (deflated 38%)
  adding: landing/.vite/deps_temp_c7b1639e/package.json (stored 0%)
  adding: landing/Dockerfile (deflated 40%)
  adding: landing/Dockerfile.dev (deflated 28%)
  adding: landing/HERO_IMAGES_SUMMARY.md (deflated 64%)
  adding: landing/README.md (deflated 52%)
  adding: landing/babel.config.cjs (deflated 31%)
  adding: landing/components.json (deflated 49%)
  adding: landing/download-images.js (deflated 71%)
  adding: landing/eslint.config.js (deflated 55%)
  adding: landing/index.html (deflated 57%)
  adding: landing/jest.config.cjs (deflated 23%)
  adding: landing/nginx.conf (deflated 39%)
  adding: landing/package-lock.json (deflated 79%)
  adding: landing/package.json (deflated 69%)
  adding: landing/postcss.config.js (deflated 18%)
  adding: landing/public/ScreenShot Tool -20250811235628.png (deflated 0%)
  adding: landing/public/commercial-cleaning-hero.svg (deflated 77%)
  adding: landing/public/commercial-cleaning-icon.svg (deflated 60%)
  adding: landing/public/communication-icon.svg (deflated 71%)
  adding: landing/public/crm-icon.svg (deflated 63%)
  adding: landing/public/electrical-hero.svg (deflated 74%)
  adding: landing/public/electrical-icon.svg (deflated 36%)
  adding: landing/public/email-marketing-icon.svg (deflated 53%)
  adding: landing/public/favicon-white.svg (deflated 18%)
  adding: landing/public/favicon.svg (deflated 29%)
  adding: landing/public/hvac-hero.svg (deflated 69%)
  adding: landing/public/hvac-icon.svg (deflated 57%)
  adding: landing/public/images/hero/commercialCleaning/commercialCleaning1.jpeg (deflated 3%)
  adding: landing/public/images/hero/commercialCleaning/commercialCleaning2.jpeg (deflated 2%)
  adding: landing/public/images/hero/commercialCleaning/commercialCleaning3.jpeg (deflated 1%)
  adding: landing/public/images/hero/commercialCleaning/commercialCleaning4.jpeg (deflated 3%)
  adding: landing/public/images/hero/electrical/electrical1.jpeg (deflated 1%)
  adding: landing/public/images/hero/electrical/electrical2.jpeg (deflated 1%)
  adding: landing/public/images/hero/electrical/electrical3.jpeg (deflated 0%)
  adding: landing/public/images/hero/hvac/hvac1.jpeg (deflated 2%)
  adding: landing/public/images/hero/hvac/hvac2.jpeg (deflated 2%)
  adding: landing/public/images/hero/hvac/hvac3.jpeg (deflated 2%)
  adding: landing/public/images/hero/landscaping/landscaping1.jpeg (deflated 1%)
  adding: landing/public/images/hero/landscaping/landscaping2.jpeg (deflated 1%)
  adding: landing/public/images/hero/landscaping/landscaping3.jpeg (deflated 1%)
  adding: landing/public/images/hero/pestControl/pestControl1.jpeg (deflated 2%)
  adding: landing/public/images/hero/pestControl/pestControl2.jpeg (deflated 2%)
  adding: landing/public/images/hero/pestControl/pestControl3.jpeg (deflated 2%)
  adding: landing/public/images/hero/plumbing/plumbing1.jpeg (deflated 0%)
  adding: landing/public/images/hero/plumbing/plumbing2.jpeg (deflated 2%)
  adding: landing/public/images/hero/plumbing/plumbing3.jpeg (deflated 1%)
  adding: landing/public/images/hero/roofing/roofing1.jpeg (deflated 2%)
  adding: landing/public/images/hero/roofing/roofing2.jpeg (deflated 4%)
  adding: landing/public/images/hero/roofing/roofing3.jpeg (deflated 5%)
  adding: landing/public/images/hero/solar/solar1.jpeg (deflated 0%)
  adding: landing/public/images/hero/solar/solar2.jpeg (deflated 1%)
  adding: landing/public/images/hero/solar/solar3.jpeg (deflated 1%)
  adding: landing/public/images/hero/windowCleaning/windowCleaning1.jpeg (deflated 1%)
  adding: landing/public/images/hero/windowCleaning/windowCleaning2.jpeg (deflated 1%)
  adding: landing/public/images/hero/windowCleaning/windowCleaning3.jpeg (deflated 2%)
  adding: landing/public/images/team/bart-rosier.jpeg (deflated 0%)
  adding: landing/public/images/team/pankaj-kumar.jpeg (deflated 0%)
  adding: landing/public/landscaping-hero.svg (deflated 70%)
  adding: landing/public/landscaping-icon.svg (deflated 61%)
  adding: landing/public/pest-control-hero.svg (deflated 72%)
  adding: landing/public/pest-control-icon.svg (deflated 55%)
  adding: landing/public/placeholder.svg (deflated 72%)
  adding: landing/public/plumbing-hero.svg (deflated 72%)
  adding: landing/public/plumbing-icon.svg (deflated 56%)
  adding: landing/public/privacy/terms-europe.md (deflated 68%)
  adding: landing/public/resources/ai-answering-and-scheduling-assistants-for-home-service-leads.md (deflated 58%)
  adding: landing/public/resources/ai-in-the-contact-center-the-future-of-customer-service.md (deflated 60%)
  adding: landing/public/resources/ai-voice-technology-benefits-and-challenges.md (deflated 59%)
  adding: landing/public/resources/autonomous-repair-solutions-the-future-of-home-services.md (deflated 56%)
  adding: landing/public/resources/how-ai-call-center-platforms-revolutionize-home-services.md (deflated 57%)
  adding: landing/public/resources/how-ai-is-revolutionizing-the-home-services-industry.md (deflated 57%)
  adding: landing/public/resources/how-voice-ai-assistants-can-help-aging-adults-live-independently.md (deflated 48%)
  adding: landing/public/resources/the-benefits-of-voice-ai-for-smart-home-automation.md (deflated 58%)
  adding: landing/public/resources/the-future-of-home-services-how-ai-is-changing-the-game.md (deflated 58%)
  adding: landing/public/resources/the-impact-of-ai-driven-voice-assistants-on-smart-home-ecosystems.md (deflated 56%)
  adding: landing/public/robots.txt (deflated 54%)
  adding: landing/public/roofing-hero-real.jpg (stored 0%)
  adding: landing/public/roofing-hero.svg (deflated 71%)
  adding: landing/public/roofing-icon.png (deflated 4%)
  adding: landing/public/roofing-icon.svg (deflated 39%)
  adding: landing/public/solar-hero.svg (deflated 72%)
  adding: landing/public/solar-icon.svg (deflated 53%)
  adding: landing/public/test-image.jpg (stored 0%)
  adding: landing/public/testimonial-1-men.jpg (deflated 9%)
  adding: landing/public/testimonial-2-women.jpg (deflated 6%)
  adding: landing/public/testimonial-3-men.jpg (deflated 6%)
  adding: landing/public/testimonial-4-women.jpg (deflated 1%)
  adding: landing/public/testimonial-5-men.jpg (deflated 10%)
  adding: landing/public/testimonial-6-women.jpg (deflated 1%)
  adding: landing/public/window-cleaning-hero.svg (deflated 79%)
  adding: landing/public/window-cleaning-icon.svg (deflated 57%)
  adding: landing/src/AnimatedRoutes.tsx (deflated 86%)
  adding: landing/src/App.css (deflated 47%)
  adding: landing/src/App.tsx (deflated 61%)
  adding: landing/src/assets/company-logos.jpg (deflated 16%)
  adding: landing/src/assets/hero-image.jpg (deflated 3%)
  adding: landing/src/assets/roofer-hero.jpg (deflated 2%)
  adding: landing/src/components/ConversionSection.tsx (deflated 76%)
  adding: landing/src/components/DemoCallsSection.tsx (deflated 71%)
  adding: landing/src/components/FiesonLogo.tsx (deflated 41%)
  adding: landing/src/components/Footer.tsx (deflated 77%)
  adding: landing/src/components/Header.tsx (deflated 61%)
  adding: landing/src/components/Hero.tsx (deflated 71%)
  adding: landing/src/components/HowItWorks.tsx (deflated 61%)
  adding: landing/src/components/LeadGenSection.tsx (deflated 77%)
  adding: landing/src/components/Testimonials.tsx (deflated 71%)
  adding: landing/src/components/UseCases.tsx (deflated 69%)
  adding: landing/src/components/ui/accordion.tsx (deflated 68%)
  adding: landing/src/components/ui/alert-dialog.tsx (deflated 77%)
  adding: landing/src/components/ui/alert.tsx (deflated 62%)
  adding: landing/src/components/ui/aspect-ratio.tsx (deflated 34%)
  adding: landing/src/components/ui/avatar.tsx (deflated 71%)
  adding: landing/src/components/ui/badge.tsx (deflated 56%)
  adding: landing/src/components/ui/breadcrumb.tsx (deflated 68%)
  adding: landing/src/components/ui/button.tsx (deflated 65%)
  adding: landing/src/components/ui/calendar.tsx (deflated 64%)
  adding: landing/src/components/ui/card.tsx (deflated 74%)
  adding: landing/src/components/ui/carousel.tsx (deflated 73%)
  adding: landing/src/components/ui/chart.tsx (deflated 73%)
  adding: landing/src/components/ui/checkbox.tsx (deflated 55%)
  adding: landing/src/components/ui/collapsible.tsx (deflated 61%)
  adding: landing/src/components/ui/command.tsx (deflated 75%)
  adding: landing/src/components/ui/context-menu.tsx (deflated 82%)
  adding: landing/src/components/ui/dialog.tsx (deflated 72%)
  adding: landing/src/components/ui/drawer.tsx (deflated 73%)
  adding: landing/src/components/ui/dropdown-menu.tsx (deflated 81%)
  adding: landing/src/components/ui/f-icon.tsx (deflated 33%)
  adding: landing/src/components/ui/form.tsx (deflated 73%)
  adding: landing/src/components/ui/hover-card.tsx (deflated 59%)
  adding: landing/src/components/ui/input-otp.tsx (deflated 65%)
  adding: landing/src/components/ui/input.tsx (deflated 48%)
  adding: landing/src/components/ui/label.tsx (deflated 50%)
  adding: landing/src/components/ui/menubar.tsx (deflated 82%)
  adding: landing/src/components/ui/navigation-menu.tsx (deflated 76%)
  adding: landing/src/components/ui/pagination.tsx (deflated 70%)
  adding: landing/src/components/ui/popover.tsx (deflated 60%)
  adding: landing/src/components/ui/progress.tsx (deflated 50%)
  adding: landing/src/components/ui/radio-group.tsx (deflated 64%)
  adding: landing/src/components/ui/resizable.tsx (deflated 67%)
  adding: landing/src/components/ui/scroll-area.tsx (deflated 67%)
  adding: landing/src/components/ui/select.tsx (deflated 76%)
  adding: landing/src/components/ui/separator.tsx (deflated 54%)
  adding: landing/src/components/ui/sheet.tsx (deflated 72%)
  adding: landing/src/components/ui/sidebar.tsx (deflated 79%)
  adding: landing/src/components/ui/skeleton.tsx (deflated 27%)
  adding: landing/src/components/ui/slider.tsx (deflated 56%)
  adding: landing/src/components/ui/sonner.tsx (deflated 60%)
  adding: landing/src/components/ui/switch.tsx (deflated 55%)
  adding: landing/src/components/ui/table.tsx (deflated 76%)
  adding: landing/src/components/ui/tabs.tsx (deflated 70%)
  adding: landing/src/components/ui/textarea.tsx (deflated 48%)
  adding: landing/src/components/ui/toast.tsx (deflated 73%)
  adding: landing/src/components/ui/toaster.tsx (deflated 59%)
  adding: landing/src/components/ui/toggle-group.tsx (deflated 68%)
  adding: landing/src/components/ui/toggle.tsx (deflated 57%)
  adding: landing/src/components/ui/tooltip.tsx (deflated 58%)
  adding: landing/src/components/ui/use-toast.ts (deflated 33%)
  adding: landing/src/data/industryContent.ts (deflated 78%)
  adding: landing/src/hooks/use-mobile.tsx (deflated 48%)
  adding: landing/src/hooks/use-toast.ts (deflated 67%)
  adding: landing/src/i18n.ts (deflated 53%)
  adding: landing/src/index.css (deflated 70%)
  adding: landing/src/lib/utils.ts (deflated 27%)
  adding: landing/src/main.tsx (deflated 28%)
  adding: landing/src/pages/About.tsx (deflated 69%)
  adding: landing/src/pages/Contact.tsx (deflated 66%)
  adding: landing/src/pages/Index.tsx (deflated 64%)
  adding: landing/src/pages/Industries.tsx (deflated 69%)
  adding: landing/src/pages/IndustryPage.tsx (deflated 76%)
  adding: landing/src/pages/Integrations.tsx (deflated 69%)
  adding: landing/src/pages/MarkdownPage.tsx (deflated 67%)
  adding: landing/src/pages/NotFound.tsx (deflated 48%)
  adding: landing/src/pages/Pricing.tsx (deflated 76%)
  adding: landing/src/pages/Resources.tsx (deflated 68%)
  adding: landing/src/vite-env.d.ts (stored 0%)
  adding: landing/tailwind.config.ts (deflated 71%)
  adding: landing/tsconfig.app.json (deflated 48%)
  adding: landing/tsconfig.json (deflated 46%)
  adding: landing/tsconfig.node.json (deflated 45%)
  adding: landing/vite.config.ts (deflated 40%)
  adding: backend/alembic/versions/11687ed232b6_initial_migration.py (deflated 87%)
  adding: backend/alembic/versions/6607e10aa23e_add_description_to_agent_model.py (deflated 52%)
  adding: backend/src/core/middleware/request_logging_middleware.py (deflated 59%)
  adding: backend/src/core/middleware/websocket_logging_decorator.py (deflated 66%)
  adding: backend/src/modules/templates/__init__.py (deflated 18%)
  adding: backend/src/modules/templates/crud.py (deflated 70%)
  adding: backend/src/modules/templates/router.py (deflated 66%)
  adding: backend/src/modules/templates/schemas.py (deflated 65%)
  adding: backend/src/modules/templates/service.py (deflated 12%)
  adding: backend/src/orchestrator/api/agent_builder.py (deflated 78%)
  adding: backend/src/orchestrator/api/node_management.py (deflated 80%)
  adding: backend/src/orchestrator/api/templates.py (deflated 81%)
  adding: backend/src/orchestrator/nodes/n8n_node.py (deflated 81%)
  adding: backend/src/orchestrator/services/n8n_service.py (deflated 81%)
  adding: backend/src/orchestrator/services/prompt_optimization.py (deflated 77%)
  adding: backend/src/orchestrator/template_loader.py (deflated 52%)
  adding: backend/src/orchestrator/templates/default_agent.json (deflated 68%)
  adding: backend/src/orchestrator/templates/multi_llm_agent.json (deflated 71%)
  adding: backend/src/orchestrator/templates/simple_agent.json (deflated 64%)
  adding: backend/.dockerignore (stored 0%)
  adding: backend/.env (deflated 33%)
  adding: backend/.env.example (deflated 40%)
  adding: backend/.env.test (deflated 33%)
  adding: backend/.gitattributes (deflated 50%)
  adding: backend/.gitignore (deflated 59%)
  adding: backend/.pre-commit-config.yaml (deflated 57%)
  adding: backend/Dockerfile (deflated 46%)
  adding: backend/IMPLEMENTATION_SUMMARY.md (deflated 66%)
  adding: backend/README.md (deflated 44%)
  adding: backend/alembic.ini (deflated 55%)
  adding: backend/alembic/README (stored 0%)
  adding: backend/alembic/env.py (deflated 63%)
  adding: backend/alembic/script.py.mako (deflated 53%)
  adding: backend/docs/AGENT_ORCHESTRATION.md (deflated 64%)
  adding: backend/docs/schemas/agent_config_schema.json (deflated 83%)
  adding: backend/generate_tests.py (deflated 72%)
  adding: backend/mypy.ini (deflated 35%)
  adding: backend/pitch.html (deflated 77%)
  adding: backend/pyproject.toml (deflated 53%)
  adding: backend/pytest.ini (deflated 7%)
  adding: backend/src/__init__.py (stored 0%)
  adding: backend/src/core/config.py (deflated 52%)
  adding: backend/src/core/db/database.py (deflated 57%)
  adding: backend/src/core/guardrails/user_intent.xml (deflated 28%)
  adding: backend/src/core/middleware/request_context_middleware.py (deflated 57%)
  adding: backend/src/core/performance_tracker.py (deflated 55%)
  adding: backend/src/core/services/conversation_manager.py (deflated 72%)
  adding: backend/src/core/utils/__init__.py (stored 0%)
  adding: backend/src/core/utils/events.py (deflated 53%)
  adding: backend/src/core/utils/helpers.py (deflated 69%)
  adding: backend/src/core/utils/logging.py (deflated 53%)
  adding: backend/src/core/utils/performance_tracker.py (deflated 66%)
  adding: backend/src/core/utils/request_context.py (deflated 31%)
  adding: backend/src/core/websocket/connection_manager.py (deflated 72%)
  adding: backend/src/main.py (deflated 69%)
  adding: backend/src/modules/agents/crud.py (deflated 66%)
  adding: backend/src/modules/agents/models.py (deflated 54%)
  adding: backend/src/modules/agents/router.py (deflated 72%)
  adding: backend/src/modules/agents/schemas.py (deflated 52%)
  adding: backend/src/modules/auth/router.py (deflated 66%)
  adding: backend/src/modules/auth/schemas.py (deflated 58%)
  adding: backend/src/modules/auth/service.py (deflated 65%)
  adding: backend/src/modules/calendar/service.py (deflated 56%)
  adding: backend/src/modules/call/crud.py (deflated 75%)
  adding: backend/src/modules/call/models.py (deflated 70%)
  adding: backend/src/modules/call/router.py (deflated 74%)
  adding: backend/src/modules/call/schemas.py (deflated 71%)
  adding: backend/src/modules/call/service.py (deflated 81%)
  adding: backend/src/modules/customers/crud.py (deflated 71%)
  adding: backend/src/modules/customers/models.py (deflated 63%)
  adding: backend/src/modules/customers/router.py (deflated 72%)
  adding: backend/src/modules/customers/schemas.py (deflated 58%)
  adding: backend/src/modules/customers/tools.py (deflated 55%)
  adding: backend/src/modules/dashboard/router.py (deflated 63%)
  adding: backend/src/modules/dashboard/schemas.py (deflated 66%)
  adding: backend/src/modules/dashboard/service.py (deflated 69%)
  adding: backend/src/modules/documents/crud.py (deflated 58%)
  adding: backend/src/modules/documents/models.py (deflated 67%)
  adding: backend/src/modules/jobs/crud.py (deflated 69%)
  adding: backend/src/modules/jobs/models.py (deflated 57%)
  adding: backend/src/modules/jobs/router.py (deflated 70%)
  adding: backend/src/modules/jobs/schemas.py (deflated 52%)
  adding: backend/src/modules/performance/crud.py (deflated 64%)
  adding: backend/src/modules/performance/models.py (deflated 56%)
  adding: backend/src/modules/performance/router.py (deflated 59%)
  adding: backend/src/modules/performance/schemas.py (deflated 51%)
  adding: backend/src/modules/test_center/__init__.py (stored 0%)
  adding: backend/src/modules/test_center/models.py (deflated 54%)
  adding: backend/src/modules/test_center/router.py (deflated 70%)
  adding: backend/src/modules/test_center/schemas.py (deflated 66%)
  adding: backend/src/modules/test_center/service.py (deflated 73%)
  adding: backend/src/modules/users/models.py (deflated 58%)
  adding: backend/src/modules/users/schemas.py (deflated 44%)
  adding: backend/src/orchestrator/__init__.py (stored 0%)
  adding: backend/src/orchestrator/dspy/modules.py (deflated 75%)
  adding: backend/src/orchestrator/dspy/signatures.py (deflated 32%)
  adding: backend/src/orchestrator/graph.py (deflated 79%)
  adding: backend/src/orchestrator/models.py (deflated 67%)
  adding: backend/src/orchestrator/nodes/__init__.py (stored 0%)
  adding: backend/src/orchestrator/nodes/llm.py (deflated 75%)
  adding: backend/src/orchestrator/nodes/rag.py (deflated 61%)
  adding: backend/src/orchestrator/nodes/tools.py (deflated 66%)
  adding: backend/src/orchestrator/router.py (deflated 66%)
  adding: backend/src/orchestrator/schemas.py (deflated 73%)
  adding: backend/src/orchestrator/services/callbacks.py (deflated 75%)
  adding: backend/src/orchestrator/services/knowledge/rag_service.py (deflated 60%)
  adding: backend/src/orchestrator/services/llm_integrations/__init__.py (stored 0%)
  adding: backend/src/orchestrator/services/llm_integrations/embedding_factory.py (deflated 69%)
  adding: backend/src/orchestrator/services/llm_integrations/llm_factory.py (deflated 74%)
  adding: backend/src/orchestrator/services/memory/mem0_service.py (deflated 75%)
  adding: backend/src/orchestrator/services/orchestration_service.py (deflated 68%)
  adding: backend/src/orchestrator/services/stt/__init__.py (deflated 52%)
  adding: backend/src/orchestrator/services/stt/assemblyai.py (deflated 65%)
  adding: backend/src/orchestrator/services/stt/base.py (deflated 54%)
  adding: backend/src/orchestrator/services/stt/nova.py (deflated 65%)
  adding: backend/src/orchestrator/services/tts/__init__.py (deflated 45%)
  adding: backend/src/orchestrator/services/tts/base.py (deflated 70%)
  adding: backend/src/orchestrator/services/tts/elevenlabs.py (deflated 68%)
  adding: backend/src/orchestrator/services/tts/nova.py (deflated 75%)
  adding: backend/src/orchestrator/services/websocket/io.py (deflated 63%)
  adding: backend/src/orchestrator/tools.py (deflated 54%)
  adding: backend/src/orchestrator/tools/__init__.py (stored 0%)
  adding: backend/src/orchestrator/tools/langchain_tools.py (deflated 61%)
  adding: backend/src/orchestrator/tools/registry.py (deflated 50%)
  adding: backend/src/scripts/enable_pgvector.py (deflated 52%)
  adding: backend/src/scripts/seed_templates.py (deflated 64%)
  adding: backend/test_runner.py (deflated 67%)
  adding: backend/tests/conftest.py (deflated 64%)
  adding: backend/tests/e2e/test_e2e_call.py (deflated 81%)
  adding: backend/tests/e2e/test_e2e_phone_calls.py (deflated 78%)
  adding: backend/tests/e2e/test_e2e_web_calls.py (deflated 84%)
  adding: backend/tests/integration/__init__.py (stored 0%)
  adding: backend/tests/integration/test_agent_graph.py (deflated 81%)
  adding: backend/tests/modules/call/test_call.py (deflated 69%)
  adding: backend/tests/test_customers.py (deflated 64%)
  adding: backend/tests/test_main.py (deflated 60%)
  adding: backend/tests/utils/__init__.py (stored 0%)
  adding: backend/tests/utils/test_helpers.py (deflated 71%)
  adding: backend/uv.lock (deflated 69%)
  adding: frontend/src/app/(app)/agents/builder/page.tsx (deflated 80%)
  adding: frontend/src/app/(app)/customers/builder/page.tsx (deflated 76%)
  adding: frontend/src/app/(app)/node/page.tsx (deflated 78%)
  adding: frontend/src/components/features/AgentBuilder/AgentAnalytics.tsx (deflated 78%)
  adding: frontend/src/components/features/AgentBuilder/AgentCanvas.tsx (deflated 74%)
  adding: frontend/src/components/features/AgentBuilder/AgentDeployment.tsx (deflated 82%)
  adding: frontend/src/components/features/AgentBuilder/ChatInterface.tsx (deflated 74%)
  adding: frontend/src/components/features/AgentBuilder/DSPyConfigEditor.tsx (deflated 82%)
  adding: frontend/src/components/features/AgentBuilder/IntegrationTesting.tsx (deflated 81%)
  adding: frontend/src/components/features/AgentBuilder/MemoryConfigEditor.tsx (deflated 85%)
  adding: frontend/src/components/features/AgentBuilder/NodeEditor.tsx (deflated 81%)
  adding: frontend/src/components/features/AgentBuilder/NodePalette.tsx (deflated 74%)
  adding: frontend/src/components/features/AgentBuilder/TemplateBrowser.tsx (deflated 76%)
  adding: frontend/src/components/features/AgentBuilder/TemplateManager.tsx (deflated 76%)
  adding: frontend/src/components/features/AgentBuilder/WorkflowCanvas.tsx (deflated 72%)
  adding: frontend/src/components/features/NodeTester/NodeTester.tsx (deflated 76%)
  adding: frontend/src/components/features/TestCenter/AgentConfig.tsx (deflated 81%)
  adding: frontend/src/components/features/TestCenter/ToolEditor.tsx (deflated 56%)
  adding: frontend/src/components/features/TestCenter/ToolManager.tsx (deflated 79%)
  adding: frontend/src/components/ui/JsonViewerModal.tsx (deflated 52%)
  adding: frontend/src/components/ui/scroll-area.tsx (deflated 67%)
  adding: frontend/src/data/electrical-agent-template.json (deflated 66%)
  adding: frontend/src/data/example-agents.ts (deflated 68%)
  adding: frontend/src/data/hvac-agent-template.json (deflated 65%)
  adding: frontend/src/data/plumbing-agent-template.json (deflated 65%)
  adding: frontend/src/data/simple-agent-template.json (deflated 64%)
  adding: frontend/src/services/agent-builder/agentBuilderService.ts (deflated 75%)
  adding: frontend/src/services/api/apiClient.ts (deflated 75%)
  adding: frontend/src/services/config/configService.ts (deflated 78%)
  adding: frontend/src/services/error/errorHandler.ts (deflated 74%)
  adding: frontend/src/services/index.ts (deflated 70%)
  adding: frontend/src/services/templates/templateService.ts (deflated 78%)
  adding: frontend/src/services/testing/testingService.ts (deflated 77%)
  adding: frontend/src/services/websocket/websocketService.ts (deflated 75%)
  adding: frontend/.dockerignore (stored 0%)
  adding: frontend/.env (deflated 23%)
  adding: frontend/.gitattributes (deflated 50%)
  adding: frontend/.gitignore (deflated 55%)
  adding: frontend/.pre-commit-config.yaml (deflated 65%)
  adding: frontend/.prettierrc.json (deflated 26%)
  adding: frontend/Dockerfile (deflated 49%)
  adding: frontend/Dockerfile.dev (deflated 23%)
  adding: frontend/FRONTEND_IMPLEMENTATION_SUMMARY.md (deflated 66%)
  adding: frontend/README.md (deflated 51%)
  adding: frontend/components.json (deflated 44%)
  adding: frontend/eslint.config.mjs (deflated 44%)
  adding: frontend/next-env.d.ts (deflated 33%)
  adding: frontend/next.config.ts (deflated 28%)
  adding: frontend/nginx.conf (deflated 39%)
  adding: frontend/package-lock.json (deflated 79%)
  adding: frontend/package.json (deflated 65%)
  adding: frontend/postcss.config.mjs (deflated 7%)
  adding: frontend/public/recorderProcessor.js (deflated 47%)
  adding: frontend/src/app/(app)/agents/[agentId]/page.tsx (deflated 73%)
  adding: frontend/src/app/(app)/agents/new/page.tsx (deflated 69%)
  adding: frontend/src/app/(app)/agents/page.tsx (deflated 74%)
  adding: frontend/src/app/(app)/agents/templates/page.tsx (deflated 72%)
  adding: frontend/src/app/(app)/analytics/page.tsx (deflated 45%)
  adding: frontend/src/app/(app)/calls/page.tsx (deflated 44%)
  adding: frontend/src/app/(app)/customers/page.tsx (deflated 71%)
  adding: frontend/src/app/(app)/dashboard/page.tsx (deflated 80%)
  adding: frontend/src/app/(app)/jobs/page.tsx (deflated 68%)
  adding: frontend/src/app/(app)/layout.tsx (deflated 56%)
  adding: frontend/src/app/(app)/numbers/page.tsx (deflated 72%)
  adding: frontend/src/app/(auth)/forgot-password/page.tsx (deflated 63%)
  adding: frontend/src/app/(auth)/layout.tsx (deflated 25%)
  adding: frontend/src/app/(auth)/login/page.tsx (deflated 69%)
  adding: frontend/src/app/(auth)/register/page.tsx (deflated 71%)
  adding: frontend/src/app/(auth)/reset-password/ResetPasswordForm.tsx (deflated 67%)
  adding: frontend/src/app/(auth)/reset-password/page.tsx (deflated 41%)
  adding: frontend/src/app/(marketing)/layout.tsx (deflated 52%)
  adding: frontend/src/app/(marketing)/page.tsx (deflated 79%)
  adding: frontend/src/app/favicon.ico (deflated 64%)
  adding: frontend/src/app/globals.css (deflated 72%)
  adding: frontend/src/app/layout.tsx (deflated 57%)
  adding: frontend/src/app/not-found.tsx (deflated 49%)
  adding: frontend/src/components/common/Header.tsx (deflated 75%)
  adding: frontend/src/components/common/Navbar.tsx (deflated 75%)
  adding: frontend/src/components/common/Sidebar.tsx (deflated 74%)
  adding: frontend/src/components/features/TestCenter/AgentSelector.tsx (deflated 75%)
  adding: frontend/src/components/features/TestCenter/ConversationLog.tsx (deflated 69%)
  adding: frontend/src/components/features/TestCenter/CustomerDetails.tsx (deflated 64%)
  adding: frontend/src/components/features/TestCenter/CustomerSelector.tsx (deflated 67%)
  adding: frontend/src/components/features/TestCenter/Insights.tsx (deflated 72%)
  adding: frontend/src/components/features/TestCenter/LivePerformanceChart.tsx (deflated 60%)
  adding: frontend/src/components/features/TestCenter/LiveTranscript.tsx (deflated 70%)
  adding: frontend/src/components/features/TestCenter/WebCallWidget.tsx (deflated 67%)
  adding: frontend/src/components/features/agent-editor/AgentConfigurationPanel.tsx (deflated 83%)
  adding: frontend/src/components/features/agent-editor/AgentEditorFlow.tsx (deflated 61%)
  adding: frontend/src/components/features/agent-editor/CreateAgentDialog.tsx (deflated 66%)
  adding: frontend/src/components/features/agent-editor/EditorHeader.tsx (deflated 53%)
  adding: frontend/src/components/features/agent-editor/JsonViewPanel.tsx (deflated 54%)
  adding: frontend/src/components/features/agent-editor/NodeTestPanel.tsx (deflated 61%)
  adding: frontend/src/components/features/agent-editor/NodesSidebar.tsx (deflated 57%)
  adding: frontend/src/components/features/agent-editor/PropertiesPanel.tsx (deflated 75%)
  adding: frontend/src/components/features/auth/ProtectedRoute.tsx (deflated 48%)
  adding: frontend/src/components/ui/avatar.tsx (deflated 67%)
  adding: frontend/src/components/ui/badge.tsx (deflated 56%)
  adding: frontend/src/components/ui/button.tsx (deflated 59%)
  adding: frontend/src/components/ui/card.tsx (deflated 74%)
  adding: frontend/src/components/ui/command.tsx (deflated 75%)
  adding: frontend/src/components/ui/dialog.tsx (deflated 71%)
  adding: frontend/src/components/ui/dropdown-menu.tsx (deflated 81%)
  adding: frontend/src/components/ui/input.tsx (deflated 47%)
  adding: frontend/src/components/ui/label.tsx (deflated 50%)
  adding: frontend/src/components/ui/navigation-menu.tsx (deflated 77%)
  adding: frontend/src/components/ui/popover.tsx (deflated 59%)
  adding: frontend/src/components/ui/resizable.tsx (deflated 45%)
  adding: frontend/src/components/ui/select.tsx (deflated 74%)
  adding: frontend/src/components/ui/separator.tsx (deflated 54%)
  adding: frontend/src/components/ui/sheet.tsx (deflated 74%)
  adding: frontend/src/components/ui/slider.tsx (deflated 55%)
  adding: frontend/src/components/ui/switch.tsx (deflated 55%)
  adding: frontend/src/components/ui/table.tsx (deflated 73%)
  adding: frontend/src/components/ui/tabs.tsx (deflated 70%)
  adding: frontend/src/components/ui/textarea.tsx (deflated 45%)
  adding: frontend/src/components/ui/tooltip.tsx (deflated 66%)
  adding: frontend/src/contexts/AuthContext.tsx (deflated 66%)
  adding: frontend/src/contexts/AuthContextObject.ts (deflated 52%)
  adding: frontend/src/contexts/ScrollContext.tsx (deflated 64%)
  adding: frontend/src/data/agent_config.json (deflated 75%)
  adding: frontend/src/lib/agent-utils.ts (deflated 71%)
  adding: frontend/src/lib/api.ts (deflated 72%)
  adding: frontend/src/lib/utils.ts (deflated 39%)
  adding: frontend/src/services/agent-editor/agentEditorService.ts (deflated 40%)
  adding: frontend/src/services/agents/agentService.ts (deflated 76%)
  adding: frontend/src/services/auth/authService.ts (deflated 65%)
  adding: frontend/src/services/calls/callService.ts (deflated 71%)
  adding: frontend/src/services/customers/customersService.ts (deflated 69%)
  adding: frontend/src/services/jobs/jobsService.ts (deflated 66%)
  adding: frontend/src/types/index.ts (deflated 73%)
  adding: frontend/tailwind.config.ts (deflated 75%)
  adding: frontend/tsconfig.json (deflated 50%)
✅ Created drafts/wip-20250821-005834.zip
[1m[7m%[27m[1m[0m                                                                                                                                                                                                                   
 
k../leanchain/hs\]7;file://pankaj-rog/home/<USER>/Projects/leanchain/hs\
[0m[27m[24m[J[0m[49m[39m[0m[47m[38;5;232m [0m[38;5;232m[47m[47m[38;5;232m [0m[38;5;232m[47m[44m[37m[0m[37m[44m[44m[38;5;254m  [1m[38;5;254m[44m[38;5;255m~[0m[38;5;255m[44m[44m[38;5;254m/Projects/leanchain/[1m[38;5;254m[44m[38;5;255mhs[0m[38;5;255m[44m[44m[38;5;254m[0m[38;5;254m[44m[44m[38;5;254m [0m[38;5;254m[44m[43m[34m[0m[34m[43m[43m[30m  [30m dev [30m!8 [30m?10[0m[30m[43m[43m[30m [0m[30m[43m[49m[33m[0m[33m[49m[39m [0m[49m[39m[K[144C[0m[49m[30m[0m[30m[40m[32m [0m[32m[40m[40m[32m✔[0m[32m[40m[40m[32m [0m[32m[40m[40m[32m[37m[0m[37m[40m[47m[30m 00:58:35[0m[30m[47m[47m[30m [0m[30m[47m[47m[30m [0m[30m[47m[49m[39m[161D[?1h=[?2004hccd frontend[1m/[0m[0m [?1l>[?2004l

kcd\[1m[7m%[27m[1m[0m                                                                                                                                                                                                                   
 
k..n/hs/frontend\]7;file://pankaj-rog/home/<USER>/Projects/leanchain/hs/frontend\
[0m[27m[24m[J[0m[49m[39m[0m[47m[38;5;232m [0m[38;5;232m[47m[47m[38;5;232m [0m[38;5;232m[47m[44m[37m[0m[37m[44m[44m[38;5;254m  [1m[38;5;254m[44m[38;5;255m~[0m[38;5;255m[44m[44m[38;5;254m/Projects/leanchain/[1m[38;5;254m[44m[38;5;255mhs[0m[38;5;255m[44m[44m[38;5;254m/[1m[38;5;254m[44m[38;5;255mfrontend[0m[38;5;255m[44m[44m[38;5;254m[0m[38;5;254m[44m[44m[38;5;254m [0m[38;5;254m[44m[43m[34m[0m[34m[43m[43m[30m  [30m dev [30m!24 [30m?24[0m[30m[43m[43m[30m [0m[30m[43m[49m[33m[0m[33m[49m[39m [0m[49m[39m[K[134C[0m[49m[30m[0m[30m[40m[32m [0m[32m[40m[40m[32m✔[0m[32m[40m[40m[32m [0m[32m[40m[40m[32m[37m[0m[37m[40m[47m[30m 02:14:22[0m[30m[47m[47m[30m [0m[30m[47m[47m[30m [0m[30m[47m[49m[39m[151D[?1h=[?2004h[7mnpm install --save-dev @types/react-resizable-panels[27m[52D[27mn[27mp[27mm[27m [27mi[27mn[27ms[27mt[27ma[27ml[27ml[27m [27m-[27m-[27ms[27ma[27mv[27me[27m-[27md[27me[27mv[27m [27m@[27mt[27my[27mp[27me[27ms[27m/[27mr[27me[27ma[27mc[27mt[27m-[27mr[27me[27ms[27mi[27mz[27ma[27mb[27ml[27me[27m-[27mp[27ma[27mn[27me[27ml[27ms[?1l>[?2004l

knpm\[1G[0K⠙[1G[0K[1mnpm[22m [31merror[39m [94mcode[39m E404
[1G[0K⠙[1G[0K[1mnpm[22m [31merror[39m [94m404[39m Not Found - GET https://registry.npmjs.org/@types%2freact-resizable-panels - Not found
[1G[0K⠙[1G[0K[1mnpm[22m [31merror[39m [94m404[39m
[1G[0K⠙[1G[0K[1mnpm[22m [31merror[39m [94m404[39m  The requested resource '@types/react-resizable-panels@*' could not be found or you do not have permission to access it.
[1G[0K⠙[1G[0K[1mnpm[22m [31merror[39m [94m404[39m
[1G[0K⠙[1G[0K[1mnpm[22m [31merror[39m [94m404[39m Note that you can also install from a
[1G[0K⠙[1G[0K[1mnpm[22m [31merror[39m [94m404[39m tarball, folder, http url, or git url.
[1G[0K⠙[1G[0K[1mnpm[22m [31merror[39m A complete log of this run can be found in: /home/<USER>/.npm/_logs/2025-08-21T00_14_23_336Z-debug-0.log
[1G[0K⠙[1G[0K[1m[7m%[27m[1m[0m                                                                                                                                                                                                                   
 
k..n/hs/frontend\]7;file://pankaj-rog/home/<USER>/Projects/leanchain/hs/frontend\
[0m[27m[24m[J[0m[49m[39m[0m[47m[38;5;232m [0m[38;5;232m[47m[47m[38;5;232m [0m[38;5;232m[47m[44m[37m[0m[37m[44m[44m[38;5;254m  [1m[38;5;254m[44m[38;5;255m~[0m[38;5;255m[44m[44m[38;5;254m/Projects/leanchain/[1m[38;5;254m[44m[38;5;255mhs[0m[38;5;255m[44m[44m[38;5;254m/[1m[38;5;254m[44m[38;5;255mfrontend[0m[38;5;255m[44m[44m[38;5;254m[0m[38;5;254m[44m[44m[38;5;254m [0m[38;5;254m[44m[43m[34m[0m[34m[43m[43m[30m  [30m dev [30m!24 [30m?24[0m[30m[43m[43m[30m [0m[30m[43m[49m[33m[0m[33m[49m[39m [0m[49m[39m[K[132C[0m[49m[31m[0m[31m[41m[33m 1[0m[33m[41m[41m[33m ✘[0m[33m[41m[41m[33m [0m[33m[41m[41m[33m[37m[0m[37m[41m[47m[30m 02:14:23[0m[30m[47m[47m[30m [0m[30m[47m[47m[30m [0m[30m[47m[49m[39m[151D[?1h=[?2004hmmake draft[?1l>[?2004l

kmake\make: *** No rule to make target 'draft'. Stop.
[1m[7m%[27m[1m[0m                                                                                                                                                                                                                   
 
k..n/hs/frontend\]7;file://pankaj-rog/home/<USER>/Projects/leanchain/hs/frontend\
[0m[27m[24m[J[0m[49m[39m[0m[47m[38;5;232m [0m[38;5;232m[47m[47m[38;5;232m [0m[38;5;232m[47m[44m[37m[0m[37m[44m[44m[38;5;254m  [1m[38;5;254m[44m[38;5;255m~[0m[38;5;255m[44m[44m[38;5;254m/Projects/leanchain/[1m[38;5;254m[44m[38;5;255mhs[0m[38;5;255m[44m[44m[38;5;254m/[1m[38;5;254m[44m[38;5;255mfrontend[0m[38;5;255m[44m[44m[38;5;254m[0m[38;5;254m[44m[44m[38;5;254m [0m[38;5;254m[44m[43m[34m[0m[34m[43m[43m[30m  [30m dev [30m!25 [30m?27[0m[30m[43m[43m[30m [0m[30m[43m[49m[33m[0m[33m[49m[39m [0m[49m[39m[K[132C[0m[49m[31m[0m[31m[41m[33m 2[0m[33m[41m[41m[33m ✘[0m[33m[41m[41m[33m [0m[33m[41m[41m[33m[37m[0m[37m[41m[47m[30m 03:05:16[0m[30m[47m[47m[30m [0m[30m[47m[47m[30m [0m[30m[47m[49m[39m[151D[?1h=[?2004hccd ../[?1l>[?2004l

kcd\[1m[7m%[27m[1m[0m                                                                                                                                                                                                                   
 
k../leanchain/hs\]7;file://pankaj-rog/home/<USER>/Projects/leanchain/hs\
[0m[27m[24m[J[0m[49m[39m[0m[47m[38;5;232m [0m[38;5;232m[47m[47m[38;5;232m [0m[38;5;232m[47m[44m[37m[0m[37m[44m[44m[38;5;254m  [1m[38;5;254m[44m[38;5;255m~[0m[38;5;255m[44m[44m[38;5;254m/Projects/leanchain/[1m[38;5;254m[44m[38;5;255mhs[0m[38;5;255m[44m[44m[38;5;254m[0m[38;5;254m[44m[44m[38;5;254m [0m[38;5;254m[44m[43m[34m[0m[34m[43m[43m[30m  [30m dev [30m!8 [30m?10[0m[30m[43m[43m[30m [0m[30m[43m[49m[33m[0m[33m[49m[39m [0m[49m[39m[K[144C[0m[49m[30m[0m[30m[40m[32m [0m[32m[40m[40m[32m✔[0m[32m[40m[40m[32m [0m[32m[40m[40m[32m[37m[0m[37m[40m[47m[30m 03:05:19[0m[30m[47m[47m[30m [0m[30m[47m[47m[30m [0m[30m[47m[49m[39m[161D[?1h=[?2004hcd ../make draft[?1l>[?2004l

kmake\--- Creating timestamped zip archive of current work in 'drafts/' ---
--- Creating timestamped zip archive of current work in 'drafts/' ---
Creating drafts/wip-20250821-030520.zip
Including files from submodule: landing
Including files from submodule: backend
Including files from submodule: frontend
	zip warning: name not matched: data/homeservice_data.sql
	zip warning: name not matched: data/homeservice_dump.sql
	zip warning: name not matched: scripts/dump_db.sh
	zip warning: name not matched: scripts/restore_db.sh
	zip warning: name not matched: backend/alembic/versions/11da272034a5_initial_migration.py
	zip warning: name not matched: backend/alembic/versions/7afe0bb9a1e3_refactor_knowledge_items_to_documents.py
	zip warning: name not matched: backend/src/orchestrator/config_loader.py
	zip warning: name not matched: backend/src/orchestrator/configs/default_agent.json
	zip warning: name not matched: frontend/src/app/(app)/test-center/page.tsx
	zip warning: name not matched: frontend/src/components/features/TestCenter/OrderHistoryModal.tsx
  adding: AGENT_BUILDER_COMPLETE.md (deflated 62%)
  adding: AGENT_BUILDER_README.md (deflated 62%)
  adding: IMPLEMENTATION_COMPLETE.md (deflated 68%)
  adding: SETUP_GUIDE.md (deflated 59%)
  adding: data/homeservice_dump_20250820-233345.sql (deflated 44%)
  adding: data/n8n_dump_20250820-233345.sql (deflated 83%)
  adding: data/test_db_dump_20250820-233345.sql (deflated 44%)
  adding: scripts/dbs/01_init_dbs.sh (deflated 54%)
  adding: scripts/dbs/02_restore_dbs.sh (deflated 54%)
  adding: scripts/dbs/03_dump_dbs.sh (deflated 57%)
  adding: scripts/zip-wip.sh (deflated 51%)
  adding: typescript (deflated 84%)
  adding: .git-crypt/.gitattributes (deflated 28%)
  adding: .git-crypt/keys/default/0/E556646840791A0F16DA342FAD4428D8EFF2A8F4.gpg (stored 0%)
  adding: .gitattributes (deflated 50%)
  adding: .gitignore (deflated 59%)
  adding: .gitmodules (deflated 59%)
  adding: .pre-commit-config.yaml (deflated 50%)
  adding: ERROR_FIXES_SUMMARY.md (deflated 65%)
  adding: Makefile (deflated 63%)
  adding: PROJECT_PLAN.md (deflated 62%)
  adding: README.md (deflated 56%)
  adding: backend/ (stored 0%)
  adding: docker-compose.yml (deflated 64%)
  adding: download_images.sh (deflated 79%)
  adding: frontend/ (stored 0%)
  adding: landing/ (stored 0%)
  adding: package.json (deflated 17%)
  adding: scripts/cleanup_test_dbs.sh (deflated 45%)
  adding: scripts/git-commit-all.sh (deflated 60%)
  adding: scripts/git-pull-all.sh (deflated 53%)
  adding: scripts/git-push-all.sh (deflated 54%)
  adding: scripts/git-status-all.sh (deflated 45%)
  adding: landing/.env (deflated 2%)
  adding: landing/.gitattributes (deflated 50%)
  adding: landing/.gitignore (deflated 38%)
  adding: landing/.vite/deps_temp_c7b1639e/package.json (stored 0%)
  adding: landing/Dockerfile (deflated 40%)
  adding: landing/Dockerfile.dev (deflated 28%)
  adding: landing/HERO_IMAGES_SUMMARY.md (deflated 64%)
  adding: landing/README.md (deflated 52%)
  adding: landing/babel.config.cjs (deflated 31%)
  adding: landing/components.json (deflated 49%)
  adding: landing/download-images.js (deflated 71%)
  adding: landing/eslint.config.js (deflated 55%)
  adding: landing/index.html (deflated 57%)
  adding: landing/jest.config.cjs (deflated 23%)
  adding: landing/nginx.conf (deflated 39%)
  adding: landing/package-lock.json (deflated 79%)
  adding: landing/package.json (deflated 69%)
  adding: landing/postcss.config.js (deflated 18%)
  adding: landing/public/ScreenShot Tool -20250811235628.png (deflated 0%)
  adding: landing/public/commercial-cleaning-hero.svg (deflated 77%)
  adding: landing/public/commercial-cleaning-icon.svg (deflated 60%)
  adding: landing/public/communication-icon.svg (deflated 71%)
  adding: landing/public/crm-icon.svg (deflated 63%)
  adding: landing/public/electrical-hero.svg (deflated 74%)
  adding: landing/public/electrical-icon.svg (deflated 36%)
  adding: landing/public/email-marketing-icon.svg (deflated 53%)
  adding: landing/public/favicon-white.svg (deflated 18%)
  adding: landing/public/favicon.svg (deflated 29%)
  adding: landing/public/hvac-hero.svg (deflated 69%)
  adding: landing/public/hvac-icon.svg (deflated 57%)
  adding: landing/public/images/hero/commercialCleaning/commercialCleaning1.jpeg (deflated 3%)
  adding: landing/public/images/hero/commercialCleaning/commercialCleaning2.jpeg (deflated 2%)
  adding: landing/public/images/hero/commercialCleaning/commercialCleaning3.jpeg (deflated 1%)
  adding: landing/public/images/hero/commercialCleaning/commercialCleaning4.jpeg (deflated 3%)
  adding: landing/public/images/hero/electrical/electrical1.jpeg (deflated 1%)
  adding: landing/public/images/hero/electrical/electrical2.jpeg (deflated 1%)
  adding: landing/public/images/hero/electrical/electrical3.jpeg (deflated 0%)
  adding: landing/public/images/hero/hvac/hvac1.jpeg (deflated 2%)
  adding: landing/public/images/hero/hvac/hvac2.jpeg (deflated 2%)
  adding: landing/public/images/hero/hvac/hvac3.jpeg (deflated 2%)
  adding: landing/public/images/hero/landscaping/landscaping1.jpeg (deflated 1%)
  adding: landing/public/images/hero/landscaping/landscaping2.jpeg (deflated 1%)
  adding: landing/public/images/hero/landscaping/landscaping3.jpeg (deflated 1%)
  adding: landing/public/images/hero/pestControl/pestControl1.jpeg (deflated 2%)
  adding: landing/public/images/hero/pestControl/pestControl2.jpeg (deflated 2%)
  adding: landing/public/images/hero/pestControl/pestControl3.jpeg (deflated 2%)
  adding: landing/public/images/hero/plumbing/plumbing1.jpeg (deflated 0%)
  adding: landing/public/images/hero/plumbing/plumbing2.jpeg (deflated 2%)
  adding: landing/public/images/hero/plumbing/plumbing3.jpeg (deflated 1%)
  adding: landing/public/images/hero/roofing/roofing1.jpeg (deflated 2%)
  adding: landing/public/images/hero/roofing/roofing2.jpeg (deflated 4%)
  adding: landing/public/images/hero/roofing/roofing3.jpeg (deflated 5%)
  adding: landing/public/images/hero/solar/solar1.jpeg (deflated 0%)
  adding: landing/public/images/hero/solar/solar2.jpeg (deflated 1%)
  adding: landing/public/images/hero/solar/solar3.jpeg (deflated 1%)
  adding: landing/public/images/hero/windowCleaning/windowCleaning1.jpeg (deflated 1%)
  adding: landing/public/images/hero/windowCleaning/windowCleaning2.jpeg (deflated 1%)
  adding: landing/public/images/hero/windowCleaning/windowCleaning3.jpeg (deflated 2%)
  adding: landing/public/images/team/bart-rosier.jpeg (deflated 0%)
  adding: landing/public/images/team/pankaj-kumar.jpeg (deflated 0%)
  adding: landing/public/landscaping-hero.svg (deflated 70%)
  adding: landing/public/landscaping-icon.svg (deflated 61%)
  adding: landing/public/pest-control-hero.svg (deflated 72%)
  adding: landing/public/pest-control-icon.svg (deflated 55%)
  adding: landing/public/placeholder.svg (deflated 72%)
  adding: landing/public/plumbing-hero.svg (deflated 72%)
  adding: landing/public/plumbing-icon.svg (deflated 56%)
  adding: landing/public/privacy/terms-europe.md (deflated 68%)
  adding: landing/public/resources/ai-answering-and-scheduling-assistants-for-home-service-leads.md (deflated 58%)
  adding: landing/public/resources/ai-in-the-contact-center-the-future-of-customer-service.md (deflated 60%)
  adding: landing/public/resources/ai-voice-technology-benefits-and-challenges.md (deflated 59%)
  adding: landing/public/resources/autonomous-repair-solutions-the-future-of-home-services.md (deflated 56%)
  adding: landing/public/resources/how-ai-call-center-platforms-revolutionize-home-services.md (deflated 57%)
  adding: landing/public/resources/how-ai-is-revolutionizing-the-home-services-industry.md (deflated 57%)
  adding: landing/public/resources/how-voice-ai-assistants-can-help-aging-adults-live-independently.md (deflated 48%)
  adding: landing/public/resources/the-benefits-of-voice-ai-for-smart-home-automation.md (deflated 58%)
  adding: landing/public/resources/the-future-of-home-services-how-ai-is-changing-the-game.md (deflated 58%)
  adding: landing/public/resources/the-impact-of-ai-driven-voice-assistants-on-smart-home-ecosystems.md (deflated 56%)
  adding: landing/public/robots.txt (deflated 54%)
  adding: landing/public/roofing-hero-real.jpg (stored 0%)
  adding: landing/public/roofing-hero.svg (deflated 71%)
  adding: landing/public/roofing-icon.png (deflated 4%)
  adding: landing/public/roofing-icon.svg (deflated 39%)
  adding: landing/public/solar-hero.svg (deflated 72%)
  adding: landing/public/solar-icon.svg (deflated 53%)
  adding: landing/public/test-image.jpg (stored 0%)
  adding: landing/public/testimonial-1-men.jpg (deflated 9%)
  adding: landing/public/testimonial-2-women.jpg (deflated 6%)
  adding: landing/public/testimonial-3-men.jpg (deflated 6%)
  adding: landing/public/testimonial-4-women.jpg (deflated 1%)
  adding: landing/public/testimonial-5-men.jpg (deflated 10%)
  adding: landing/public/testimonial-6-women.jpg (deflated 1%)
  adding: landing/public/window-cleaning-hero.svg (deflated 79%)
  adding: landing/public/window-cleaning-icon.svg (deflated 57%)
  adding: landing/src/AnimatedRoutes.tsx (deflated 86%)
  adding: landing/src/App.css (deflated 47%)
  adding: landing/src/App.tsx (deflated 61%)
  adding: landing/src/assets/company-logos.jpg (deflated 16%)
  adding: landing/src/assets/hero-image.jpg (deflated 3%)
  adding: landing/src/assets/roofer-hero.jpg (deflated 2%)
  adding: landing/src/components/ConversionSection.tsx (deflated 76%)
  adding: landing/src/components/DemoCallsSection.tsx (deflated 71%)
  adding: landing/src/components/FiesonLogo.tsx (deflated 41%)
  adding: landing/src/components/Footer.tsx (deflated 77%)
  adding: landing/src/components/Header.tsx (deflated 61%)
  adding: landing/src/components/Hero.tsx (deflated 71%)
  adding: landing/src/components/HowItWorks.tsx (deflated 61%)
  adding: landing/src/components/LeadGenSection.tsx (deflated 77%)
  adding: landing/src/components/Testimonials.tsx (deflated 71%)
  adding: landing/src/components/UseCases.tsx (deflated 69%)
  adding: landing/src/components/ui/accordion.tsx (deflated 68%)
  adding: landing/src/components/ui/alert-dialog.tsx (deflated 77%)
  adding: landing/src/components/ui/alert.tsx (deflated 62%)
  adding: landing/src/components/ui/aspect-ratio.tsx (deflated 34%)
  adding: landing/src/components/ui/avatar.tsx (deflated 71%)
  adding: landing/src/components/ui/badge.tsx (deflated 56%)
  adding: landing/src/components/ui/breadcrumb.tsx (deflated 68%)
  adding: landing/src/components/ui/button.tsx (deflated 65%)
  adding: landing/src/components/ui/calendar.tsx (deflated 64%)
  adding: landing/src/components/ui/card.tsx (deflated 74%)
  adding: landing/src/components/ui/carousel.tsx (deflated 73%)
  adding: landing/src/components/ui/chart.tsx (deflated 73%)
  adding: landing/src/components/ui/checkbox.tsx (deflated 55%)
  adding: landing/src/components/ui/collapsible.tsx (deflated 61%)
  adding: landing/src/components/ui/command.tsx (deflated 75%)
  adding: landing/src/components/ui/context-menu.tsx (deflated 82%)
  adding: landing/src/components/ui/dialog.tsx (deflated 72%)
  adding: landing/src/components/ui/drawer.tsx (deflated 73%)
  adding: landing/src/components/ui/dropdown-menu.tsx (deflated 81%)
  adding: landing/src/components/ui/f-icon.tsx (deflated 33%)
  adding: landing/src/components/ui/form.tsx (deflated 73%)
  adding: landing/src/components/ui/hover-card.tsx (deflated 59%)
  adding: landing/src/components/ui/input-otp.tsx (deflated 65%)
  adding: landing/src/components/ui/input.tsx (deflated 48%)
  adding: landing/src/components/ui/label.tsx (deflated 50%)
  adding: landing/src/components/ui/menubar.tsx (deflated 82%)
  adding: landing/src/components/ui/navigation-menu.tsx (deflated 76%)
  adding: landing/src/components/ui/pagination.tsx (deflated 70%)
  adding: landing/src/components/ui/popover.tsx (deflated 60%)
  adding: landing/src/components/ui/progress.tsx (deflated 50%)
  adding: landing/src/components/ui/radio-group.tsx (deflated 64%)
  adding: landing/src/components/ui/resizable.tsx (deflated 67%)
  adding: landing/src/components/ui/scroll-area.tsx (deflated 67%)
  adding: landing/src/components/ui/select.tsx (deflated 76%)
  adding: landing/src/components/ui/separator.tsx (deflated 54%)
  adding: landing/src/components/ui/sheet.tsx (deflated 72%)
  adding: landing/src/components/ui/sidebar.tsx (deflated 79%)
  adding: landing/src/components/ui/skeleton.tsx (deflated 27%)
  adding: landing/src/components/ui/slider.tsx (deflated 56%)
  adding: landing/src/components/ui/sonner.tsx (deflated 60%)
  adding: landing/src/components/ui/switch.tsx (deflated 55%)
  adding: landing/src/components/ui/table.tsx (deflated 76%)
  adding: landing/src/components/ui/tabs.tsx (deflated 70%)
  adding: landing/src/components/ui/textarea.tsx (deflated 48%)
  adding: landing/src/components/ui/toast.tsx (deflated 73%)
  adding: landing/src/components/ui/toaster.tsx (deflated 59%)
  adding: landing/src/components/ui/toggle-group.tsx (deflated 68%)
  adding: landing/src/components/ui/toggle.tsx (deflated 57%)
  adding: landing/src/components/ui/tooltip.tsx (deflated 58%)
  adding: landing/src/components/ui/use-toast.ts (deflated 33%)
  adding: landing/src/data/industryContent.ts (deflated 78%)
  adding: landing/src/hooks/use-mobile.tsx (deflated 48%)
  adding: landing/src/hooks/use-toast.ts (deflated 67%)
  adding: landing/src/i18n.ts (deflated 53%)
  adding: landing/src/index.css (deflated 70%)
  adding: landing/src/lib/utils.ts (deflated 27%)
  adding: landing/src/main.tsx (deflated 28%)
  adding: landing/src/pages/About.tsx (deflated 69%)
  adding: landing/src/pages/Contact.tsx (deflated 66%)
  adding: landing/src/pages/Index.tsx (deflated 64%)
  adding: landing/src/pages/Industries.tsx (deflated 69%)
  adding: landing/src/pages/IndustryPage.tsx (deflated 76%)
  adding: landing/src/pages/Integrations.tsx (deflated 69%)
  adding: landing/src/pages/MarkdownPage.tsx (deflated 67%)
  adding: landing/src/pages/NotFound.tsx (deflated 48%)
  adding: landing/src/pages/Pricing.tsx (deflated 76%)
  adding: landing/src/pages/Resources.tsx (deflated 68%)
  adding: landing/src/vite-env.d.ts (stored 0%)
  adding: landing/tailwind.config.ts (deflated 71%)
  adding: landing/tsconfig.app.json (deflated 48%)
  adding: landing/tsconfig.json (deflated 46%)
  adding: landing/tsconfig.node.json (deflated 45%)
  adding: landing/vite.config.ts (deflated 40%)
  adding: backend/alembic/versions/11687ed232b6_initial_migration.py (deflated 87%)
  adding: backend/alembic/versions/6607e10aa23e_add_description_to_agent_model.py (deflated 52%)
  adding: backend/src/core/middleware/request_logging_middleware.py (deflated 59%)
  adding: backend/src/core/middleware/websocket_logging_decorator.py (deflated 66%)
  adding: backend/src/modules/templates/__init__.py (deflated 18%)
  adding: backend/src/modules/templates/crud.py (deflated 70%)
  adding: backend/src/modules/templates/router.py (deflated 66%)
  adding: backend/src/modules/templates/schemas.py (deflated 66%)
  adding: backend/src/modules/templates/service.py (deflated 12%)
  adding: backend/src/orchestrator/api/agent_builder.py (deflated 78%)
  adding: backend/src/orchestrator/api/node_management.py (deflated 80%)
  adding: backend/src/orchestrator/nodes/n8n_node.py (deflated 81%)
  adding: backend/src/orchestrator/services/n8n_service.py (deflated 81%)
  adding: backend/src/orchestrator/services/prompt_optimization.py (deflated 77%)
  adding: backend/src/orchestrator/template_loader.py (deflated 52%)
  adding: backend/src/orchestrator/templates/default_agent.json (deflated 68%)
  adding: backend/src/orchestrator/templates/multi_llm_agent.json (deflated 71%)
  adding: backend/src/orchestrator/templates/simple_agent.json (deflated 64%)
  adding: backend/.dockerignore (stored 0%)
  adding: backend/.env (deflated 33%)
  adding: backend/.env.example (deflated 40%)
  adding: backend/.env.test (deflated 33%)
  adding: backend/.gitattributes (deflated 50%)
  adding: backend/.gitignore (deflated 59%)
  adding: backend/.pre-commit-config.yaml (deflated 57%)
  adding: backend/Dockerfile (deflated 46%)
  adding: backend/IMPLEMENTATION_SUMMARY.md (deflated 66%)
  adding: backend/README.md (deflated 44%)
  adding: backend/alembic.ini (deflated 55%)
  adding: backend/alembic/README (stored 0%)
  adding: backend/alembic/env.py (deflated 63%)
  adding: backend/alembic/script.py.mako (deflated 53%)
  adding: backend/docs/AGENT_ORCHESTRATION.md (deflated 64%)
  adding: backend/docs/schemas/agent_config_schema.json (deflated 83%)
  adding: backend/generate_tests.py (deflated 72%)
  adding: backend/mypy.ini (deflated 35%)
  adding: backend/pitch.html (deflated 77%)
  adding: backend/pyproject.toml (deflated 53%)
  adding: backend/pytest.ini (deflated 7%)
  adding: backend/src/__init__.py (stored 0%)
  adding: backend/src/core/config.py (deflated 52%)
  adding: backend/src/core/db/database.py (deflated 57%)
  adding: backend/src/core/guardrails/user_intent.xml (deflated 28%)
  adding: backend/src/core/middleware/request_context_middleware.py (deflated 57%)
  adding: backend/src/core/performance_tracker.py (deflated 55%)
  adding: backend/src/core/services/conversation_manager.py (deflated 72%)
  adding: backend/src/core/utils/__init__.py (stored 0%)
  adding: backend/src/core/utils/events.py (deflated 53%)
  adding: backend/src/core/utils/helpers.py (deflated 69%)
  adding: backend/src/core/utils/logging.py (deflated 53%)
  adding: backend/src/core/utils/performance_tracker.py (deflated 66%)
  adding: backend/src/core/utils/request_context.py (deflated 31%)
  adding: backend/src/core/websocket/connection_manager.py (deflated 72%)
  adding: backend/src/main.py (deflated 69%)
  adding: backend/src/modules/agents/crud.py (deflated 66%)
  adding: backend/src/modules/agents/models.py (deflated 54%)
  adding: backend/src/modules/agents/router.py (deflated 72%)
  adding: backend/src/modules/agents/schemas.py (deflated 52%)
  adding: backend/src/modules/auth/router.py (deflated 66%)
  adding: backend/src/modules/auth/schemas.py (deflated 58%)
  adding: backend/src/modules/auth/service.py (deflated 65%)
  adding: backend/src/modules/calendar/service.py (deflated 56%)
  adding: backend/src/modules/call/crud.py (deflated 75%)
  adding: backend/src/modules/call/models.py (deflated 70%)
  adding: backend/src/modules/call/router.py (deflated 74%)
  adding: backend/src/modules/call/schemas.py (deflated 71%)
  adding: backend/src/modules/call/service.py (deflated 81%)
  adding: backend/src/modules/customers/crud.py (deflated 71%)
  adding: backend/src/modules/customers/models.py (deflated 63%)
  adding: backend/src/modules/customers/router.py (deflated 72%)
  adding: backend/src/modules/customers/schemas.py (deflated 58%)
  adding: backend/src/modules/customers/tools.py (deflated 55%)
  adding: backend/src/modules/dashboard/router.py (deflated 63%)
  adding: backend/src/modules/dashboard/schemas.py (deflated 66%)
  adding: backend/src/modules/dashboard/service.py (deflated 69%)
  adding: backend/src/modules/documents/crud.py (deflated 58%)
  adding: backend/src/modules/documents/models.py (deflated 67%)
  adding: backend/src/modules/jobs/crud.py (deflated 69%)
  adding: backend/src/modules/jobs/models.py (deflated 57%)
  adding: backend/src/modules/jobs/router.py (deflated 70%)
  adding: backend/src/modules/jobs/schemas.py (deflated 52%)
  adding: backend/src/modules/performance/crud.py (deflated 64%)
  adding: backend/src/modules/performance/models.py (deflated 56%)
  adding: backend/src/modules/performance/router.py (deflated 59%)
  adding: backend/src/modules/performance/schemas.py (deflated 51%)
  adding: backend/src/modules/test_center/__init__.py (stored 0%)
  adding: backend/src/modules/test_center/models.py (deflated 54%)
  adding: backend/src/modules/test_center/router.py (deflated 70%)
  adding: backend/src/modules/test_center/schemas.py (deflated 66%)
  adding: backend/src/modules/test_center/service.py (deflated 73%)
  adding: backend/src/modules/users/models.py (deflated 58%)
  adding: backend/src/modules/users/schemas.py (deflated 44%)
  adding: backend/src/orchestrator/__init__.py (stored 0%)
  adding: backend/src/orchestrator/dspy/modules.py (deflated 75%)
  adding: backend/src/orchestrator/dspy/signatures.py (deflated 32%)
  adding: backend/src/orchestrator/graph.py (deflated 79%)
  adding: backend/src/orchestrator/models.py (deflated 67%)
  adding: backend/src/orchestrator/nodes/__init__.py (stored 0%)
  adding: backend/src/orchestrator/nodes/llm.py (deflated 75%)
  adding: backend/src/orchestrator/nodes/rag.py (deflated 61%)
  adding: backend/src/orchestrator/nodes/tools.py (deflated 66%)
  adding: backend/src/orchestrator/router.py (deflated 65%)
  adding: backend/src/orchestrator/schemas.py (deflated 73%)
  adding: backend/src/orchestrator/services/callbacks.py (deflated 75%)
  adding: backend/src/orchestrator/services/knowledge/rag_service.py (deflated 60%)
  adding: backend/src/orchestrator/services/llm_integrations/__init__.py (stored 0%)
  adding: backend/src/orchestrator/services/llm_integrations/embedding_factory.py (deflated 69%)
  adding: backend/src/orchestrator/services/llm_integrations/llm_factory.py (deflated 74%)
  adding: backend/src/orchestrator/services/memory/mem0_service.py (deflated 75%)
  adding: backend/src/orchestrator/services/orchestration_service.py (deflated 68%)
  adding: backend/src/orchestrator/services/stt/__init__.py (deflated 52%)
  adding: backend/src/orchestrator/services/stt/assemblyai.py (deflated 65%)
  adding: backend/src/orchestrator/services/stt/base.py (deflated 54%)
  adding: backend/src/orchestrator/services/stt/nova.py (deflated 65%)
  adding: backend/src/orchestrator/services/tts/__init__.py (deflated 45%)
  adding: backend/src/orchestrator/services/tts/base.py (deflated 70%)
  adding: backend/src/orchestrator/services/tts/elevenlabs.py (deflated 68%)
  adding: backend/src/orchestrator/services/tts/nova.py (deflated 75%)
  adding: backend/src/orchestrator/services/websocket/io.py (deflated 63%)
  adding: backend/src/orchestrator/tools.py (deflated 54%)
  adding: backend/src/orchestrator/tools/__init__.py (stored 0%)
  adding: backend/src/orchestrator/tools/langchain_tools.py (deflated 61%)
  adding: backend/src/orchestrator/tools/registry.py (deflated 50%)
  adding: backend/src/scripts/enable_pgvector.py (deflated 52%)
  adding: backend/src/scripts/seed_templates.py (deflated 64%)
  adding: backend/test_runner.py (deflated 67%)
  adding: backend/tests/conftest.py (deflated 64%)
  adding: backend/tests/e2e/test_e2e_call.py (deflated 81%)
  adding: backend/tests/e2e/test_e2e_phone_calls.py (deflated 78%)
  adding: backend/tests/e2e/test_e2e_web_calls.py (deflated 84%)
  adding: backend/tests/integration/__init__.py (stored 0%)
  adding: backend/tests/integration/test_agent_graph.py (deflated 81%)
  adding: backend/tests/modules/call/test_call.py (deflated 69%)
  adding: backend/tests/test_customers.py (deflated 64%)
  adding: backend/tests/test_main.py (deflated 60%)
  adding: backend/tests/utils/__init__.py (stored 0%)
  adding: backend/tests/utils/test_helpers.py (deflated 71%)
  adding: backend/uv.lock (deflated 69%)
  adding: frontend/src/app/(app)/agents/[agentId]/page_old.tsx (deflated 75%)
  adding: frontend/src/app/(app)/customers/builder/page.tsx (deflated 76%)
  adding: frontend/src/app/(app)/node/page.tsx (deflated 78%)
  adding: frontend/src/components/JsonViewer.tsx (deflated 58%)
  adding: frontend/src/components/features/AgentBuilder/AgentAnalytics.tsx (deflated 78%)
  adding: frontend/src/components/features/AgentBuilder/AgentCanvas.tsx (deflated 74%)
  adding: frontend/src/components/features/AgentBuilder/AgentDeployment.tsx (deflated 82%)
  adding: frontend/src/components/features/AgentBuilder/ChatInterface.tsx (deflated 74%)
  adding: frontend/src/components/features/AgentBuilder/DSPyConfigEditor.tsx (deflated 82%)
  adding: frontend/src/components/features/AgentBuilder/IntegrationTesting.tsx (deflated 81%)
  adding: frontend/src/components/features/AgentBuilder/MemoryConfigEditor.tsx (deflated 85%)
  adding: frontend/src/components/features/AgentBuilder/NodeEditor.tsx (deflated 81%)
  adding: frontend/src/components/features/AgentBuilder/NodePalette.tsx (deflated 74%)
  adding: frontend/src/components/features/AgentBuilder/TemplateBrowser.tsx (deflated 76%)
  adding: frontend/src/components/features/AgentBuilder/TemplateManager.tsx (deflated 76%)
  adding: frontend/src/components/features/AgentBuilder/WorkflowCanvas.tsx (deflated 72%)
  adding: frontend/src/components/features/NodeTester/NodeTester.tsx (deflated 76%)
  adding: frontend/src/components/features/TestCenter/AgentConfig.tsx (deflated 81%)
  adding: frontend/src/components/features/TestCenter/ToolEditor.tsx (deflated 56%)
  adding: frontend/src/components/features/TestCenter/ToolManager.tsx (deflated 79%)
  adding: frontend/src/components/ui/JsonViewerModal.tsx (deflated 52%)
  adding: frontend/src/components/ui/scroll-area.tsx (deflated 67%)
  adding: frontend/src/data/electrical-agent-template.json (deflated 66%)
  adding: frontend/src/data/example-agents.ts (deflated 68%)
  adding: frontend/src/data/hvac-agent-template.json (deflated 65%)
  adding: frontend/src/data/plumbing-agent-template.json (deflated 65%)
  adding: frontend/src/data/simple-agent-template.json (deflated 64%)
  adding: frontend/src/lib/syntaxHighlight.ts (deflated 53%)
  adding: frontend/src/services/agent-builder/agentBuilderService.ts (deflated 75%)
  adding: frontend/src/services/api/apiClient.ts (deflated 75%)
  adding: frontend/src/services/config/configService.ts (deflated 78%)
  adding: frontend/src/services/error/errorHandler.ts (deflated 74%)
  adding: frontend/src/services/index.ts (deflated 70%)
  adding: frontend/src/services/templates/templateService.ts (deflated 78%)
  adding: frontend/src/services/testing/testingService.ts (deflated 77%)
  adding: frontend/src/services/websocket/websocketService.ts (deflated 75%)
  adding: frontend/src/types/workflow.ts (deflated 61%)
  adding: frontend/srcsrc/app/(app)/agents/builder/page.tsx (deflated 74%)
  adding: frontend/.dockerignore (stored 0%)
  adding: frontend/.env (deflated 23%)
  adding: frontend/.gitattributes (deflated 50%)
  adding: frontend/.gitignore (deflated 55%)
  adding: frontend/.pre-commit-config.yaml (deflated 65%)
  adding: frontend/.prettierrc.json (deflated 26%)
  adding: frontend/Dockerfile (deflated 49%)
  adding: frontend/Dockerfile.dev (deflated 23%)
  adding: frontend/FRONTEND_IMPLEMENTATION_SUMMARY.md (deflated 66%)
  adding: frontend/README.md (deflated 51%)
  adding: frontend/components.json (deflated 44%)
  adding: frontend/eslint.config.mjs (deflated 44%)
  adding: frontend/next-env.d.ts (deflated 33%)
  adding: frontend/next.config.ts (deflated 28%)
  adding: frontend/nginx.conf (deflated 39%)
  adding: frontend/package-lock.json (deflated 79%)
  adding: frontend/package.json (deflated 65%)
  adding: frontend/postcss.config.mjs (deflated 7%)
  adding: frontend/public/recorderProcessor.js (deflated 47%)
  adding: frontend/src/app/(app)/agents/[agentId]/page.tsx (deflated 80%)
  adding: frontend/src/app/(app)/agents/new/page.tsx (deflated 69%)
  adding: frontend/src/app/(app)/agents/page.tsx (deflated 74%)
  adding: frontend/src/app/(app)/agents/templates/page.tsx (deflated 74%)
  adding: frontend/src/app/(app)/analytics/page.tsx (deflated 45%)
  adding: frontend/src/app/(app)/calls/page.tsx (deflated 44%)
  adding: frontend/src/app/(app)/customers/page.tsx (deflated 71%)
  adding: frontend/src/app/(app)/dashboard/page.tsx (deflated 80%)
  adding: frontend/src/app/(app)/jobs/page.tsx (deflated 68%)
  adding: frontend/src/app/(app)/layout.tsx (deflated 56%)
  adding: frontend/src/app/(app)/numbers/page.tsx (deflated 72%)
  adding: frontend/src/app/(auth)/forgot-password/page.tsx (deflated 63%)
  adding: frontend/src/app/(auth)/layout.tsx (deflated 25%)
  adding: frontend/src/app/(auth)/login/page.tsx (deflated 69%)
  adding: frontend/src/app/(auth)/register/page.tsx (deflated 71%)
  adding: frontend/src/app/(auth)/reset-password/ResetPasswordForm.tsx (deflated 67%)
  adding: frontend/src/app/(auth)/reset-password/page.tsx (deflated 41%)
  adding: frontend/src/app/(marketing)/layout.tsx (deflated 52%)
  adding: frontend/src/app/(marketing)/page.tsx (deflated 79%)
  adding: frontend/src/app/favicon.ico (deflated 64%)
  adding: frontend/src/app/globals.css (deflated 72%)
  adding: frontend/src/app/layout.tsx (deflated 57%)
  adding: frontend/src/app/not-found.tsx (deflated 49%)
  adding: frontend/src/components/common/Header.tsx (deflated 75%)
  adding: frontend/src/components/common/Navbar.tsx (deflated 75%)
  adding: frontend/src/components/common/Sidebar.tsx (deflated 73%)
  adding: frontend/src/components/features/TestCenter/AgentSelector.tsx (deflated 75%)
  adding: frontend/src/components/features/TestCenter/ConversationLog.tsx (deflated 69%)
  adding: frontend/src/components/features/TestCenter/CustomerDetails.tsx (deflated 64%)
  adding: frontend/src/components/features/TestCenter/CustomerSelector.tsx (deflated 67%)
  adding: frontend/src/components/features/TestCenter/Insights.tsx (deflated 72%)
  adding: frontend/src/components/features/TestCenter/LivePerformanceChart.tsx (deflated 60%)
  adding: frontend/src/components/features/TestCenter/LiveTranscript.tsx (deflated 70%)
  adding: frontend/src/components/features/TestCenter/WebCallWidget.tsx (deflated 67%)
  adding: frontend/src/components/features/agent-editor/AgentConfigurationPanel.tsx (deflated 83%)
  adding: frontend/src/components/features/agent-editor/AgentEditorFlow.tsx (deflated 61%)
  adding: frontend/src/components/features/agent-editor/CreateAgentDialog.tsx (deflated 66%)
  adding: frontend/src/components/features/agent-editor/EditorHeader.tsx (deflated 53%)
  adding: frontend/src/components/features/agent-editor/JsonViewPanel.tsx (deflated 54%)
  adding: frontend/src/components/features/agent-editor/NodeTestPanel.tsx (deflated 61%)
  adding: frontend/src/components/features/agent-editor/NodesSidebar.tsx (deflated 57%)
  adding: frontend/src/components/features/agent-editor/PropertiesPanel.tsx (deflated 75%)
  adding: frontend/src/components/features/auth/ProtectedRoute.tsx (deflated 49%)
  adding: frontend/src/components/ui/avatar.tsx (deflated 67%)
  adding: frontend/src/components/ui/badge.tsx (deflated 56%)
  adding: frontend/src/components/ui/button.tsx (deflated 59%)
  adding: frontend/src/components/ui/card.tsx (deflated 74%)
  adding: frontend/src/components/ui/command.tsx (deflated 75%)
  adding: frontend/src/components/ui/dialog.tsx (deflated 71%)
  adding: frontend/src/components/ui/dropdown-menu.tsx (deflated 81%)
  adding: frontend/src/components/ui/input.tsx (deflated 47%)
  adding: frontend/src/components/ui/label.tsx (deflated 50%)
  adding: frontend/src/components/ui/navigation-menu.tsx (deflated 77%)
  adding: frontend/src/components/ui/popover.tsx (deflated 59%)
  adding: frontend/src/components/ui/resizable.tsx (deflated 45%)
  adding: frontend/src/components/ui/select.tsx (deflated 74%)
  adding: frontend/src/components/ui/separator.tsx (deflated 54%)
  adding: frontend