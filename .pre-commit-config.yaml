repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.6.0
    hooks:
      - id: check-yaml
        exclude: ^data/
      - id: end-of-file-fixer
        exclude: ^data/
      - id: trailing-whitespace
        exclude: ^data/
  - repo: local
    hooks:
      - id: dump-database
        name: Dump Database
        entry: bash -c "./scripts/dump_db.sh && git add data/homeservice_dump.sql"
        language: system
        pass_filenames: false
        always_run: true
        stages: [pre-commit]
        require_serial: true
