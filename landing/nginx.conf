server {
    listen 8080; # Cloud Run requires listening on port 8080
    server_name localhost;

    root /usr/share/nginx/html; # This is where your built React app will be served from

    index index.html index.htm;

    location / {
        try_files $uri $uri/ /index.html; # This is the key for SPA routing fallback
    }

    # Optional: Add caching for static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        log_not_found off;
        add_header Cache-Control "public";
    }
}