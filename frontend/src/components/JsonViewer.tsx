import React, { useMemo } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Copy, Check } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { syntaxHighlight } from '@/lib/syntaxHighlight';

interface JsonViewerProps {
  jsonData: object | string;
  isOpen: boolean;
  onClose: () => void;
  title?: string;
}

export const JsonViewer: React.FC<JsonViewerProps> = ({ jsonData, isOpen, onClose, title = "Workflow JSON" }) => {
  const [isCopied, setIsCopied] = React.useState(false);

  const processedJson = useMemo(() => {
    let jsonContent: object | string = jsonData;
    if (typeof jsonData === 'string') {
      try {
        jsonContent = JSON.parse(jsonData);
      } catch (e) {
        return "Invalid JSON string provided.";
      }
    }
    return JSON.stringify(jsonContent, null, 2);
  }, [jsonData]);

  const highlightedJson = useMemo(() => {
    if (processedJson === "Invalid JSON string provided.") {
      return processedJson;
    }
    return syntaxHighlight(processedJson);
  }, [processedJson]);

  const handleCopy = () => {
    if (processedJson === "Invalid JSON string provided.") {
      toast.error('Cannot copy invalid JSON.');
      return;
    }
    navigator.clipboard.writeText(processedJson);
    toast.success('Copied to clipboard');
    setIsCopied(true);
    setTimeout(() => setIsCopied(false), 2000);
  };

  

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl h-[80vh] flex flex-col border-gray-700">
        <DialogHeader>
          <DialogTitle className="text-gray-900">{title}</DialogTitle>
        </DialogHeader>
        <div className="relative flex-grow overflow-auto bg-gray-800 p-4 rounded-md border border-gray-700 text-white ">
          <Button
            size="sm"
            variant="ghost"
            className="absolute top-2 right-2 text-gray-400 hover:text-white hover:bg-gray-700"
            onClick={handleCopy}
          >
            {isCopied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
          </Button>
          <pre>
            <code dangerouslySetInnerHTML={{ __html: highlightedJson }} />
          </pre>
        </div>
        <DialogFooter className="mt-4">
          <Button onClick={onClose} variant="outline">
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
