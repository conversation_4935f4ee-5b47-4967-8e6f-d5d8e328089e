'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import {
  LayoutDashboard,
  LineChart,
  Users,
  Phone,
  Briefcase,
  ChevronLeft,
  LogOut,
  FlaskConical,
  Bot,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

const Sidebar: React.FC = () => {
  const pathname = usePathname();
  const { user, logout } = useAuth();
  const router = useRouter();

  // State to manage the user's explicit collapse preference
  const [userCollapsedPreference, setUserCollapsedPreference] = useState(false);
  // State to track if the screen is considered "small"
  const [isSmallScreen, setIsSmallScreen] = useState(() => {
    if (typeof window !== 'undefined') {
      return window.innerWidth < 768; // md breakpoint
    }
    return false;
  });

  // State for dynamic sidebar width
  const [sidebarWidth, setSidebarWidth] = useState(240); // Default width
  const MIN_WIDTH = 80; // Corresponds to w-20
  const MAX_WIDTH = 320; // Example max width

  useEffect(() => {
    const checkScreenSize = () => {
      setIsSmallScreen(window.innerWidth < 1024);
    };

    const handleResize = () => {
      checkScreenSize();
      // Adjust sidebar width based on screen size, within min/max limits
      const newWidth = Math.max(MIN_WIDTH, Math.min(MAX_WIDTH, window.innerWidth * 0.15)); // Example: 15% of screen width
      setSidebarWidth(newWidth);
    };

    // Set initial screen size and sidebar width
    handleResize();

    // Add event listener for resize
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Determine the effective collapsed state
  // If it's a small screen, it's always collapsed. Otherwise, use user's preference.
  const isEffectivelyCollapsed = isSmallScreen || userCollapsedPreference;

  const handleLogout = () => {
    logout();
    router.push('/');
  };

  const navLinks = [
    { href: '/dashboard', label: 'Dashboard', icon: LayoutDashboard },
    { href: '/analytics', label: 'Analytics', icon: LineChart },
    { href: '/customers', label: 'Customers', icon: Users },
    { href: '/calls', label: 'Calls', icon: Phone },
    { href: '/jobs', label: 'Jobs', icon: Briefcase },
    { href: '/numbers', label: 'Numbers', icon: Phone },
    { href: '/agents', label: 'Agents', icon: Bot },
  ];

  return (
    <TooltipProvider delayDuration={0}>
      <aside
        style={{ width: isEffectivelyCollapsed ? MIN_WIDTH : sidebarWidth }}
        className={cn(
          'h-screen bg-background border-r border-border p-4 flex flex-col transition-all duration-300 ease-in-out',
        )}
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-10">
          <Link href="/" className="text-2xl font-bold text-primary">
            {isEffectivelyCollapsed ? 'FI' : 'Fieson'}
          </Link>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setUserCollapsedPreference(!userCollapsedPreference)}
                className="flex-shrink-0"
              >
                <ChevronLeft
                  className={cn(
                    'transition-transform duration-300',
                    isEffectivelyCollapsed && 'rotate-180',
                  )}
                />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right" className="bg-primary text-primary-foreground">
              <p>{isEffectivelyCollapsed ? 'Expand' : 'Collapse'}</p>
            </TooltipContent>
          </Tooltip>
        </div>

        {/* Navigation */}
        <nav className="space-y-2 flex-1">
          {navLinks.map((link) => (
            <Tooltip key={link.href}>
              <TooltipTrigger asChild>
                <Link
                  href={link.href}
                  className={cn(
                    'flex items-center px-4 py-2 rounded-lg text-foreground hover:bg-accent transition-colors duration-200',
                    pathname === link.href && 'bg-accent text-primary font-semibold',
                    isEffectivelyCollapsed ? 'justify-center' : 'space-x-3',
                  )}
                >
                  <link.icon className="flex-shrink-0" size={20} />
                  <span
                    className={cn(
                      'overflow-hidden transition-all',
                      isEffectivelyCollapsed ? 'w-0' : 'w-full',
                    )}
                  >
                    {link.label}
                  </span>
                </Link>
              </TooltipTrigger>
              {isEffectivelyCollapsed && (
                <TooltipContent side="right" className="bg-primary text-primary-foreground">
                  <p>{link.label}</p>
                </TooltipContent>
              )}
            </Tooltip>
          ))}
        </nav>

        {/* Footer */}
        <div className="border-t border-border pt-4">
          <Tooltip>
            <TooltipTrigger className="w-full">
              <div
                className={cn(
                  'flex items-center w-full',
                  isEffectivelyCollapsed ? 'justify-center' : 'space-x-2',
                )}
              >
                <Avatar>
                  <AvatarFallback className="bg-primary text-primary-foreground text-sm font-semibold">
                    {user?.full_name ? user.full_name.charAt(0).toUpperCase() : 'U'}
                  </AvatarFallback>
                </Avatar>
                <span
                  className={cn(
                    'text-foreground font-medium overflow-hidden transition-all',
                    isEffectivelyCollapsed ? 'w-0' : 'w-full',
                  )}
                >
                  {user?.full_name || 'Guest'}
                </span>
              </div>
            </TooltipTrigger>
            {isEffectivelyCollapsed && (
              <TooltipContent side="right" className="bg-primary text-primary-foreground">
                <p>{user?.full_name || 'Guest'}</p>
              </TooltipContent>
            )}
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                onClick={handleLogout}
                className={cn(
                  'flex items-center text-foreground hover:text-destructive transition-colors duration-200 font-medium w-full mt-2',
                  isEffectivelyCollapsed ? 'justify-center' : 'space-x-2',
                )}
              >
                <LogOut className="flex-shrink-0" size={20} />
                <span
                  className={cn(
                    'overflow-hidden transition-all',
                    isEffectivelyCollapsed ? 'w-0' : 'w-full',
                  )}
                >
                  Logout
                </span>
              </Button>
            </TooltipTrigger>
            {isEffectivelyCollapsed && (
              <TooltipContent side="right" className="bg-primary text-primary-foreground">
                <p>Logout</p>
              </TooltipContent>
            )}
          </Tooltip>
        </div>
      </aside>
    </TooltipProvider>
  );
};

export default Sidebar;
