'use client';

import React from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import {
  NavigationMenu,
  NavigationMenuList,
  NavigationMenuItem,
  NavigationMenuLink,
  navigationMenuTriggerStyle,
} from '@/components/ui/navigation-menu';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Menu } from 'lucide-react';
import { cn } from '@/lib/utils';

const Header: React.FC = () => {
  const { user } = useAuth();

  const navLinks = user ? (
    <></>
  ) : (
    <>
      <NavigationMenuItem>
        <NavigationMenuLink
          href="/login"
          className={cn(
            navigationMenuTriggerStyle(),
            'text-primary hover:text-primary-foreground transition-colors duration-200 font-medium',
          )}
        >
          Login
        </NavigationMenuLink>
      </NavigationMenuItem>
      <NavigationMenuItem>
        <Link href="/register">
          <Button className="px-4 py-2 rounded-lg font-medium transition-all duration-200 bg-primary text-primary-foreground hover:bg-primary/90">
            Get Started
          </Button>
        </Link>
      </NavigationMenuItem>
    </>
  );

  return (
    <header className="bg-background bg-opacity-80 backdrop-blur-md border-b border-border border-opacity-50 shadow-elegant sticky top-0 z-50">
      <div className="mx-auto px-4">
        <div className="flex justify-between items-center py-2">
          <div className="flex items-center space-x-6">
            <Link
              href="/"
              className="text-2xl font-bold text-primary hover:text-primary-foreground transition-colors duration-200"
            >
              Fieson
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-6">
            <NavigationMenu>
              <NavigationMenuList>
                {navLinks}
                <NavigationMenuItem>
                  <NavigationMenuLink
                    href="/docs"
                    className={cn(
                      navigationMenuTriggerStyle(),
                      'text-foreground hover:text-primary transition-colors duration-200 font-medium',
                    )}
                  >
                    Docs
                  </NavigationMenuLink>
                </NavigationMenuItem>
              </NavigationMenuList>
            </NavigationMenu>
          </div>

          {/* Mobile Navigation */}
          <div className="md:hidden flex items-center">
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon">
                  <Menu className="h-6 w-6" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-[250px] sm:w-[300px] bg-background">
                <nav className="flex flex-col gap-4 pt-6">
                  {user ? (
                    <></>
                  ) : (
                    <>
                      <Link href="/login">
                        <Button className="text-foreground hover:text-primary transition-colors duration-200 font-medium">
                          Login
                        </Button>
                      </Link>
                      <Link href="/register">
                        <Button className="w-full px-4 py-2 rounded-lg font-medium transition-all duration-200 bg-primary text-primary-foreground hover:bg-primary/90">
                          Get Started
                        </Button>
                      </Link>
                    </>
                  )}
                  <Link
                    href="/docs"
                    className="text-foreground hover:text-primary transition-colors duration-200 font-medium"
                  >
                    Docs
                  </Link>
                </nav>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
