'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import {
  Play,
  Square,
  RefreshCw,
  Code,
  Eye,
  Copy,
  Download,
  Upload,
  Settings,
  Brain,
  Database,
  Zap,
  Wrench,
  MessageSquare,
  Phone,
  Globe,
  CheckCircle,
  XCircle,
  AlertCircle,
  Clock,
  Activity
} from 'lucide-react';
import { toast } from 'react-hot-toast';

import { NodeConfig, NodeType } from '@/types/workflow'; // Consolidated import

interface NodeTesterProps {
  node: NodeConfig;
  onNodeChange: (node: NodeConfig) => void;
  onViewJson: (json: string) => void;
  onTestNode: () => void;
  isTestingNode?: boolean;
}

const NODE_TYPES = [
  { id: NodeType.PYTHON_FUNCTION, name: 'Python Function', icon: Code, description: 'Execute custom Python code' },
  { id: NodeType.LLM, name: 'LLM Node', icon: Brain, description: 'Large Language Model processing' },
  { id: NodeType.DSPY_MODULE, name: 'DSPy Module', icon: Zap, description: 'DSPy-powered AI module' },
  { id: NodeType.MEMORY, name: 'Memory Node', icon: Database, description: 'Memory storage and retrieval' },
  { id: NodeType.TOOL, name: 'Tool Node', icon: Wrench, description: 'External tool integration' },
  { id: NodeType.RAG, name: 'RAG Node', icon: Database, description: 'Retrieval Augmented Generation' },
  { id: 'n8n', name: 'N8N Workflow', icon: Globe, description: 'N8N workflow automation' } // 'n8n' is not in NodeType enum, keep as string for now
];

const DEPENDENCY_OPTIONS = [
  'llm_service',
  'db',
  'embedding_service',
  'vector_db',
  'memory_service',
  'tool_service',
  'web_search',
  'file_service',
  'n8n_service'
];

const NodeTester: React.FC<NodeTesterProps> = ({
  node,
  onNodeChange,
  onViewJson,
  onTestNode,
  isTestingNode = false
}) => {
  const [testInput, setTestInput] = useState('');
  const [testOutput, setTestOutput] = useState('');
  const [testHistory, setTestHistory] = useState<any[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const handleNodeUpdate = (updates: Partial<NodeConfig>) => {
    onNodeChange({ ...node, ...updates });
  };

  const handleParameterUpdate = (key: string, value: any) => {
    handleNodeUpdate({
      parameters: { ...node.parameters, [key]: value }
    });
  };

  const handleTestRun = async () => {
    if (!testInput.trim()) {
      toast.error('Please enter test input');
      return;
    }

    setIsRunning(true);
    try {
      // Simulate node execution
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const mockOutput = `Node "${node.name}" processed input: "${testInput}"\nType: ${node.type}\nResult: Success`;
      setTestOutput(mockOutput);
      
      // Add to test history
      const testResult = {
        id: Date.now(),
        input: testInput,
        output: mockOutput,
        timestamp: new Date().toISOString(),
        success: true,
        duration: 2000
      };
      
      setTestHistory(prev => [testResult, ...prev.slice(0, 9)]); // Keep last 10 tests
      toast.success('Node test completed successfully!');
    } catch (error) {
      toast.error('Node test failed');
    } finally {
      setIsRunning(false);
    }
  };

  const handleCopyJson = () => {
    const nodeJson = JSON.stringify(node, null, 2);
    navigator.clipboard.writeText(nodeJson);
    toast.success('Node JSON copied to clipboard!');
  };

  const handleExportNode = () => {
    const nodeData = {
      node: node,
      exported_at: new Date().toISOString(),
      version: '1.0'
    };
    
    const dataStr = JSON.stringify(nodeData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${node.name.replace(/\s+/g, '_').toLowerCase()}_node.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    toast.success('Node exported successfully!');
  };

  const getNodeIcon = (type: string) => {
    return NODE_TYPES.find(t => t.id === type)?.icon || Settings;
  };

  const NodeIcon = getNodeIcon(node.type);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <NodeIcon className="w-8 h-8 text-blue-500" />
          <div>
            <h2 className="text-2xl font-bold">{node.name}</h2>
            <p className="text-gray-600">Configure and test individual node</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-sm">
            {NODE_TYPES.find(t => t.id === node.type)?.name || node.type}
          </Badge>
          <Button variant="outline" onClick={handleCopyJson}>
            <Copy className="w-4 h-4 mr-2" />
            Copy JSON
          </Button>
          <Button variant="outline" onClick={handleExportNode}>
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" onClick={() => onViewJson(JSON.stringify(node, null, 2))}>
            <Code className="w-4 h-4 mr-2" />
            View JSON
          </Button>
        </div>
      </div>

      <Tabs defaultValue="configuration" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="configuration">Configuration</TabsTrigger>
          <TabsTrigger value="testing">Testing</TabsTrigger>
          <TabsTrigger value="history">Test History</TabsTrigger>
        </TabsList>

        {/* Configuration Tab */}
        <TabsContent value="configuration" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Basic Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="node-name">Node Name</Label>
                  <Input
                    id="node-name"
                    value={node.name}
                    onChange={(e) => handleNodeUpdate({ name: e.target.value })}
                    placeholder="Enter node name"
                  />
                </div>
                <div>
                  <Label htmlFor="node-type">Node Type</Label>
                  <Select
                    value={node.type}
                    onValueChange={(value) => handleNodeUpdate({ type: value as NodeType })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {NODE_TYPES.map((type) => (
                        <SelectItem key={type.id} value={type.id}>
                          <div className="flex items-center gap-2">
                            <type.icon className="w-4 h-4" />
                            {type.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="node-description">Description</Label>
                <Textarea
                  id="node-description"
                  value={node.description || ''}
                  onChange={(e) => handleNodeUpdate({ description: e.target.value })}
                  placeholder="Describe what this node does"
                  rows={2}
                />
              </div>

              {/* Function Path for Python Functions */}
              {node.type === NodeType.PYTHON_FUNCTION && (
                <div>
                  <Label htmlFor="function-path">Function Path</Label>
                  <Input
                    id="function-path"
                    value={node.functionPath || ''}
                    onChange={(e) => handleNodeUpdate({ functionPath: e.target.value })}
                    placeholder="orchestrator.nodes.custom.function_name"
                  />
                </div>
              )}

              {/* System Prompt for LLM nodes */}
              {node.type === NodeType.LLM && (
                <div>
                  <Label htmlFor="system-prompt">System Prompt</Label>
                  <Textarea
                    id="system-prompt"
                    value={node.system_prompt || ''}
                    onChange={(e) => handleNodeUpdate({ system_prompt: e.target.value })}
                    placeholder="You are a helpful AI assistant..."
                    rows={4}
                  />
                </div>
              )}

              {/* N8N Configuration */}
              {node.type === 'n8n' && (
                <div className="space-y-4">
                  <h4 className="font-medium">N8N Configuration</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="n8n-workflow-id">Workflow ID</Label>
                      <Input
                        id="n8n-workflow-id"
                        value={node.n8n_config?.workflow_id || ''}
                        onChange={(e) => handleNodeUpdate({
                          n8n_config: { ...node.n8n_config, workflow_id: e.target.value }
                        })}
                        placeholder="workflow-123"
                      />
                    </div>
                    <div>
                      <Label htmlFor="n8n-webhook-url">Webhook URL</Label>
                      <Input
                        id="n8n-webhook-url"
                        value={node.n8n_config?.webhook_url || ''}
                        onChange={(e) => handleNodeUpdate({
                          n8n_config: { ...node.n8n_config, webhook_url: e.target.value }
                        })}
                        placeholder="http://localhost:5678/webhook/..."
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="n8n-auth-token">Auth Token</Label>
                    <Input
                      id="n8n-auth-token"
                      type="password"
                      value={node.n8n_config?.auth_token || ''}
                      onChange={(e) => handleNodeUpdate({
                        n8n_config: { ...node.n8n_config, auth_token: e.target.value }
                      })}
                      placeholder="n8n auth token"
                    />
                  </div>
                </div>
              )}

              {/* Dependencies */}
              <div className="space-y-4">
                <h4 className="font-medium">Dependencies</h4>
                <div className="flex flex-wrap gap-2">
                  {DEPENDENCY_OPTIONS.map((dep) => (
                    <Button
                      key={dep}
                      size="sm"
                      variant={node.dependencies.includes(dep) ? "default" : "outline"}
                      onClick={() => {
                        const deps = node.dependencies.includes(dep)
                          ? node.dependencies.filter(d => d !== dep)
                          : [...node.dependencies, dep];
                        handleNodeUpdate({ dependencies: deps });
                      }}
                    >
                      {dep}
                    </Button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Testing Tab */}
        <TabsContent value="testing" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Test Input */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Play className="w-5 h-5" />
                  Test Input
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="test-input">Input Data</Label>
                  <Textarea
                    id="test-input"
                    value={testInput}
                    onChange={(e) => setTestInput(e.target.value)}
                    placeholder="Enter test input data..."
                    rows={6}
                  />
                </div>
                <Button
                  onClick={handleTestRun}
                  disabled={isRunning || !testInput.trim()}
                  className="w-full"
                >
                  {isRunning ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      Running Test...
                    </>
                  ) : (
                    <>
                      <Play className="w-4 h-4 mr-2" />
                      Run Test
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>

            {/* Test Output */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="w-5 h-5" />
                  Test Output
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-50 p-4 rounded-lg min-h-[200px]">
                  {testOutput ? (
                    <pre className="text-sm whitespace-pre-wrap">{testOutput}</pre>
                  ) : (
                    <div className="text-center text-gray-500 mt-8">
                      <Activity className="w-12 h-12 mx-auto mb-2 opacity-50" />
                      <p>Run a test to see output here</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Test History Tab */}
        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Test History</CardTitle>
            </CardHeader>
            <CardContent>
              {testHistory.length === 0 ? (
                <div className="text-center py-8">
                  <Clock className="w-12 h-12 mx-auto mb-2 text-gray-400" />
                  <p className="text-gray-600">No test history yet</p>
                  <p className="text-sm text-gray-500">Run some tests to see history here</p>
                </div>
              ) : (
                <ScrollArea className="h-96">
                  <div className="space-y-3">
                    {testHistory.map((test) => (
                      <div key={test.id} className="p-3 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            {test.success ? (
                              <CheckCircle className="w-4 h-4 text-green-500" />
                            ) : (
                              <XCircle className="w-4 h-4 text-red-500" />
                            )}
                            <span className="text-sm font-medium">
                              {new Date(test.timestamp).toLocaleString()}
                            </span>
                          </div>
                          <Badge variant="outline" className="text-xs">
                            {test.duration}ms
                          </Badge>
                        </div>
                        <div className="text-sm">
                          <div className="mb-1">
                            <span className="font-medium">Input:</span> {test.input}
                          </div>
                          <div>
                            <span className="font-medium">Output:</span> {test.output}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default NodeTester;
