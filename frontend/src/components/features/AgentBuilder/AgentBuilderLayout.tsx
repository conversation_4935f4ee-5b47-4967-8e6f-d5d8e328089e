// frontend/src/components/features/AgentBuilder/AgentBuilderLayout.tsx
'use client';

import React from 'react';
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '@/components/ui/resizable';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';

// Import sub-components
import NodePalette from '@/components/features/AgentBuilder/NodePalette';
import WorkflowCanvas from '@/components/features/AgentBuilder/WorkflowCanvas';
import WorkflowConfigPanel from '@/components/features/AgentBuilder/WorkflowConfigPanel';
import ChatInterface from '@/components/features/AgentBuilder/ChatInterface';
import TemplateManager from '@/components/features/AgentBuilder/TemplateManager';
import IntegrationTesting from '@/components/features/AgentBuilder/IntegrationTesting';
import DebugConsole from '@/components/features/AgentBuilder/DebugConsole';
import AgentAnalytics from '@/components/features/AgentBuilder/AgentAnalytics';
import AgentDeployment from '@/components/features/AgentBuilder/AgentDeployment';
import NodePropertiesPanel from '@/components/features/AgentBuilder/NodePropertiesPanel';
import AgentPropertiesPanel from '@/components/features/AgentBuilder/AgentPropertiesPanel';

// Import new enhanced components
import NodeTestingPanel from '@/components/features/AgentBuilder/NodeTestingPanel';
import EdgeEditor from '@/components/features/AgentBuilder/EdgeEditor';
import WorkflowValidationPanel from '@/components/features/AgentBuilder/WorkflowValidationPanel';

// Import types
import { NodeConfig, NodeType, ToolMetadata } from '@/types/workflow';

interface AgentBuilderLayoutProps {
  currentWorkflow: {
    id: string;
    name: string;
    description: string;
    nodes: NodeConfig[];
    connections: Record<string, any>;
    agent_type: string;
    category: string;
    tags: string[];
    version: string;
  };
  setCurrentWorkflow: React.Dispatch<React.SetStateAction<any>>;
  selectedNode: NodeConfig | null;
  setSelectedNode: (node: NodeConfig | null) => void;
  showNodeEditor: boolean;
  setShowNodeEditor: (show: boolean) => void;
  templateSearchTerm: string;
  selectedTemplateCategory: string;
  isExecuting: boolean;
  executionState: any;
  activeTab: string;
  setActiveTab: (tab: string) => void;
  availableNodeTypes: NodeType[];
  availableToolsMetadata: ToolMetadata[];
  templates: any[]; // AgentTemplate[]
  handleWorkflowChange: (workflow: {
    nodes: NodeConfig[];
    connections: Record<string, any>;
  }) => void;
  handleNodeSelect: (node: NodeConfig | null) => void;
  handleNodeEdit: (node: NodeConfig) => void;
  handleNodeSave: (updatedNode: NodeConfig) => void;
  handleNodeDelete: (nodeId: string) => void;
  handleLoadTemplate: (template: any) => void; // AgentTemplate
  handleSaveAsTemplate: (templateData: any) => void; // Partial<AgentTemplate>
}

const AgentBuilderLayout: React.FC<AgentBuilderLayoutProps> = ({
  currentWorkflow,
  setCurrentWorkflow,
  selectedNode,
  setSelectedNode,
  showNodeEditor,
  setShowNodeEditor,
  templateSearchTerm,
  selectedTemplateCategory,
  isExecuting,
  executionState,
  activeTab,
  setActiveTab,
  availableNodeTypes,
  availableToolsMetadata,
  templates,
  handleWorkflowChange,
  handleNodeSelect,
  handleNodeEdit,
  handleNodeSave,
  handleNodeDelete,
  handleLoadTemplate,
  handleSaveAsTemplate,
}) => {
  return (
    <div className="flex-1 overflow-hidden flex">
      <ResizablePanelGroup direction="horizontal" className="h-full flex-1">
        {/* Node Palette */}
        <ResizablePanel
          defaultSize={15}
          minSize={10}
          className="border-r bg-gray-50 overflow-hidden flex flex-col"
        >
          <div className="h-full flex flex-col">
            <NodePalette
              nodeTypes={availableNodeTypes}
              onNodeSelect={handleNodeSelect}
              availableToolsMetadata={availableToolsMetadata}
            />
          </div>
        </ResizablePanel>

        <ResizableHandle />

        {/* Canvas Area */}
        <ResizablePanel defaultSize={65} minSize={40} className="flex flex-col overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
            <div className="border-b px-4">
              <TabsList>
                <TabsTrigger value="canvas">Canvas</TabsTrigger>
                <TabsTrigger value="config">Configuration</TabsTrigger>
                <TabsTrigger value="validation">Validation</TabsTrigger>
                <TabsTrigger value="chat">Chat Test</TabsTrigger>
                <TabsTrigger value="templates">Templates</TabsTrigger>
                <TabsTrigger value="testing">Test</TabsTrigger>
                <TabsTrigger value="debug">Debug</TabsTrigger>
                <TabsTrigger value="analytics">Analytics</TabsTrigger>
                <TabsTrigger value="deployment">Deployment</TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="canvas" className="flex-1 m-0">
              <WorkflowCanvas
                workflow={currentWorkflow}
                onWorkflowChange={handleWorkflowChange}
                onNodeSelect={handleNodeSelect}
                onNodeEdit={handleNodeEdit}
                isExecuting={isExecuting}
                executionState={executionState}
              />
            </TabsContent>

            <TabsContent value="config" className="flex-1 p-4">
              <WorkflowConfigPanel
                currentWorkflow={currentWorkflow}
                setCurrentWorkflow={setCurrentWorkflow}
              />
            </TabsContent>

            <TabsContent value="validation" className="flex-1 m-0">
              <WorkflowValidationPanel workflow={currentWorkflow} />
            </TabsContent>

            <TabsContent value="chat" className="flex-1 m-0">
              <ChatInterface
                agentId={currentWorkflow.id}
                workflow={{
                  id: currentWorkflow.id,
                  name: currentWorkflow.name,
                  nodes: currentWorkflow.nodes.map((node) => ({
                    id: node.id,
                    name: node.name,
                    type: node.type,
                  })),
                }}
                isConnected={!isExecuting}
                onConnect={() => console.log('Chat connected')}
                onDisconnect={() => console.log('Chat disconnected')}
                onExecutionUpdate={(steps) => {
                  console.log('Execution steps:', steps);
                  // Update execution state for canvas visualization
                  // This logic might need to be lifted or passed down more carefully
                  // For now, directly updating executionState prop
                  // This is a simplified example, actual implementation might differ
                  // based on how executionState is managed in the parent.
                  if (executionState) {
                    executionState.currentNodeId = steps.find(
                      (s) => s.status === 'running',
                    )?.node_id;
                    executionState.executionPath = steps
                      .filter((s) => s.status === 'completed')
                      .map((s) => s.node_id);
                    executionState.nodeMetrics = steps.map((s) => ({
                      node_id: s.node_id,
                      status: s.status,
                      duration_ms: s.duration_ms,
                    }));
                  }
                }}
              />
            </TabsContent>

            <TabsContent value="templates" className="flex-1 m-0 p-4">
              <TemplateManager
                currentWorkflow={currentWorkflow}
                onLoadTemplate={handleLoadTemplate}
                onSaveAsTemplate={handleSaveAsTemplate}
              />
            </TabsContent>

            <TabsContent value="testing" className="flex-1 m-0 p-4">
              <IntegrationTesting
                workflow={currentWorkflow}
                onTestComplete={(results) => {
                  const passed = results.filter((r) => r.success).length;
                  const total = results.length;
                  console.log(`Tests completed: ${passed}/${total} passed`);
                }}
              />
            </TabsContent>

            <TabsContent value="debug" className="flex-1 m-0 p-4">
              <DebugConsole currentWorkflow={currentWorkflow} />
            </TabsContent>

            <TabsContent value="analytics" className="flex-1 m-0 p-4">
              <AgentAnalytics agentId={currentWorkflow.id} agentName={currentWorkflow.name} />
            </TabsContent>

            <TabsContent value="deployment" className="flex-1 m-0 p-4">
              <AgentDeployment
                workflow={currentWorkflow}
                onDeploymentComplete={(deploymentInfo) => {
                  console.log('Agent deployed successfully!', deploymentInfo);
                }}
              />
            </TabsContent>
          </Tabs>
        </ResizablePanel>

        <ResizableHandle />

        {/* Properties Panel */}
        <ResizablePanel defaultSize={15} minSize={10} className="border-l bg-white overflow-hidden">
          {selectedNode ? (
            <Tabs defaultValue="properties" className="h-full flex flex-col">
              <div className="border-b px-2 py-1">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="properties" className="text-xs">
                    Properties
                  </TabsTrigger>
                  <TabsTrigger value="testing" className="text-xs">
                    Testing
                  </TabsTrigger>
                </TabsList>
              </div>

              <TabsContent value="properties" className="flex-1 m-0 overflow-auto">
                <NodePropertiesPanel selectedNode={selectedNode} handleNodeEdit={handleNodeEdit} />
              </TabsContent>

              <TabsContent value="testing" className="flex-1 m-0 overflow-auto">
                <NodeTestingPanel selectedNode={selectedNode} />
              </TabsContent>
            </Tabs>
          ) : (
            <AgentPropertiesPanel
              currentWorkflow={currentWorkflow}
              setCurrentWorkflow={setCurrentWorkflow}
            />
          )}
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  );
};

export default AgentBuilderLayout;
