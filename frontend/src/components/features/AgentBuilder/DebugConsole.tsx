// frontend/src/components/features/AgentBuilder/DebugConsole.tsx
'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Play } from 'lucide-react';
import { toast } from 'react-hot-toast';

interface DebugConsoleProps {
  currentWorkflow: {
    nodes: Array<{ id: string; name: string; type: string }>;
  };
}

const DebugConsole: React.FC<DebugConsoleProps> = ({ currentWorkflow }) => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Debug Console</h3>
          <p className="text-sm text-gray-600">
            Debug your agent workflow with step-by-step execution and logging
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              // Clear debug logs
              toast.success('Debug logs cleared');
            }}
          >
            Clear Logs
          </Button>
          <Button
            size="sm"
            onClick={() => {
              // Start debug session
              toast.success('Debug session started');
            }}
          >
            <Play className="w-4 h-4 mr-2" />
            Start Debug
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Debug Input */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Debug Input</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label>Test Input</Label>
              <Textarea
                placeholder="Enter test input for debugging..."
                rows={4}
                className="mt-1"
              />
            </div>
            <div>
              <Label>Debug Mode</Label>
              <Select defaultValue="step">
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="step">Step-by-step</SelectItem>
                  <SelectItem value="breakpoint">Breakpoints</SelectItem>
                  <SelectItem value="full">Full execution</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Debug Output */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Debug Output</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-gray-900 text-green-400 p-4 rounded-md font-mono text-sm h-48 overflow-y-auto">
              <div className="space-y-1">
                <div>[DEBUG] Workflow initialized</div>
                <div>
                  [DEBUG] Starting node: {currentWorkflow.nodes[0]?.name || 'None'}
                </div>
                <div>[DEBUG] Waiting for input...</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Node Execution Status */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Node Execution Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {currentWorkflow.nodes.map((node, index) => (
              <div
                key={node.id}
                className="flex items-center justify-between p-3 border rounded-lg"
              >
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-sm font-medium">
                    {index + 1}
                  </div>
                  <div>
                    <div className="font-medium">{node.name}</div>
                    <div className="text-sm text-gray-600">{node.type}</div>
                  </div>
                </div>
                <Badge variant="outline">Pending</Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DebugConsole;
