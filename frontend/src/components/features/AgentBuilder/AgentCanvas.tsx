'use client';

import React, { useState, useCallback } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Plus,
  Trash2,
  Edit,
  Copy,
  ArrowRight,
  GitBranch,
  Play,
  Square,
  Settings,
  Brain,
  Database,
  Zap,
  Code,
  Wrench,
  Globe
} from 'lucide-react';
import { NodeConfig } from '@/components/features/NodeTester/NodeTester';

interface AgentNode extends NodeConfig {
  position: { x: number; y: number };
}

interface Connection {
  from: string;
  to: string;
  type: 'main' | 'conditional';
  condition?: string;
}

interface AgentCanvasProps {
  nodes: AgentNode[];
  connections: Connection[];
  onNodesChange: (nodes: AgentNode[]) => void;
  onConnectionsChange: (connections: Connection[]) => void;
  onNodeEdit: (node: AgentNode) => void;
  onNodeTest: (node: AgentNode) => void;
}

const NODE_ICONS = {
  pythonFunction: Code,
  llm: Brain,
  dspyModule: Zap,
  memory: Database,
  tool: Wrench,
  rag: Database,
  n8n: Globe
};

const AgentCanvas: React.FC<AgentCanvasProps> = ({
  nodes,
  connections,
  onNodesChange,
  onConnectionsChange,
  onNodeEdit,
  onNodeTest
}) => {
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [draggedNode, setDraggedNode] = useState<string | null>(null);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionStart, setConnectionStart] = useState<string | null>(null);

  const handleNodeDragStart = useCallback((nodeId: string, event: React.MouseEvent) => {
    const node = nodes.find(n => n.id === nodeId);
    if (!node) return;

    const rect = (event.target as HTMLElement).getBoundingClientRect();
    setDragOffset({
      x: event.clientX - rect.left,
      y: event.clientY - rect.top
    });
    setDraggedNode(nodeId);
  }, [nodes]);

  const handleNodeDrag = useCallback((event: React.MouseEvent) => {
    if (!draggedNode) return;

    const canvas = event.currentTarget as HTMLElement;
    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left - dragOffset.x;
    const y = event.clientY - rect.top - dragOffset.y;

    const updatedNodes = nodes.map(node =>
      node.id === draggedNode
        ? { ...node, position: { x: Math.max(0, x), y: Math.max(0, y) } }
        : node
    );
    onNodesChange(updatedNodes);
  }, [draggedNode, dragOffset, nodes, onNodesChange]);

  const handleNodeDragEnd = useCallback(() => {
    setDraggedNode(null);
    setDragOffset({ x: 0, y: 0 });
  }, []);

  const handleNodeClick = useCallback((nodeId: string) => {
    if (isConnecting && connectionStart && connectionStart !== nodeId) {
      // Create connection
      const newConnection: Connection = {
        from: connectionStart,
        to: nodeId,
        type: 'main'
      };
      onConnectionsChange([...connections, newConnection]);
      setIsConnecting(false);
      setConnectionStart(null);
    } else {
      setSelectedNode(nodeId);
    }
  }, [isConnecting, connectionStart, connections, onConnectionsChange]);

  const handleStartConnection = useCallback((nodeId: string) => {
    setIsConnecting(true);
    setConnectionStart(nodeId);
  }, []);

  const handleDeleteNode = useCallback((nodeId: string) => {
    const updatedNodes = nodes.filter(n => n.id !== nodeId);
    const updatedConnections = connections.filter(c => c.from !== nodeId && c.to !== nodeId);
    onNodesChange(updatedNodes);
    onConnectionsChange(updatedConnections);
  }, [nodes, connections, onNodesChange, onConnectionsChange]);

  const handleDuplicateNode = useCallback((nodeId: string) => {
    const node = nodes.find(n => n.id === nodeId);
    if (!node) return;

    const duplicatedNode: AgentNode = {
      ...node,
      id: `${node.id}_copy_${Date.now()}`,
      name: `${node.name} (Copy)`,
      position: {
        x: node.position.x + 50,
        y: node.position.y + 50
      }
    };

    onNodesChange([...nodes, duplicatedNode]);
  }, [nodes, onNodesChange]);

  const getNodeIcon = (type: string) => {
    return NODE_ICONS[type as keyof typeof NODE_ICONS] || Settings;
  };

  const getNodeColor = (type: string) => {
    const colors = {
      pythonFunction: 'bg-blue-100 border-blue-300 text-blue-800',
      llm: 'bg-purple-100 border-purple-300 text-purple-800',
      dspyModule: 'bg-green-100 border-green-300 text-green-800',
      memory: 'bg-orange-100 border-orange-300 text-orange-800',
      tool: 'bg-gray-100 border-gray-300 text-gray-800',
      rag: 'bg-indigo-100 border-indigo-300 text-indigo-800',
      n8n: 'bg-teal-100 border-teal-300 text-teal-800'
    };
    return colors[type as keyof typeof colors] || 'bg-gray-100 border-gray-300 text-gray-800';
  };

  const renderConnection = (connection: Connection) => {
    const fromNode = nodes.find(n => n.id === connection.from);
    const toNode = nodes.find(n => n.id === connection.to);
    
    if (!fromNode || !toNode) return null;

    const fromX = fromNode.position.x + 150; // Half node width
    const fromY = fromNode.position.y + 50;  // Half node height
    const toX = toNode.position.x + 150;
    const toY = toNode.position.y + 50;

    return (
      <g key={`${connection.from}-${connection.to}`}>
        <line
          x1={fromX}
          y1={fromY}
          x2={toX}
          y2={toY}
          stroke={connection.type === 'conditional' ? '#f59e0b' : '#3b82f6'}
          strokeWidth="2"
          markerEnd="url(#arrowhead)"
        />
        {connection.type === 'conditional' && (
          <text
            x={(fromX + toX) / 2}
            y={(fromY + toY) / 2 - 10}
            fill="#f59e0b"
            fontSize="12"
            textAnchor="middle"
          >
            {connection.condition || 'condition'}
          </text>
        )}
      </g>
    );
  };

  return (
    <div className="relative w-full h-[600px] bg-gray-50 border rounded-lg overflow-hidden">
      {/* Canvas */}
      <div
        className="relative w-full h-full"
        onMouseMove={handleNodeDrag}
        onMouseUp={handleNodeDragEnd}
        onMouseLeave={handleNodeDragEnd}
      >
        {/* SVG for connections */}
        <svg className="absolute inset-0 w-full h-full pointer-events-none">
          <defs>
            <marker
              id="arrowhead"
              markerWidth="10"
              markerHeight="7"
              refX="9"
              refY="3.5"
              orient="auto"
            >
              <polygon
                points="0 0, 10 3.5, 0 7"
                fill="#3b82f6"
              />
            </marker>
          </defs>
          {connections.map(renderConnection)}
        </svg>

        {/* Nodes */}
        {nodes.map((node) => {
          const NodeIcon = getNodeIcon(node.type);
          const isSelected = selectedNode === node.id;
          const isDragging = draggedNode === node.id;

          return (
            <div
              key={node.id}
              className={`absolute w-72 cursor-move transition-all duration-200 ${
                isDragging ? 'z-50 scale-105' : 'z-10'
              } ${isSelected ? 'ring-2 ring-blue-500' : ''}`}
              style={{
                left: node.position.x,
                top: node.position.y,
                transform: isDragging ? 'rotate(2deg)' : 'none'
              }}
              onMouseDown={(e) => handleNodeDragStart(node.id, e)}
              onClick={() => handleNodeClick(node.id)}
            >
              <Card className={`${getNodeColor(node.type)} shadow-lg hover:shadow-xl transition-shadow`}>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <NodeIcon className="w-5 h-5" />
                      <CardTitle className="text-sm font-medium">{node.name}</CardTitle>
                    </div>
                    <div className="flex items-center gap-1">
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-6 w-6 p-0"
                        onClick={(e) => {
                          e.stopPropagation();
                          onNodeEdit(node);
                        }}
                      >
                        <Edit className="w-3 h-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-6 w-6 p-0"
                        onClick={(e) => {
                          e.stopPropagation();
                          onNodeTest(node);
                        }}
                      >
                        <Play className="w-3 h-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-6 w-6 p-0"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDuplicateNode(node.id);
                        }}
                      >
                        <Copy className="w-3 h-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-6 w-6 p-0"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteNode(node.id);
                        }}
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                    {node.description || 'No description'}
                  </p>
                  <div className="flex items-center justify-between">
                    <Badge variant="outline" className="text-xs">
                      {node.type}
                    </Badge>
                    <div className="flex items-center gap-1">
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-6 text-xs px-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleStartConnection(node.id);
                        }}
                      >
                        <ArrowRight className="w-3 h-3 mr-1" />
                        Connect
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          );
        })}

        {/* Empty state */}
        {nodes.length === 0 && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <Brain className="w-16 h-16 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-medium text-gray-600 mb-2">No nodes yet</h3>
              <p className="text-gray-500 mb-4">Add your first node to start building your agent</p>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Add Node
              </Button>
            </div>
          </div>
        )}

        {/* Connection mode indicator */}
        {isConnecting && (
          <div className="absolute top-4 left-4 bg-blue-100 border border-blue-300 rounded-lg p-2">
            <div className="flex items-center gap-2 text-blue-800">
              <GitBranch className="w-4 h-4" />
              <span className="text-sm">Click another node to connect</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AgentCanvas;
