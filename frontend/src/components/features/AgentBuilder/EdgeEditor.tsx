// frontend/src/components/features/AgentBuilder/EdgeEditor.tsx
'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  GitBranch,
  ArrowRight,
  Settings,
  Trash2,
  Save,
  Code,
  Zap,
} from 'lucide-react';

import {
  NodeConfig,
  ConditionalConnection,
  MainConnection,
  ConnectionTarget,
} from '@/types/workflow';

interface EdgeEditorProps {
  sourceNode: NodeConfig;
  targetNode: NodeConfig;
  existingConnection?: MainConnection | { conditional: ConditionalConnection };
  onSave: (connection: MainConnection | { conditional: ConditionalConnection }) => void;
  onDelete?: () => void;
  onClose: () => void;
}

const EdgeEditor: React.FC<EdgeEditorProps> = ({
  sourceNode,
  targetNode,
  existingConnection,
  onSave,
  onDelete,
  onClose,
}) => {
  const [connectionType, setConnectionType] = useState<'main' | 'conditional'>(
    existingConnection && 'conditional' in existingConnection ? 'conditional' : 'main'
  );

  const [mainConnection, setMainConnection] = useState<MainConnection>({
    main: [{ node: targetNode.id, type: 'main', index: 0 }],
  });

  const [conditionalConnection, setConditionalConnection] = useState<ConditionalConnection>({
    type: 'aiBased',
    prompt: '',
    conditionFunctionPath: '',
    paths: {
      'True': [{ node: targetNode.id, type: 'conditional', index: 0 }],
      'False': [],
    },
  });

  const [activeTab, setActiveTab] = useState('basic');

  // Initialize state from existing connection
  useEffect(() => {
    if (existingConnection) {
      if ('main' in existingConnection) {
        setMainConnection(existingConnection);
        setConnectionType('main');
      } else if ('conditional' in existingConnection) {
        setConditionalConnection(existingConnection.conditional);
        setConnectionType('conditional');
      }
    }
  }, [existingConnection]);

  const handleSave = () => {
    if (connectionType === 'main') {
      onSave(mainConnection);
    } else {
      onSave({ conditional: conditionalConnection });
    }
  };

  const handleAddConditionPath = () => {
    const newCondition = `condition_${Object.keys(conditionalConnection.paths).length + 1}`;
    setConditionalConnection(prev => ({
      ...prev,
      paths: {
        ...prev.paths,
        [newCondition]: [{ node: targetNode.id, type: 'conditional', index: 0 }],
      },
    }));
  };

  const handleRemoveConditionPath = (condition: string) => {
    const { [condition]: removed, ...remainingPaths } = conditionalConnection.paths;
    setConditionalConnection(prev => ({
      ...prev,
      paths: remainingPaths,
    }));
  };

  const handleConditionChange = (oldCondition: string, newCondition: string) => {
    if (oldCondition === newCondition) return;
    
    const paths = { ...conditionalConnection.paths };
    paths[newCondition] = paths[oldCondition];
    delete paths[oldCondition];
    
    setConditionalConnection(prev => ({
      ...prev,
      paths,
    }));
  };

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {connectionType === 'conditional' ? (
              <GitBranch className="w-5 h-5" />
            ) : (
              <ArrowRight className="w-5 h-5" />
            )}
            <CardTitle>
              {connectionType === 'conditional' ? 'Conditional Edge' : 'Main Edge'}
            </CardTitle>
            <Badge variant="outline">
              {sourceNode.name} → {targetNode.name}
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            {onDelete && (
              <Button variant="destructive" size="sm" onClick={onDelete}>
                <Trash2 className="w-4 h-4" />
              </Button>
            )}
            <Button variant="outline" size="sm" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              <Save className="w-4 h-4 mr-1" />
              Save
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="basic">Basic</TabsTrigger>
            <TabsTrigger value="conditions">Conditions</TabsTrigger>
            <TabsTrigger value="advanced">Advanced</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <div>
              <Label>Connection Type</Label>
              <Select
                value={connectionType}
                onValueChange={(value: 'main' | 'conditional') => setConnectionType(value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="main">
                    <div className="flex items-center gap-2">
                      <ArrowRight className="w-4 h-4" />
                      Main Connection
                    </div>
                  </SelectItem>
                  <SelectItem value="conditional">
                    <div className="flex items-center gap-2">
                      <GitBranch className="w-4 h-4" />
                      Conditional Connection
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium">Source Node</span>
                <Badge variant="outline">{sourceNode.type}</Badge>
              </div>
              <div className="text-sm text-gray-600 mb-3">{sourceNode.name}</div>
              
              <div className="flex items-center justify-center my-4">
                <ArrowRight className="w-6 h-6 text-gray-400" />
              </div>
              
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium">Target Node</span>
                <Badge variant="outline">{targetNode.type}</Badge>
              </div>
              <div className="text-sm text-gray-600">{targetNode.name}</div>
            </div>

            {connectionType === 'conditional' && (
              <div>
                <Label>Condition Type</Label>
                <Select
                  value={conditionalConnection.type}
                  onValueChange={(value) =>
                    setConditionalConnection(prev => ({ ...prev, type: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="aiBased">
                      <div className="flex items-center gap-2">
                        <Zap className="w-4 h-4" />
                        AI-Based Condition
                      </div>
                    </SelectItem>
                    <SelectItem value="functionBased">
                      <div className="flex items-center gap-2">
                        <Code className="w-4 h-4" />
                        Function-Based Condition
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
          </TabsContent>

          <TabsContent value="conditions" className="space-y-4">
            {connectionType === 'conditional' && (
              <>
                {conditionalConnection.type === 'aiBased' && (
                  <div>
                    <Label htmlFor="condition-prompt">Condition Prompt</Label>
                    <Textarea
                      id="condition-prompt"
                      value={conditionalConnection.prompt || ''}
                      onChange={(e) =>
                        setConditionalConnection(prev => ({ ...prev, prompt: e.target.value }))
                      }
                      placeholder="Describe the condition for routing decisions..."
                      rows={4}
                    />
                  </div>
                )}

                {conditionalConnection.type === 'functionBased' && (
                  <div>
                    <Label htmlFor="condition-function">Condition Function Path</Label>
                    <Input
                      id="condition-function"
                      value={conditionalConnection.conditionFunctionPath || ''}
                      onChange={(e) =>
                        setConditionalConnection(prev => ({ 
                          ...prev, 
                          conditionFunctionPath: e.target.value 
                        }))
                      }
                      placeholder="orchestrator.conditions.check_condition"
                    />
                  </div>
                )}

                <div>
                  <div className="flex items-center justify-between mb-3">
                    <Label>Condition Paths</Label>
                    <Button size="sm" onClick={handleAddConditionPath}>
                      Add Path
                    </Button>
                  </div>

                  <div className="space-y-3">
                    {Object.entries(conditionalConnection.paths).map(([condition, targets]) => (
                      <div key={condition} className="p-3 border rounded-lg">
                        <div className="flex items-center gap-2 mb-2">
                          <Input
                            value={condition}
                            onChange={(e) => handleConditionChange(condition, e.target.value)}
                            className="flex-1"
                            placeholder="Condition name"
                          />
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => handleRemoveConditionPath(condition)}
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                        
                        <div className="text-sm text-gray-600">
                          Targets: {targets.length > 0 ? targets.map(t => t.node).join(', ') : 'None'}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </>
            )}

            {connectionType === 'main' && (
              <div className="p-4 bg-blue-50 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <ArrowRight className="w-4 h-4 text-blue-600" />
                  <span className="font-medium text-blue-800">Direct Connection</span>
                </div>
                <p className="text-sm text-blue-700">
                  This is a direct connection from {sourceNode.name} to {targetNode.name}. 
                  No conditions are required.
                </p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="advanced" className="space-y-4">
            <div>
              <Label>Connection Priority</Label>
              <Input
                type="number"
                min="0"
                max="100"
                defaultValue="50"
                placeholder="Priority (0-100)"
              />
            </div>

            <div>
              <Label>Connection Label</Label>
              <Input
                placeholder="Optional label for this connection"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch id="animated" />
              <Label htmlFor="animated">Animated Connection</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch id="enabled" defaultChecked />
              <Label htmlFor="enabled">Connection Enabled</Label>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default EdgeEditor;
