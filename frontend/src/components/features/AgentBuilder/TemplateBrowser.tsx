'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  Search,
  Filter,
  Star,
  Users,
  Download,
  Eye,
  Heart,
  TrendingUp,
  Clock,
  Sparkles,
  Grid,
  List,
  SortAsc,
  SortDesc
} from 'lucide-react';
import { toast } from 'react-hot-toast';

import { AgentTemplate } from '@/services/agent-builder/agentBuilderService';
import { apiClient } from '@/services/api/apiClient';

interface TemplateBrowserProps {
  onSelectTemplate?: (template: AgentTemplate) => void;
  onUseTemplate?: (template: AgentTemplate) => void;
  showActions?: boolean;
  compact?: boolean;
}

const TemplateBrowser: React.FC<TemplateBrowserProps> = ({
  onSelectTemplate,
  onUseTemplate,
  showActions = true,
  compact = false
}) => {
  const [templates, setTemplates] = useState<AgentTemplate[]>([]);
  const [filteredTemplates, setFilteredTemplates] = useState<AgentTemplate[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('usage_count');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [activeTab, setActiveTab] = useState('all');

  useEffect(() => {
    loadTemplates();
    loadCategories();
  }, []);

  useEffect(() => {
    filterAndSortTemplates();
  }, [templates, searchTerm, selectedCategory, sortBy, sortOrder, activeTab]);

  const loadTemplates = async () => {
    try {
      setLoading(true);
      const data = await apiClient.getAgentTemplates();
      setTemplates(data);
    } catch (error) {
      console.error('Failed to load templates:', error);
      toast.error('Failed to load templates');
    } finally {
      setLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      const data = await apiClient.getTemplateCategories();
      setCategories(data);
    } catch (error) {
      console.error('Failed to load categories:', error);
    }
  };

  const filterAndSortTemplates = () => {
    let filtered = [...templates];

    // Filter by tab
    if (activeTab === 'featured') {
      filtered = filtered.filter(t => t.rating && t.rating >= 4.0);
    } else if (activeTab === 'popular') {
      filtered = filtered.sort((a, b) => b.usage_count - a.usage_count).slice(0, 20);
    } else if (activeTab === 'recent') {
      filtered = filtered.sort((a, b) => 
        new Date(b.workflow.created_at || 0).getTime() - new Date(a.workflow.created_at || 0).getTime()
      ).slice(0, 20);
    }

    // Filter by search term
    if (searchTerm) {
      const search = searchTerm.toLowerCase();
      filtered = filtered.filter(template =>
        template.name.toLowerCase().includes(search) ||
        template.description.toLowerCase().includes(search) ||
        template.tags.some(tag => tag.toLowerCase().includes(search))
      );
    }

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(template => template.category === selectedCategory);
    }

    // Sort templates
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'rating':
          aValue = a.rating || 0;
          bValue = b.rating || 0;
          break;
        case 'usage_count':
          aValue = a.usage_count;
          bValue = b.usage_count;
          break;
        case 'created_at':
          aValue = new Date(a.workflow.created_at || 0);
          bValue = new Date(b.workflow.created_at || 0);
          break;
        default:
          return 0;
      }

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    setFilteredTemplates(filtered);
  };

  const handleUseTemplate = async (template: AgentTemplate) => {
    try {
      if (onUseTemplate) {
        onUseTemplate(template);
      } else {
        // Default behavior - track usage
        await apiClient.useTemplate(template.id);
        toast.success(`Using template: ${template.name}`);
      }
    } catch (error) {
      console.error('Failed to use template:', error);
      toast.error('Failed to use template');
    }
  };

  const handleRateTemplate = async (template: AgentTemplate, rating: number) => {
    try {
      await apiClient.rateTemplate(template.id, rating);
      toast.success('Rating submitted!');
      // Reload templates to get updated rating
      loadTemplates();
    } catch (error) {
      console.error('Failed to rate template:', error);
      toast.error('Failed to submit rating');
    }
  };

  const getComplexityBadge = (template: AgentTemplate) => {
    const nodeCount = template.workflow.nodes.length;
    if (nodeCount <= 2) return <Badge variant="secondary">Simple</Badge>;
    if (nodeCount <= 5) return <Badge variant="outline">Moderate</Badge>;
    return <Badge variant="destructive">Complex</Badge>;
  };

  const TemplateCard: React.FC<{ template: AgentTemplate }> = ({ template }) => (
    <Card className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${compact ? 'p-2' : ''}`}>
      <CardHeader className={compact ? 'pb-2' : ''}>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className={`${compact ? 'text-sm' : 'text-lg'} mb-1`}>
              {template.name}
            </CardTitle>
            <div className="flex items-center gap-2 mb-2">
              <Badge variant="outline" className="text-xs">
                {template.category.replace('_', ' ')}
              </Badge>
              {getComplexityBadge(template)}
              {template.rating && (
                <div className="flex items-center gap-1">
                  <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                  <span className="text-xs">{template.rating}</span>
                </div>
              )}
            </div>
          </div>
          {!compact && (
            <div className="flex items-center gap-1">
              <Users className="w-4 h-4 text-gray-400" />
              <span className="text-sm text-gray-600">{template.usage_count}</span>
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent className={compact ? 'pt-0' : ''}>
        <p className={`text-gray-600 mb-3 ${compact ? 'text-xs' : 'text-sm'} line-clamp-2`}>
          {template.description}
        </p>

        <div className="flex flex-wrap gap-1 mb-3">
          {template.tags.slice(0, compact ? 2 : 3).map((tag) => (
            <Badge key={tag} variant="secondary" className="text-xs">
              {tag}
            </Badge>
          ))}
          {template.tags.length > (compact ? 2 : 3) && (
            <Badge variant="secondary" className="text-xs">
              +{template.tags.length - (compact ? 2 : 3)}
            </Badge>
          )}
        </div>

        {showActions && (
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                size="sm"
                onClick={() => handleUseTemplate(template)}
              >
                Use Template
              </Button>
              {!compact && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onSelectTemplate?.(template)}
                >
                  <Eye className="w-4 h-4 mr-1" />
                  Preview
                </Button>
              )}
            </div>
            
            {!compact && (
              <div className="flex items-center gap-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star
                    key={star}
                    className={`w-4 h-4 cursor-pointer transition-colors ${
                      template.rating && star <= template.rating
                        ? 'fill-yellow-400 text-yellow-400'
                        : 'text-gray-300 hover:text-yellow-400'
                    }`}
                    onClick={() => handleRateTemplate(template, star)}
                  />
                ))}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );

  const TemplateListItem: React.FC<{ template: AgentTemplate }> = ({ template }) => (
    <div className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
      <div className="flex-1">
        <div className="flex items-center gap-3 mb-2">
          <h3 className="font-medium">{template.name}</h3>
          <Badge variant="outline" className="text-xs">
            {template.category.replace('_', ' ')}
          </Badge>
          {getComplexityBadge(template)}
          {template.rating && (
            <div className="flex items-center gap-1">
              <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
              <span className="text-sm">{template.rating}</span>
            </div>
          )}
        </div>
        <p className="text-sm text-gray-600 mb-2">{template.description}</p>
        <div className="flex items-center gap-4 text-xs text-gray-500">
          <span className="flex items-center gap-1">
            <Users className="w-3 h-3" />
            {template.usage_count} uses
          </span>
          <span>{template.workflow.nodes.length} nodes</span>
          <span>by {template.author}</span>
        </div>
      </div>
      
      {showActions && (
        <div className="flex items-center gap-2">
          <Button size="sm" variant="outline" onClick={() => onSelectTemplate?.(template)}>
            <Eye className="w-4 h-4 mr-1" />
            Preview
          </Button>
          <Button size="sm" onClick={() => handleUseTemplate(template)}>
            Use Template
          </Button>
        </div>
      )}
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Sparkles className="w-8 h-8 mx-auto mb-2 animate-pulse" />
          <p>Loading templates...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      {!compact && (
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold">Agent Templates</h2>
            <p className="text-gray-600">Discover and use pre-built agent workflows</p>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
            >
              {viewMode === 'grid' ? <List className="w-4 h-4" /> : <Grid className="w-4 h-4" />}
            </Button>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="flex flex-wrap items-center gap-4">
        <div className="flex-1 min-w-64">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              placeholder="Search templates..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="All Categories" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {categories.map((category) => (
              <SelectItem key={category.id} value={category.id}>
                {category.name} ({category.count})
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={sortBy} onValueChange={setSortBy}>
          <SelectTrigger className="w-40">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="usage_count">Most Used</SelectItem>
            <SelectItem value="rating">Highest Rated</SelectItem>
            <SelectItem value="name">Name</SelectItem>
            <SelectItem value="created_at">Newest</SelectItem>
          </SelectContent>
        </Select>

        <Button
          variant="outline"
          size="sm"
          onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
        >
          {sortOrder === 'asc' ? <SortAsc className="w-4 h-4" /> : <SortDesc className="w-4 h-4" />}
        </Button>
      </div>

      {/* Tabs */}
      {!compact && (
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="all">All Templates</TabsTrigger>
            <TabsTrigger value="featured">Featured</TabsTrigger>
            <TabsTrigger value="popular">Popular</TabsTrigger>
            <TabsTrigger value="recent">Recent</TabsTrigger>
          </TabsList>
        </Tabs>
      )}

      {/* Templates */}
      <div className="space-y-4">
        {filteredTemplates.length === 0 ? (
          <div className="text-center py-12">
            <Search className="w-12 h-12 mx-auto mb-3 text-gray-400" />
            <p className="text-gray-600">No templates found</p>
            <p className="text-sm text-gray-500">Try adjusting your search or filters</p>
          </div>
        ) : viewMode === 'grid' ? (
          <div className={`grid gap-4 ${compact ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'}`}>
            {filteredTemplates.map((template) => (
              <TemplateCard key={template.id} template={template} />
            ))}
          </div>
        ) : (
          <div className="space-y-2">
            {filteredTemplates.map((template) => (
              <TemplateListItem key={template.id} template={template} />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default TemplateBrowser;
