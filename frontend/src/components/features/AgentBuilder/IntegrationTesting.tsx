'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  Play,
  Pause,
  Square,
  RotateCcw,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  TrendingUp,
  BarChart3,
  FileText,
  Download,
  Upload,
  Plus,
  Trash2,
  Edit,
  Copy,
  Zap,
  Target,
  Activity
} from 'lucide-react';
import { toast } from 'react-hot-toast';

import { Workflow, NodeConfig } from '@/services/agent-builder/agentBuilderService';
import { testingService, TestCase, TestSuite, TestExecution, TestResult } from '@/services/testing/testingService';
import { apiClient } from '@/services/api/apiClient';

interface IntegrationTestingProps {
  workflow?: Workflow;
  onTestComplete?: (results: TestResult[]) => void;
}

const IntegrationTesting: React.FC<IntegrationTestingProps> = ({
  workflow,
  onTestComplete
}) => {
  const [testSuites, setTestSuites] = useState<TestSuite[]>([]);
  const [currentExecution, setCurrentExecution] = useState<TestExecution | null>(null);
  const [isRunning, setIsRunning] = useState(false);
  const [selectedSuite, setSelectedSuite] = useState<string>('');
  const [showCreateSuite, setShowCreateSuite] = useState(false);
  const [showCreateTest, setShowCreateTest] = useState(false);

  // Test suite creation form
  const [suiteForm, setSuiteForm] = useState({
    name: '',
    description: '',
    testCases: [] as TestCase[]
  });

  // Test case creation form
  const [testForm, setTestForm] = useState({
    name: '',
    description: '',
    input: '{}',
    expectedOutput: '',
    expectedBehavior: 'success' as 'success' | 'failure' | 'timeout',
    timeoutMs: 30000,
    tags: ''
  });

  // Performance testing state
  const [perfTestConfig, setPerfTestConfig] = useState({
    iterations: 10,
    concurrency: 1,
    testInput: '{}'
  });

  useEffect(() => {
    loadTestSuites();
  }, []);

  const loadTestSuites = () => {
    const suites = testingService.getAllTestSuites();
    setTestSuites(suites);
  };

  const handleCreateTestSuite = () => {
    try {
      const testSuite = testingService.createTestSuite(
        suiteForm.name,
        suiteForm.description,
        suiteForm.testCases,
        workflow?.id
      );
      
      toast.success('Test suite created successfully!');
      setShowCreateSuite(false);
      setSuiteForm({ name: '', description: '', testCases: [] });
      loadTestSuites();
    } catch (error) {
      console.error('Failed to create test suite:', error);
      toast.error('Failed to create test suite');
    }
  };

  const handleCreateTestCase = () => {
    try {
      const input = JSON.parse(testForm.input);
      const tags = testForm.tags.split(',').map(tag => tag.trim()).filter(Boolean);
      
      const testCase = testingService.createTestCase(
        testForm.name,
        testForm.description,
        input,
        testForm.expectedOutput || undefined,
        testForm.expectedBehavior,
        testForm.timeoutMs,
        tags
      );

      // Add to current suite form if creating a new suite
      if (showCreateSuite) {
        setSuiteForm(prev => ({
          ...prev,
          testCases: [...prev.testCases, testCase]
        }));
      }

      toast.success('Test case created successfully!');
      setShowCreateTest(false);
      setTestForm({
        name: '',
        description: '',
        input: '{}',
        expectedOutput: '',
        expectedBehavior: 'success',
        timeoutMs: 30000,
        tags: ''
      });
    } catch (error) {
      console.error('Failed to create test case:', error);
      toast.error('Failed to create test case - check your JSON input');
    }
  };

  const handleRunTestSuite = async (suiteId: string) => {
    if (!workflow) {
      toast.error('No workflow available for testing');
      return;
    }

    try {
      setIsRunning(true);
      const execution = await testingService.executeTestSuite(suiteId, workflow);
      setCurrentExecution(execution);
      
      if (onTestComplete && execution.results) {
        onTestComplete(execution.results);
      }
      
      toast.success(`Test suite completed: ${execution.summary.passed}/${execution.summary.total} passed`);
    } catch (error) {
      console.error('Failed to run test suite:', error);
      toast.error('Failed to run test suite');
    } finally {
      setIsRunning(false);
    }
  };

  const handleRunPerformanceTest = async () => {
    if (!workflow) {
      toast.error('No workflow available for testing');
      return;
    }

    try {
      setIsRunning(true);
      const input = JSON.parse(perfTestConfig.testInput);
      
      const results = await testingService.performanceTest(
        workflow,
        input,
        perfTestConfig.iterations,
        perfTestConfig.concurrency
      );
      
      toast.success(`Performance test completed: ${results.performance_metrics.success_rate.toFixed(1)}% success rate`);
      
      // Create a mock execution for display
      const execution: TestExecution = {
        id: `perf_${Date.now()}`,
        test_suite_id: 'performance',
        results: results.results,
        summary: {
          total: results.results.length,
          passed: results.results.filter(r => r.success).length,
          failed: results.results.filter(r => !r.success).length,
          success_rate: results.performance_metrics.success_rate,
          total_duration_ms: results.results.reduce((sum, r) => sum + r.execution_time_ms, 0),
          avg_duration_ms: results.performance_metrics.avg_duration_ms
        },
        started_at: new Date().toISOString(),
        completed_at: new Date().toISOString(),
        status: 'completed'
      };
      
      setCurrentExecution(execution);
    } catch (error) {
      console.error('Failed to run performance test:', error);
      toast.error('Failed to run performance test - check your JSON input');
    } finally {
      setIsRunning(false);
    }
  };

  const handleTestNode = async (node: NodeConfig) => {
    try {
      const testInput = { user_message: "Test message for node validation" };
      const result = await testingService.testNode(node, testInput);
      
      if (result.success) {
        toast.success(`Node ${node.name} test passed`);
      } else {
        toast.error(`Node ${node.name} test failed: ${result.error_message}`);
      }
    } catch (error) {
      console.error('Failed to test node:', error);
      toast.error('Failed to test node');
    }
  };

  const exportTestSuite = (suite: TestSuite) => {
    try {
      const exported = testingService.exportTestSuite(suite.id);
      const blob = new Blob([exported], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${suite.name.replace(/\s+/g, '_')}_test_suite.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      toast.success('Test suite exported successfully!');
    } catch (error) {
      console.error('Failed to export test suite:', error);
      toast.error('Failed to export test suite');
    }
  };

  const importTestSuite = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const text = await file.text();
      const suite = testingService.importTestSuite(text);
      toast.success('Test suite imported successfully!');
      loadTestSuites();
    } catch (error) {
      console.error('Failed to import test suite:', error);
      toast.error('Failed to import test suite');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
      case 'passed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'running':
        return <Clock className="w-4 h-4 text-blue-500 animate-spin" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-400" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Integration & Testing</h2>
          <p className="text-gray-600">Test your agents with comprehensive test suites</p>
        </div>
        <div className="flex items-center gap-2">
          <input
            type="file"
            accept=".json"
            onChange={importTestSuite}
            className="hidden"
            id="import-test-suite"
          />
          <Button
            variant="outline"
            onClick={() => document.getElementById('import-test-suite')?.click()}
          >
            <Upload className="w-4 h-4 mr-2" />
            Import
          </Button>
          <Button onClick={() => setShowCreateSuite(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Create Test Suite
          </Button>
        </div>
      </div>

      <Tabs defaultValue="test-suites" className="space-y-4">
        <TabsList>
          <TabsTrigger value="test-suites">Test Suites</TabsTrigger>
          <TabsTrigger value="node-testing">Node Testing</TabsTrigger>
          <TabsTrigger value="performance">Performance Testing</TabsTrigger>
          <TabsTrigger value="results">Results</TabsTrigger>
        </TabsList>

        {/* Test Suites Tab */}
        <TabsContent value="test-suites" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {testSuites.map((suite) => (
              <Card key={suite.id}>
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="text-lg">{suite.name}</CardTitle>
                      <p className="text-sm text-gray-600 mt-1">{suite.description}</p>
                    </div>
                    <Badge variant="outline">
                      {suite.test_cases.length} tests
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="text-sm text-gray-600">
                      Created: {new Date(suite.created_at).toLocaleDateString()}
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        onClick={() => handleRunTestSuite(suite.id)}
                        disabled={isRunning || !workflow}
                      >
                        <Play className="w-4 h-4 mr-1" />
                        Run Tests
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => exportTestSuite(suite)}
                      >
                        <Download className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Node Testing Tab */}
        <TabsContent value="node-testing" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Individual Node Testing</CardTitle>
              <p className="text-sm text-gray-600">Test individual nodes in your workflow</p>
            </CardHeader>
            <CardContent>
              {workflow?.nodes ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {workflow.nodes.map((node) => (
                    <Card key={node.id} className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">{node.name}</h4>
                          <p className="text-sm text-gray-600">{node.type}</p>
                        </div>
                        <Button
                          size="sm"
                          onClick={() => handleTestNode(node)}
                          disabled={isRunning}
                        >
                          <Zap className="w-4 h-4 mr-1" />
                          Test
                        </Button>
                      </div>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Target className="w-12 h-12 mx-auto mb-3 text-gray-400" />
                  <p className="text-gray-600">No workflow loaded</p>
                  <p className="text-sm text-gray-500">Load a workflow to test individual nodes</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Performance Testing Tab */}
        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Performance Testing</CardTitle>
              <p className="text-sm text-gray-600">Test your agent's performance under load</p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="iterations">Iterations</Label>
                  <Input
                    id="iterations"
                    type="number"
                    value={perfTestConfig.iterations}
                    onChange={(e) => setPerfTestConfig(prev => ({
                      ...prev,
                      iterations: parseInt(e.target.value) || 10
                    }))}
                    min="1"
                    max="100"
                  />
                </div>
                <div>
                  <Label htmlFor="concurrency">Concurrency</Label>
                  <Input
                    id="concurrency"
                    type="number"
                    value={perfTestConfig.concurrency}
                    onChange={(e) => setPerfTestConfig(prev => ({
                      ...prev,
                      concurrency: parseInt(e.target.value) || 1
                    }))}
                    min="1"
                    max="10"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="test-input">Test Input (JSON)</Label>
                <Textarea
                  id="test-input"
                  value={perfTestConfig.testInput}
                  onChange={(e) => setPerfTestConfig(prev => ({
                    ...prev,
                    testInput: e.target.value
                  }))}
                  rows={4}
                  placeholder='{"user_message": "Hello, how can you help me?"}'
                />
              </div>
              <Button
                onClick={handleRunPerformanceTest}
                disabled={isRunning || !workflow}
                className="w-full"
              >
                <TrendingUp className="w-4 h-4 mr-2" />
                Run Performance Test
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Results Tab */}
        <TabsContent value="results" className="space-y-4">
          {currentExecution ? (
            <div className="space-y-4">
              {/* Execution Summary */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    {getStatusIcon(currentExecution.status)}
                    Test Execution Results
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {currentExecution.summary.passed}
                      </div>
                      <div className="text-sm text-gray-600">Passed</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-red-600">
                        {currentExecution.summary.failed}
                      </div>
                      <div className="text-sm text-gray-600">Failed</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {currentExecution.summary.success_rate.toFixed(1)}%
                      </div>
                      <div className="text-sm text-gray-600">Success Rate</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600">
                        {currentExecution.summary.avg_duration_ms.toFixed(0)}ms
                      </div>
                      <div className="text-sm text-gray-600">Avg Duration</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Individual Test Results */}
              <Card>
                <CardHeader>
                  <CardTitle>Test Results</CardTitle>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-96">
                    <div className="space-y-2">
                      {currentExecution.results.map((result, index) => (
                        <div
                          key={result.test_case_id}
                          className="flex items-center justify-between p-3 border rounded-lg"
                        >
                          <div className="flex items-center gap-3">
                            {getStatusIcon(result.success ? 'passed' : 'failed')}
                            <div>
                              <div className="font-medium">Test {index + 1}</div>
                              <div className="text-sm text-gray-600">
                                {result.execution_time_ms}ms
                              </div>
                            </div>
                          </div>
                          {result.error_message && (
                            <Badge variant="destructive" className="text-xs">
                              {result.error_message}
                            </Badge>
                          )}
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>
            </div>
          ) : (
            <div className="text-center py-12">
              <BarChart3 className="w-12 h-12 mx-auto mb-3 text-gray-400" />
              <p className="text-gray-600">No test results yet</p>
              <p className="text-sm text-gray-500">Run a test suite to see results here</p>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Create Test Suite Dialog */}
      {showCreateSuite && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md max-h-[80vh] overflow-y-auto">
            <CardHeader>
              <CardTitle>Create Test Suite</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="suite-name">Suite Name</Label>
                <Input
                  id="suite-name"
                  value={suiteForm.name}
                  onChange={(e) => setSuiteForm(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter suite name"
                />
              </div>
              <div>
                <Label htmlFor="suite-description">Description</Label>
                <Textarea
                  id="suite-description"
                  value={suiteForm.description}
                  onChange={(e) => setSuiteForm(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe this test suite"
                  rows={3}
                />
              </div>
              <div>
                <div className="flex items-center justify-between mb-2">
                  <Label>Test Cases ({suiteForm.testCases.length})</Label>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowCreateTest(true)}
                  >
                    <Plus className="w-4 h-4 mr-1" />
                    Add Test
                  </Button>
                </div>
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {suiteForm.testCases.map((testCase, index) => (
                    <div key={testCase.id} className="flex items-center justify-between p-2 border rounded">
                      <span className="text-sm">{testCase.name}</span>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => setSuiteForm(prev => ({
                          ...prev,
                          testCases: prev.testCases.filter((_, i) => i !== index)
                        }))}
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowCreateSuite(false)}>
                  Cancel
                </Button>
                <Button
                  onClick={handleCreateTestSuite}
                  disabled={!suiteForm.name || !suiteForm.description}
                >
                  Create Suite
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Create Test Case Dialog */}
      {showCreateTest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-lg max-h-[80vh] overflow-y-auto">
            <CardHeader>
              <CardTitle>Create Test Case</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="test-name">Test Name</Label>
                <Input
                  id="test-name"
                  value={testForm.name}
                  onChange={(e) => setTestForm(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter test name"
                />
              </div>
              <div>
                <Label htmlFor="test-description">Description</Label>
                <Textarea
                  id="test-description"
                  value={testForm.description}
                  onChange={(e) => setTestForm(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe this test case"
                  rows={2}
                />
              </div>
              <div>
                <Label htmlFor="test-input">Input (JSON)</Label>
                <Textarea
                  id="test-input"
                  value={testForm.input}
                  onChange={(e) => setTestForm(prev => ({ ...prev, input: e.target.value }))}
                  placeholder='{"user_message": "Hello"}'
                  rows={3}
                />
              </div>
              <div>
                <Label htmlFor="expected-output">Expected Output (optional)</Label>
                <Textarea
                  id="expected-output"
                  value={testForm.expectedOutput}
                  onChange={(e) => setTestForm(prev => ({ ...prev, expectedOutput: e.target.value }))}
                  placeholder="Expected response"
                  rows={2}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="expected-behavior">Expected Behavior</Label>
                  <Select
                    value={testForm.expectedBehavior}
                    onValueChange={(value: 'success' | 'failure' | 'timeout') =>
                      setTestForm(prev => ({ ...prev, expectedBehavior: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="success">Success</SelectItem>
                      <SelectItem value="failure">Failure</SelectItem>
                      <SelectItem value="timeout">Timeout</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="timeout">Timeout (ms)</Label>
                  <Input
                    id="timeout"
                    type="number"
                    value={testForm.timeoutMs}
                    onChange={(e) => setTestForm(prev => ({
                      ...prev,
                      timeoutMs: parseInt(e.target.value) || 30000
                    }))}
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="test-tags">Tags (comma-separated)</Label>
                <Input
                  id="test-tags"
                  value={testForm.tags}
                  onChange={(e) => setTestForm(prev => ({ ...prev, tags: e.target.value }))}
                  placeholder="e.g., smoke, regression, integration"
                />
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowCreateTest(false)}>
                  Cancel
                </Button>
                <Button
                  onClick={handleCreateTestCase}
                  disabled={!testForm.name || !testForm.description}
                >
                  Create Test
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default IntegrationTesting;
