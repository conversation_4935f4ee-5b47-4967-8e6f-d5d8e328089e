// frontend/src/components/features/AgentBuilder/AgentHeader.tsx
'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Download, Save, Play, Code, ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface AgentHeaderProps {
  agentName: string;
  agentDescription: string;
  onShowTemplateDialog: () => void;
  onSaveWorkflow: () => void;
  onTestWorkflow: () => void;
  isExecuting: boolean;
  onShowAgentJsonDialog: () => void;
}

const AgentHeader: React.FC<AgentHeaderProps> = ({
  agentName,
  agentDescription,
  onShowTemplateDialog,
  onSaveWorkflow,
  onTestWorkflow,
  isExecuting,
  onShowAgentJsonDialog,
}) => {
  const router = useRouter();

  return (
    <div className="border-b bg-white px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{agentName}</h1>
            <p className="text-sm text-gray-600">{agentDescription}</p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={onShowTemplateDialog}>
            <Download className="w-4 h-4 mr-2" />
            Templates
          </Button>
          <Button variant="outline" onClick={onSaveWorkflow}>
            <Save className="w-4 h-4 mr-2" />
            Save
          </Button>
          <Button onClick={onTestWorkflow} disabled={isExecuting}>
            <Play className="w-4 h-4 mr-2" />
            {isExecuting ? 'Testing...' : 'Test'}
          </Button>
          <Button variant="outline" onClick={onShowAgentJsonDialog}>
            <Code className="w-4 h-4 mr-2" />
            View Agent JSON
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AgentHeader;
