// frontend/src/components/features/AgentBuilder/ReactFlow/CustomEdge.tsx
'use client';

import React from 'react';
import {
  EdgeProps,
  getBezierPath,
  EdgeLabelRenderer,
  BaseEdge,
} from 'reactflow';
import { ReactFlowEdgeData } from '@/types/workflow';

interface CustomEdgeProps extends EdgeProps {
  data?: ReactFlowEdgeData;
}

const CustomEdge: React.FC<CustomEdgeProps> = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  data,
  selected,
  animated,
  label,
}) => {
  const [edgePath, labelX, labelY] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  });

  const isConditional = data?.isConditional || !!label;
  const condition = data?.condition || label;

  // Determine edge styling based on type
  const edgeStyle = {
    strokeWidth: selected ? 3 : 2,
    stroke: isConditional 
      ? getConditionColor(condition as string)
      : (selected ? '#3b82f6' : '#6b7280'),
    strokeDasharray: isConditional ? '5,5' : undefined,
    ...style,
  };

  return (
    <>
      <BaseEdge
        path={edgePath}
        style={edgeStyle}
        className={animated ? 'animate-pulse' : ''}
      />
      
      {/* Edge Label */}
      {condition && (
        <EdgeLabelRenderer>
          <div
            style={{
              position: 'absolute',
              transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
              fontSize: 10,
              pointerEvents: 'all',
            }}
            className="nodrag nopan"
          >
            <div
              className={`
                px-2 py-1 rounded-full text-xs font-medium border-2 border-white shadow-sm
                ${getConditionBadgeColor(condition as string)}
              `}
            >
              {condition}
            </div>
          </div>
        </EdgeLabelRenderer>
      )}

      {/* Connection Point Indicators */}
      {selected && (
        <>
          <circle
            cx={sourceX}
            cy={sourceY}
            r={3}
            fill="#3b82f6"
            className="animate-pulse"
          />
          <circle
            cx={targetX}
            cy={targetY}
            r={3}
            fill="#3b82f6"
            className="animate-pulse"
          />
        </>
      )}
    </>
  );
};

// Helper functions for styling
const getConditionColor = (condition: string): string => {
  const colorMap: Record<string, string> = {
    'True': '#10b981',
    'False': '#ef4444',
    'None': '#6b7280',
    'default': '#f59e0b',
    'yes': '#10b981',
    'no': '#ef4444',
    'success': '#10b981',
    'error': '#ef4444',
    'retry': '#f59e0b',
  };
  return colorMap[condition] || '#8b5cf6';
};

const getConditionBadgeColor = (condition: string): string => {
  const colorMap: Record<string, string> = {
    'True': 'bg-green-100 text-green-800',
    'False': 'bg-red-100 text-red-800',
    'None': 'bg-gray-100 text-gray-800',
    'default': 'bg-yellow-100 text-yellow-800',
    'yes': 'bg-green-100 text-green-800',
    'no': 'bg-red-100 text-red-800',
    'success': 'bg-green-100 text-green-800',
    'error': 'bg-red-100 text-red-800',
    'retry': 'bg-yellow-100 text-yellow-800',
  };
  return colorMap[condition] || 'bg-purple-100 text-purple-800';
};

export default CustomEdge;
