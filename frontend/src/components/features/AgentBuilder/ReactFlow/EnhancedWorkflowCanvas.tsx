// frontend/src/components/features/AgentBuilder/ReactFlow/EnhancedWorkflowCanvas.tsx
'use client';

import React, { useCallback, useRef, useState, useEffect } from 'react';
import ReactFlow, {
  Node,
  Edge,
  addEdge,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  Connection,
  NodeTypes,
  EdgeTypes,
  ReactFlowProvider,
  useReactFlow,
  Panel,
  BackgroundVariant,
  ConnectionMode,
  OnConnect,
  OnNodesChange,
  OnEdgesChange,
  ReactFlowInstance,
} from 'reactflow';
import 'reactflow/dist/style.css';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Save,
  Download,
  Upload,
  Play,
  Square,
  RotateCcw,
  ZoomIn,
  ZoomOut,
  Maximize,
} from 'lucide-react';

import CustomNode from './CustomNode';
import CustomEdge from './CustomEdge';
import {
  NodeConfig,
  NodeType,
  Workflow,
  AgentExecutionState,
  ReactFlowNodeData,
  ReactFlowEdgeData,
} from '@/types/workflow';
import {
  transformWorkflowToReactFlow,
  transformReactFlowToWorkflow,
  getNodeColor,
} from '@/lib/agent-utils';

// Define node and edge types
const nodeTypes: NodeTypes = {
  custom: CustomNode,
};

const edgeTypes: EdgeTypes = {
  custom: CustomEdge,
};

interface EnhancedWorkflowCanvasProps {
  workflow: Workflow;
  onWorkflowChange: (workflow: Workflow) => void;
  onNodeSelect?: (node: NodeConfig | null) => void;
  onNodeEdit?: (node: NodeConfig) => void;
  onNodeTest?: (node: NodeConfig) => void;
  executionState?: AgentExecutionState;
  isExecuting?: boolean;
  onExecute?: () => void;
  onStop?: () => void;
  className?: string;
}

const EnhancedWorkflowCanvas: React.FC<EnhancedWorkflowCanvasProps> = ({
  workflow,
  onWorkflowChange,
  onNodeSelect,
  onNodeEdit,
  onNodeTest,
  executionState,
  isExecuting = false,
  onExecute,
  onStop,
  className = '',
}) => {
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const [reactFlowInstance, setReactFlowInstance] = useState<ReactFlowInstance | null>(null);

  // Transform workflow to React Flow format
  const { nodes: initialNodes, edges: initialEdges } = transformWorkflowToReactFlow(
    workflow,
    executionState
  );

  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

  // Update nodes and edges when workflow or execution state changes
  useEffect(() => {
    const { nodes: newNodes, edges: newEdges } = transformWorkflowToReactFlow(
      workflow,
      executionState
    );
    setNodes(newNodes);
    setEdges(newEdges);
  }, [workflow, executionState, setNodes, setEdges]);

  // Handle new connections
  const onConnect: OnConnect = useCallback(
    (connection: Connection) => {
      const newEdge: Edge<ReactFlowEdgeData> = {
        ...connection,
        id: `${connection.source}-${connection.target}-${Date.now()}`,
        type: 'custom',
        data: {
          isConditional: connection.sourceHandle !== 'main',
          condition: connection.sourceHandle !== 'main' ? connection.sourceHandle : undefined,
          sourceHandle: connection.sourceHandle || 'main',
          targetHandle: connection.targetHandle || 'input',
        },
      };

      setEdges((eds) => addEdge(newEdge, eds));
      
      // Update workflow
      const updatedWorkflow = transformReactFlowToWorkflow(
        nodes,
        [...edges, newEdge],
        workflow
      );
      onWorkflowChange(updatedWorkflow);
    },
    [nodes, edges, workflow, onWorkflowChange, setEdges]
  );

  // Handle node changes (position, selection, etc.)
  const handleNodesChange: OnNodesChange = useCallback(
    (changes) => {
      onNodesChange(changes);
      
      // Update workflow with new node positions
      const updatedNodes = nodes.map((node) => {
        const change = changes.find((c) => c.id === node.id && c.type === 'position');
        if (change && change.type === 'position' && change.position) {
          return { ...node, position: change.position };
        }
        return node;
      });

      const updatedWorkflow = transformReactFlowToWorkflow(updatedNodes, edges, workflow);
      onWorkflowChange(updatedWorkflow);
    },
    [nodes, edges, workflow, onNodesChange, onWorkflowChange]
  );

  // Handle edge changes
  const handleEdgesChange: OnEdgesChange = useCallback(
    (changes) => {
      onEdgesChange(changes);
      
      // Update workflow when edges are removed
      const updatedWorkflow = transformReactFlowToWorkflow(nodes, edges, workflow);
      onWorkflowChange(updatedWorkflow);
    },
    [nodes, edges, workflow, onEdgesChange, onWorkflowChange]
  );

  // Handle node selection
  const onNodeClick = useCallback(
    (event: React.MouseEvent, node: Node<ReactFlowNodeData>) => {
      if (onNodeSelect) {
        onNodeSelect(node.data.config);
      }
    },
    [onNodeSelect]
  );

  // Handle node double-click for editing
  const onNodeDoubleClick = useCallback(
    (event: React.MouseEvent, node: Node<ReactFlowNodeData>) => {
      if (onNodeEdit) {
        onNodeEdit(node.data.config);
      }
    },
    [onNodeEdit]
  );

  // Handle drag and drop from node palette
  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();

      if (!reactFlowInstance || !reactFlowWrapper.current) {
        return;
      }

      const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();
      const droppedData = event.dataTransfer.getData('application/reactflow');

      if (!droppedData) {
        return;
      }

      const nodeData = JSON.parse(droppedData);
      const position = reactFlowInstance.project({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      });

      const newNodeId = `${nodeData.type}_${Date.now()}`;
      const newNodeConfig: NodeConfig = {
        id: newNodeId,
        name: nodeData.name,
        type: nodeData.type as NodeType,
        parameters: nodeData.parameters || {},
        dependencies: nodeData.dependencies || [],
        position: [position.x, position.y],
        description: nodeData.description,
        category: nodeData.category,
        ui_config: {},
        tools: nodeData.type === NodeType.TOOL ? [nodeData.name] : [],
        system_prompt: nodeData.type === NodeType.LLM ? 'You are a helpful AI assistant.' : undefined,
      };

      const newNode: Node<ReactFlowNodeData> = {
        id: newNodeId,
        type: 'custom',
        position,
        data: {
          label: nodeData.name,
          type: nodeData.type as NodeType,
          description: nodeData.description,
          status: 'ready',
          config: newNodeConfig,
          isValid: true,
          errors: [],
        },
        sourcePosition: 'right' as const,
        targetPosition: 'left' as const,
      };

      setNodes((nds) => nds.concat(newNode));

      // Update workflow
      const updatedWorkflow = transformReactFlowToWorkflow(
        [...nodes, newNode],
        edges,
        workflow
      );
      onWorkflowChange(updatedWorkflow);
    },
    [reactFlowInstance, nodes, edges, workflow, onWorkflowChange, setNodes]
  );

  // Toolbar actions
  const handleFitView = () => {
    reactFlowInstance?.fitView({ padding: 0.1 });
  };

  const handleZoomIn = () => {
    reactFlowInstance?.zoomIn();
  };

  const handleZoomOut = () => {
    reactFlowInstance?.zoomOut();
  };

  const handleReset = () => {
    reactFlowInstance?.setViewport({ x: 0, y: 0, zoom: 1 });
  };

  return (
    <div className={`w-full h-full ${className}`} ref={reactFlowWrapper}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={handleNodesChange}
        onEdgesChange={handleEdgesChange}
        onConnect={onConnect}
        onNodeClick={onNodeClick}
        onNodeDoubleClick={onNodeDoubleClick}
        onDrop={onDrop}
        onDragOver={onDragOver}
        onInit={setReactFlowInstance}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        connectionMode={ConnectionMode.Loose}
        snapToGrid={true}
        snapGrid={[15, 15]}
        defaultViewport={{ x: 0, y: 0, zoom: 1 }}
        minZoom={0.1}
        maxZoom={2}
        attributionPosition="bottom-left"
        proOptions={{ hideAttribution: true }}
      >
        <Controls
          showZoom={true}
          showFitView={true}
          showInteractive={true}
          position="bottom-right"
        />
        
        <MiniMap
          nodeColor={(node) => {
            const nodeType = node.data?.config?.type;
            return nodeType ? getNodeColor(nodeType).split(' ')[0] : '#6b7280';
          }}
          nodeStrokeWidth={3}
          nodeBorderRadius={2}
          position="bottom-left"
          pannable={true}
          zoomable={true}
        />
        
        <Background 
          variant={BackgroundVariant.Dots} 
          gap={20} 
          size={1} 
          color="#e5e7eb" 
        />

        {/* Custom Toolbar */}
        <Panel position="top-right" className="flex items-center gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={handleFitView}
            className="h-8"
          >
            <Maximize className="w-4 h-4" />
          </Button>
          
          <Button
            size="sm"
            variant="outline"
            onClick={handleZoomIn}
            className="h-8"
          >
            <ZoomIn className="w-4 h-4" />
          </Button>
          
          <Button
            size="sm"
            variant="outline"
            onClick={handleZoomOut}
            className="h-8"
          >
            <ZoomOut className="w-4 h-4" />
          </Button>
          
          <Button
            size="sm"
            variant="outline"
            onClick={handleReset}
            className="h-8"
          >
            <RotateCcw className="w-4 h-4" />
          </Button>

          {onExecute && (
            <Button
              size="sm"
              onClick={isExecuting ? onStop : onExecute}
              className="h-8"
              variant={isExecuting ? "destructive" : "default"}
            >
              {isExecuting ? (
                <>
                  <Square className="w-4 h-4 mr-1" />
                  Stop
                </>
              ) : (
                <>
                  <Play className="w-4 h-4 mr-1" />
                  Execute
                </>
              )}
            </Button>
          )}
        </Panel>

        {/* Execution Status */}
        {executionState && (
          <Panel position="top-left" className="flex items-center gap-2">
            <Badge variant={
              executionState.status === 'running' ? 'default' :
              executionState.status === 'completed' ? 'secondary' :
              executionState.status === 'failed' ? 'destructive' : 'outline'
            }>
              {executionState.status}
            </Badge>
            
            {executionState.currentNodeId && (
              <Badge variant="outline">
                Current: {workflow.nodes.find(n => n.id === executionState.currentNodeId)?.name}
              </Badge>
            )}
          </Panel>
        )}
      </ReactFlow>
    </div>
  );
};

export default EnhancedWorkflowCanvas;
