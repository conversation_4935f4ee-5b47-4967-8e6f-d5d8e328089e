// frontend/src/components/features/AgentBuilder/ReactFlow/CustomNode.tsx
'use client';

import React from 'react';
import { <PERSON>le, Position, NodeProps } from 'reactflow';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Brain,
  Database,
  Zap,
  Wrench,
  GitBranch,
  Search,
  ArrowDown,
  ArrowUp,
  Code,
  Settings,
  Edit,
  Play,
  AlertTriangle,
  CheckCircle,
  Clock,
  XCircle,
} from 'lucide-react';
import { NodeType, ReactFlowNodeData } from '@/types/workflow';
import { getNodeColor } from '@/lib/agent-utils';

const getNodeIcon = (type: NodeType) => {
  const iconMap = {
    [NodeType.LLM]: Brain,
    [NodeType.MEMORY]: Database,
    [NodeType.DSPY_MODULE]: Zap,
    [NodeType.TOOL]: <PERSON><PERSON>,
    [NodeType.CONDITIONAL]: GitBranch,
    [NodeType.RAG]: Search,
    [NodeType.INPUT]: ArrowDown,
    [NodeType.OUTPUT]: ArrowUp,
    [NodeType.PYTHON_FUNCTION]: Code,
  };
  return iconMap[type] || Settings;
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'completed':
      return <CheckCircle className="w-3 h-3 text-green-500" />;
    case 'running':
      return <Clock className="w-3 h-3 text-blue-500 animate-spin" />;
    case 'failed':
      return <XCircle className="w-3 h-3 text-red-500" />;
    case 'pending':
      return <Clock className="w-3 h-3 text-yellow-500" />;
    default:
      return null;
  }
};

interface CustomNodeProps extends NodeProps {
  data: ReactFlowNodeData;
  selected: boolean;
}

const CustomNode: React.FC<CustomNodeProps> = ({ data, selected, id }) => {
  const NodeIcon = getNodeIcon(data.type);
  const hasErrors = data.errors && data.errors.length > 0;
  const isValid = data.isValid !== false;

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    // This will be handled by the parent component
    if (data.config.onEdit) {
      data.config.onEdit(data.config);
    }
  };

  const handleTest = (e: React.MouseEvent) => {
    e.stopPropagation();
    // This will be handled by the parent component
    if (data.config.onTest) {
      data.config.onTest(data.config);
    }
  };

  return (
    <div className="relative">
      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className="w-3 h-3 !bg-gray-400 border-2 border-white"
      />

      {/* Main Node Card */}
      <Card
        className={`
          min-w-[200px] max-w-[280px] cursor-pointer transition-all duration-200
          ${getNodeColor(data.type)}
          ${selected ? 'ring-2 ring-blue-500 shadow-lg scale-105' : 'hover:shadow-md'}
          ${hasErrors ? 'border-red-300' : ''}
        `}
      >
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 flex-1 min-w-0">
              <NodeIcon className="w-4 h-4 flex-shrink-0" />
              <span className="font-medium text-sm truncate">{data.label}</span>
              {getStatusIcon(data.status || 'ready')}
            </div>
            
            <div className="flex items-center gap-1 flex-shrink-0">
              {!isValid && (
                <AlertTriangle className="w-3 h-3 text-red-500" />
              )}
              <Badge variant="outline" className="text-xs">
                {data.type}
              </Badge>
            </div>
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          {data.description && (
            <p className="text-xs text-gray-600 mb-2 line-clamp-2">
              {data.description}
            </p>
          )}

          <div className="flex items-center justify-between text-xs">
            <span className="text-gray-500">
              {data.status === 'ready' ? 'Ready' : data.status}
            </span>
            {data.executionTime && (
              <span className="text-gray-500">{data.executionTime}ms</span>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-1 mt-2">
            <Button
              size="sm"
              variant="ghost"
              className="h-6 w-6 p-0 opacity-70 hover:opacity-100"
              onClick={handleEdit}
            >
              <Edit className="w-3 h-3" />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="h-6 w-6 p-0 opacity-70 hover:opacity-100"
              onClick={handleTest}
            >
              <Play className="w-3 h-3" />
            </Button>
          </div>

          {/* Error Display */}
          {hasErrors && (
            <div className="mt-2 p-1 bg-red-50 border border-red-200 rounded text-xs">
              <div className="text-red-700 font-medium">Errors:</div>
              {data.errors!.slice(0, 2).map((error, index) => (
                <div key={index} className="text-red-600">
                  • {error}
                </div>
              ))}
              {data.errors!.length > 2 && (
                <div className="text-red-500">
                  +{data.errors!.length - 2} more...
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Output Handles */}
      <Handle
        type="source"
        position={Position.Right}
        id="main"
        className="w-3 h-3 !bg-blue-500 border-2 border-white"
      />

      {/* Conditional Output Handles for Conditional Nodes */}
      {data.type === NodeType.CONDITIONAL && (
        <>
          <Handle
            type="source"
            position={Position.Right}
            id="True"
            style={{ top: '30%' }}
            className="w-3 h-3 !bg-green-500 border-2 border-white"
          />
          <Handle
            type="source"
            position={Position.Right}
            id="False"
            style={{ top: '70%' }}
            className="w-3 h-3 !bg-red-500 border-2 border-white"
          />
        </>
      )}
    </div>
  );
};

export default CustomNode;
