'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import {
  Brain,
  Database,
  Zap,
  Wrench as Tool,
  GitBranch,
  Search,
  ArrowDown,
  ArrowUp,
  Settings,
  Trash2,
  Copy,
  Play,
  Save,
} from 'lucide-react';

import {
  ToolMetadata,
  ToolParameterType,
  NodeConfig,
  MemoryConfig,
  DSPyConfig,
  NodeType as BackendNodeType,
} from '@/types/workflow';

interface NodeEditorProps {
  node: NodeConfig | null;
  onSave: (node: NodeConfig) => void;
  onDelete?: (nodeId: string) => void;
  onTest?: (node: NodeConfig) => void;
  onClose: () => void;
  availableNodeTypes: Array<{
    type: BackendNodeType;
    name: string;
    description: string;
    category: string;
    icon: string;
    parameters: Array<{
      name: string;
      type: string;
      description: string;
      required: boolean;
      default?: any;
      enum_values?: any[];
      min_value?: number;
      max_value?: number;
    }>;
  }>;
  availableToolsMetadata: ToolMetadata[];
}

const NodeEditor: React.FC<NodeEditorProps> = ({
  node,
  onSave,
  onDelete,
  onTest,
  onClose,
  availableNodeTypes,
  availableToolsMetadata,
}) => {
  const [editedNode, setEditedNode] = useState<NodeConfig>(
    node || {
      id: `node_${Date.now()}`,
      name: 'New Node',
      type: BackendNodeType.LLM,
      parameters: {},
      dependencies: [],
      description: '',
      category: 'AI',
      ui_config: {},
    },
  );

  const [activeTab, setActiveTab] = useState('basic');
  const [isValid, setIsValid] = useState(true);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  useEffect(() => {
    validateNode();
  }, [editedNode]);

  const validateNode = () => {
    const errors: string[] = [];

    if (!editedNode.name.trim()) {
      errors.push('Node name is required');
    }

    if (!editedNode.type) {
      errors.push('Node type is required');
    }

    if (editedNode.type === 'llm' && !editedNode.system_prompt?.trim()) {
      errors.push('System prompt is required for LLM nodes');
    }

    if (editedNode.type === 'dspyModule' && !editedNode.dspy_config?.signature) {
      errors.push('DSPy signature is required for DSPy nodes');
    }

    if (editedNode.type === 'memory' && !editedNode.memory_config?.type) {
      errors.push('Memory type is required for memory nodes');
    }

    if (editedNode.type === 'tool' && (!editedNode.tools || editedNode.tools.length === 0)) {
      errors.push('At least one tool must be selected for Tool nodes');
    }

    setValidationErrors(errors);
    setIsValid(errors.length === 0);
  };

  const handleSave = () => {
    if (isValid) {
      onSave(editedNode);
    }
  };

  const handleParameterChange = (paramName: string, value: any) => {
    setEditedNode((prev) => ({
      ...prev,
      parameters: {
        ...prev.parameters,
        [paramName]: value,
      },
    }));
  };

  const handleMemoryConfigChange = (field: string, value: any) => {
    setEditedNode((prev) => ({
      ...prev,
      memory_config: {
        ...prev.memory_config,
        [field]: value,
      } as MemoryConfig,
    }));
  };

  const handleDSPyConfigChange = (field: string, value: any) => {
    setEditedNode((prev) => ({
      ...prev,
      dspy_config: {
        ...prev.dspy_config,
        [field]: value,
      } as DSPyConfig,
    }));
  };

  const handleToolSelection = (toolName: string) => {
    setEditedNode((prev) => ({
      ...prev,
      tools: [toolName], // Assuming single tool selection for now
      parameters: {}, // Clear existing parameters when tool changes
    }));
  };

  const handleToolParameterChange = (toolParamName: string, value: any) => {
    setEditedNode((prev) => ({
      ...prev,
      parameters: {
        ...prev.parameters,
        [toolParamName]: value,
      },
    }));
  };

  const getNodeTypeIcon = (type: string) => {
    switch (type) {
      case 'llm':
        return <Brain className="w-4 h-4" />;
      case 'memory':
        return <Database className="w-4 h-4" />;
      case 'dspyModule':
        return <Zap className="w-4 h-4" />;
      case 'tool':
        return <Tool className="w-4 h-4" />;
      case 'conditional':
        return <GitBranch className="w-4 h-4" />;
      case 'rag':
        return <Search className="w-4 h-4" />;
      case 'input':
        return <ArrowDown className="w-4 h-4" />;
      case 'output':
        return <ArrowUp className="w-4 h-4" />;
      default:
        return <Settings className="w-4 h-4" />;
    }
  };

  const selectedNodeType = availableNodeTypes.find((t) => t.type === editedNode.type);
  const selectedToolMetadata = availableToolsMetadata.find(
    (tool) => tool.name === editedNode.tools?.[0],
  );

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {getNodeTypeIcon(editedNode.type)}
            <CardTitle>{node ? 'Edit Node' : 'Create Node'}</CardTitle>
            <Badge variant="outline">{editedNode.type}</Badge>
          </div>
          <div className="flex items-center gap-2">
            {onTest && (
              <Button variant="outline" size="sm" onClick={() => onTest(editedNode)}>
                <Play className="w-4 h-4 mr-1" />
                Test
              </Button>
            )}
            {onDelete && node && (
              <Button variant="destructive" size="sm" onClick={() => onDelete(node.id)}>
                <Trash2 className="w-4 h-4" />
              </Button>
            )}
            <Button variant="outline" size="sm" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={handleSave} disabled={!isValid}>
              <Save className="w-4 h-4 mr-1" />
              Save
            </Button>
          </div>
        </div>
        {validationErrors.length > 0 && (
          <div className="text-sm text-red-600">
            {validationErrors.map((error, index) => (
              <div key={index}>• {error}</div>
            ))}
          </div>
        )}
      </CardHeader>

      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="basic">Basic</TabsTrigger>
            <TabsTrigger value="advanced">Advanced</TabsTrigger>
            <TabsTrigger value="memory">Memory</TabsTrigger>
            <TabsTrigger value="dspy">DSPy</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Node Name *</Label>
                <Input
                  id="name"
                  value={editedNode.name}
                  onChange={(e) => setEditedNode((prev) => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter node name"
                />
              </div>
              <div>
                <Label htmlFor="type">Node Type *</Label>
                <Select
                  value={editedNode.type}
                  onValueChange={(value) => setEditedNode((prev) => ({ ...prev, type: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select node type" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableNodeTypes.map((nodeType) => (
                      <SelectItem key={nodeType.type} value={nodeType.type}>
                        <div className="flex items-center gap-2">
                          {getNodeTypeIcon(nodeType.type)}
                          <span>{nodeType.name}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={editedNode.description || ''}
                onChange={(e) =>
                  setEditedNode((prev) => ({ ...prev, description: e.target.value }))
                }
                placeholder="Describe what this node does"
                rows={2}
              />
            </div>

            {editedNode.type === 'llm' && (
              <div>
                <Label htmlFor="system_prompt">System Prompt *</Label>
                <Textarea
                  id="system_prompt"
                  value={editedNode.system_prompt || ''}
                  onChange={(e) =>
                    setEditedNode((prev) => ({ ...prev, system_prompt: e.target.value }))
                  }
                  placeholder="You are a helpful AI assistant..."
                  rows={6}
                />
              </div>
            )}

            {editedNode.type === 'tool' && (
              <div className="space-y-4">
                <Label htmlFor="tool-select">Select Tool *</Label>
                <Select value={editedNode.tools?.[0] || ''} onValueChange={handleToolSelection}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a tool" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableToolsMetadata.map((tool) => (
                      <SelectItem key={tool.name} value={tool.name}>
                        {tool.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {selectedToolMetadata && (
                  <div className="space-y-3 mt-4">
                    <h4 className="font-medium">Tool Parameters</h4>
                    <p className="text-sm text-gray-600">{selectedToolMetadata.description}</p>
                    {selectedToolMetadata.parameters.length === 0 && (
                      <p className="text-sm text-gray-500">No parameters for this tool.</p>
                    )}
                    {selectedToolMetadata.parameters.map((param) => (
                      <div key={param.name} className="grid grid-cols-3 gap-2 items-center">
                        <Label className="text-sm">
                          {param.name} {param.required && '*'}
                        </Label>
                        <div className="col-span-2">
                          {param.type === ToolParameterType.STRING && !param.enum_values && (
                            <Input
                              value={editedNode.parameters[param.name] || param.default || ''}
                              onChange={(e) =>
                                handleToolParameterChange(param.name, e.target.value)
                              }
                              placeholder={param.description}
                            />
                          )}
                          {param.type === ToolParameterType.STRING && param.enum_values && (
                            <Select
                              value={editedNode.parameters[param.name] || param.default}
                              onValueChange={(value) =>
                                handleToolParameterChange(param.name, value)
                              }
                            >
                              <SelectTrigger>
                                <SelectValue placeholder={param.description} />
                              </SelectTrigger>
                              <SelectContent>
                                {param.enum_values.map((value) => (
                                  <SelectItem key={value} value={value}>
                                    {value}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          )}
                          {param.type === ToolParameterType.INTEGER && (
                            <Input
                              type="number"
                              value={editedNode.parameters[param.name] || param.default || 0}
                              onChange={(e) =>
                                handleToolParameterChange(param.name, parseInt(e.target.value))
                              }
                              min={param.min_value}
                              max={param.max_value}
                            />
                          )}
                          {param.type === ToolParameterType.FLOAT && (
                            <Input
                              type="number"
                              step="0.1"
                              value={editedNode.parameters[param.name] || param.default || 0}
                              onChange={(e) =>
                                handleToolParameterChange(param.name, parseFloat(e.target.value))
                              }
                              min={param.min_value}
                              max={param.max_value}
                            />
                          )}
                          {param.type === ToolParameterType.BOOLEAN && (
                            <Switch
                              checked={editedNode.parameters[param.name] || param.default || false}
                              onCheckedChange={(checked) =>
                                handleToolParameterChange(param.name, checked)
                              }
                            />
                          )}
                          {(param.type === ToolParameterType.OBJECT ||
                            param.type === ToolParameterType.ARRAY) && (
                            <Textarea
                              value={JSON.stringify(
                                editedNode.parameters[param.name] || param.default || {},
                                null,
                                2,
                              )}
                              onChange={(e) => {
                                try {
                                  handleToolParameterChange(param.name, JSON.parse(e.target.value));
                                } catch (err) {
                                  // Invalid JSON, do not update
                                  console.error('Invalid JSON for parameter', param.name, err);
                                }
                              }}
                              placeholder={param.description}
                              rows={3}
                            />
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {selectedNodeType && editedNode.type !== 'llm' && editedNode.type !== 'tool' && (
              <div>
                <Label>Parameters</Label>
                <div className="space-y-3 mt-2">
                  {selectedNodeType.parameters.map((param) => (
                    <div key={param.name} className="grid grid-cols-3 gap-2 items-center">
                      <Label className="text-sm">{param.name}</Label>
                      <div className="col-span-2">
                        {param.type === 'string' && !param.enum_values && (
                          <Input
                            value={editedNode.parameters[param.name] || param.default || ''}
                            onChange={(e) => handleParameterChange(param.name, e.target.value)}
                            placeholder={param.description}
                          />
                        )}
                        {param.type === 'string' && param.enum_values && (
                          <Select
                            value={editedNode.parameters[param.name] || param.default}
                            onValueChange={(value) => handleParameterChange(param.name, value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder={param.description} />
                            </SelectTrigger>
                            <SelectContent>
                              {param.enum_values.map((value) => (
                                <SelectItem key={value} value={value}>
                                  {value}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}
                        {param.type === 'integer' && (
                          <Input
                            type="number"
                            value={editedNode.parameters[param.name] || param.default || 0}
                            onChange={(e) =>
                              handleParameterChange(param.name, parseInt(e.target.value))
                            }
                            min={param.min_value}
                            max={param.max_value}
                          />
                        )}
                        {param.type === 'float' && (
                          <Input
                            type="number"
                            step="0.1"
                            value={editedNode.parameters[param.name] || param.default || 0}
                            onChange={(e) =>
                              handleParameterChange(param.name, parseFloat(e.target.value))
                            }
                            min={param.min_value}
                            max={param.max_value}
                          />
                        )}
                        {param.type === 'boolean' && (
                          <Switch
                            checked={editedNode.parameters[param.name] || param.default || false}
                            onCheckedChange={(checked) =>
                              handleParameterChange(param.name, checked)
                            }
                          />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="advanced" className="space-y-4">
            <div>
              <Label htmlFor="function_path">Function Path</Label>
              <Input
                id="function_path"
                value={editedNode.functionPath || ''}
                onChange={(e) =>
                  setEditedNode((prev) => ({ ...prev, functionPath: e.target.value }))
                }
                placeholder="orchestrator.nodes.llm.generate_response"
              />
            </div>

            <div>
              <Label>Dependencies</Label>
              <div className="flex flex-wrap gap-2 mt-2">
                {['llm_service', 'db', 'embedding_service', 'settings'].map((dep) => (
                  <div key={dep} className="flex items-center space-x-2">
                    <Switch
                      checked={editedNode.dependencies.includes(dep)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setEditedNode((prev) => ({
                            ...prev,
                            dependencies: [...prev.dependencies, dep],
                          }));
                        } else {
                          setEditedNode((prev) => ({
                            ...prev,
                            dependencies: prev.dependencies.filter((d) => d !== dep),
                          }));
                        }
                      }}
                    />
                    <Label className="text-sm">{dep}</Label>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <Label htmlFor="category">Category</Label>
              <Select
                value={editedNode.category || ''}
                onValueChange={(value) => setEditedNode((prev) => ({ ...prev, category: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="AI">AI</SelectItem>
                  <SelectItem value="Data">Data</SelectItem>
                  <SelectItem value="Logic">Logic</SelectItem>
                  <SelectItem value="Integration">Integration</SelectItem>
                  <SelectItem value="IO">Input/Output</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </TabsContent>

          <TabsContent value="memory" className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch
                checked={!!editedNode.memory_config}
                onCheckedChange={(checked) => {
                  if (checked) {
                    setEditedNode((prev) => ({
                      ...prev,
                      memory_config: { type: 'short_term' },
                    }));
                  } else {
                    setEditedNode((prev) => ({
                      ...prev,
                      memory_config: undefined,
                    }));
                  }
                }}
              />
              <Label>Enable Memory</Label>
            </div>

            {editedNode.memory_config && (
              <div className="space-y-4">
                <div>
                  <Label>Memory Type</Label>
                  <Select
                    value={editedNode.memory_config.type}
                    onValueChange={(value) => handleMemoryConfigChange('type', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="short_term">Short Term</SelectItem>
                      <SelectItem value="long_term">Long Term</SelectItem>
                      <SelectItem value="episodic">Episodic</SelectItem>
                      <SelectItem value="semantic">Semantic</SelectItem>
                      <SelectItem value="working">Working</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Max Entries</Label>
                  <Input
                    type="number"
                    value={editedNode.memory_config.max_entries || 100}
                    onChange={(e) =>
                      handleMemoryConfigChange('max_entries', parseInt(e.target.value))
                    }
                  />
                </div>

                <div>
                  <Label>Similarity Threshold</Label>
                  <Input
                    type="number"
                    step="0.1"
                    min="0"
                    max="1"
                    value={editedNode.memory_config.similarity_threshold || 0.7}
                    onChange={(e) =>
                      handleMemoryConfigChange('similarity_threshold', parseFloat(e.target.value))
                    }
                  />
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="dspy" className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch
                checked={!!editedNode.dspy_config}
                onCheckedChange={(checked) => {
                  if (checked) {
                    setEditedNode((prev) => ({
                      ...prev,
                      dspy_config: {
                        module_type: 'predict',
                        signature: 'input -> output',
                        examples: [],
                      },
                    }));
                  } else {
                    setEditedNode((prev) => ({
                      ...prev,
                      dspy_config: undefined,
                    }));
                  }
                }}
              />
              <Label>Enable DSPy</Label>
            </div>

            {editedNode.dspy_config && (
              <div className="space-y-4">
                <div>
                  <Label>Module Type</Label>
                  <Select
                    value={editedNode.dspy_config.module_type}
                    onValueChange={(value) => handleDSPyConfigChange('module_type', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="predict">Predict</SelectItem>
                      <SelectItem value="chain_of_thought">Chain of Thought</SelectItem>
                      <SelectItem value="react">ReAct</SelectItem>
                      <SelectItem value="retrieve">Retrieve</SelectItem>
                      <SelectItem value="generate">Generate</SelectItem>
                      <SelectItem value="classify">Classify</SelectItem>
                      <SelectItem value="summarize">Summarize</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Signature</Label>
                  <Input
                    value={editedNode.dspy_config.signature}
                    onChange={(e) => handleDSPyConfigChange('signature', e.target.value)}
                    placeholder="input -> output"
                  />
                </div>

                <div>
                  <Label>Max Bootstrapped Demos</Label>
                  <Input
                    type="number"
                    value={editedNode.dspy_config.max_bootstrapped_demos || 4}
                    onChange={(e) =>
                      handleDSPyConfigChange('max_bootstrapped_demos', parseInt(e.target.value))
                    }
                  />
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default NodeEditor;
