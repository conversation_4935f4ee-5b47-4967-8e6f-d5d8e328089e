// frontend/src/components/features/AgentBuilder/WorkflowConfigPanel.tsx
'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { Workflow as BuilderWorkflow } from '@/types/workflow';

interface WorkflowConfigPanelProps {
  currentWorkflow: BuilderWorkflow;
  setCurrentWorkflow: React.Dispatch<React.SetStateAction<BuilderWorkflow>>;
}

const WorkflowConfigPanel: React.FC<WorkflowConfigPanelProps> = ({
  currentWorkflow,
  setCurrentWorkflow,
}) => {
  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Workflow Configuration</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label>Name</Label>
              <Input
                key={`config-name-${currentWorkflow.id}`}
                value={currentWorkflow.name}
                onChange={(e) =>
                  setCurrentWorkflow((prev) => ({ ...prev, name: e.target.value }))
                }
              />
            </div>
            <div>
              <Label>Category</Label>
              <Select
                value={currentWorkflow.category}
                onValueChange={(value) =>
                  setCurrentWorkflow((prev) => ({ ...prev, category: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="customer_service">Customer Service</SelectItem>
                  <SelectItem value="sales">Sales</SelectItem>
                  <SelectItem value="support">Support</SelectItem>
                  <SelectItem value="analysis">Analysis</SelectItem>
                  <SelectItem value="custom">Custom</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div>
            <Label>Description</Label>
            <Textarea
              key={`config-desc-${currentWorkflow.id}`}
              value={currentWorkflow.description}
              onChange={(e) =>
                setCurrentWorkflow((prev) => ({ ...prev, description: e.target.value }))
              }
              rows={3}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default WorkflowConfigPanel;
