'use client';

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Brain,
  Database,
  Zap,
  Wrench,
  GitBranch,
  Search,
  ArrowDown,
  ArrowUp,
  Settings,
  Filter,
  Sparkles,
} from 'lucide-react';

import { ToolMetadata, NodeType as BackendNodeType } from '@/types/workflow';

interface NodePaletteItem {
  type: BackendNodeType;
  name: string;
  description: string;
  category: string;
  icon: string;
  parameters: Array<{
    name: string;
    type: string;
    description: string;
    required: boolean;
    default?: any;
    enum_values?: any[];
    min_value?: number;
    max_value?: number;
  }>;
}

interface NodePaletteProps {
  nodeTypes: NodePaletteItem[];
  onNodeDragStart?: (nodeType: string) => void;
  onNodeSelect?: (nodeType: NodePaletteItem) => void;
  onAddNode?: (nodeType: NodePaletteItem) => void;
  availableToolsMetadata: ToolMetadata[];
}

const NodePalette: React.FC<NodePaletteProps> = ({ nodeTypes, onNodeDragStart, onNodeSelect, availableToolsMetadata }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const getNodeIcon = (iconName: string) => {
    switch (iconName) {
      case 'brain':
        return <Brain className="w-5 h-5" />;
      case 'database':
        return <Database className="w-5 h-5" />;
      case 'zap':
        return <Zap className="w-5 h-5" />;
      case 'tool':
        return <Wrench className="w-5 h-5" />;
      case 'git-branch':
        return <GitBranch className="w-5 h-5" />;
      case 'search':
        return <Search className="w-5 h-5" />;
      case 'arrow-down':
        return <ArrowDown className="w-5 h-5" />;
      case 'arrow-up':
        return <ArrowUp className="w-5 h-5" />;
      default:
        return <Settings className="w-5 h-5" />;
    }
  };

  const getNodeColor = (category: string) => {
    switch (category) {
      case 'AI':
        return 'bg-blue-50 border-blue-200 hover:bg-blue-100';
      case 'Data':
        return 'bg-green-50 border-green-200 hover:bg-green-100';
      case 'Logic':
        return 'bg-yellow-50 border-yellow-200 hover:bg-yellow-100';
      case 'Integration':
        return 'bg-orange-50 border-orange-200 hover:bg-orange-100';
      case 'IO':
        return 'bg-gray-50 border-gray-200 hover:bg-gray-100';
      default:
        return 'bg-gray-50 border-gray-200 hover:bg-gray-100';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'AI':
        return <Brain className="w-4 h-4" />;
      case 'Data':
        return <Database className="w-4 h-4" />;
      case 'Logic':
        return <GitBranch className="w-4 h-4" />;
      case 'Integration':
        return <Wrench className="w-4 h-4" />;
      case 'IO':
        return <ArrowDown className="w-4 h-4" />;
      default:
        return <Settings className="w-4 h-4" />;
    }
  };

  const categories = [
    'all',
    ...Array.from(new Set(nodeTypes.map((nt) => nt.category))),
    ...(availableToolsMetadata.length > 0 ? ['Tool'] : []),
  ];

  const filteredNodeTypes = nodeTypes.filter((nodeType) => {
    const matchesSearch =
      nodeType.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      nodeType.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || nodeType.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const filteredTools = availableToolsMetadata.filter((tool) => {
    const matchesSearch =
      tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      tool.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || selectedCategory === 'Tool';
    return matchesSearch && matchesCategory;
  });

  const groupedNodeTypes = categories.reduce(
    (acc, category) => {
      if (category === 'all') return acc;
      if (category === 'Tool') {
        acc[category] = filteredTools.map(tool => ({
          type: 'tool',
          name: tool.name,
          description: tool.description,
          category: 'Tool',
          icon: 'tool',
          parameters: tool.parameters.map(p => ({
            name: p.name,
            type: p.type,
            description: p.description,
            required: p.required,
            default: p.default,
            enum_values: p.enum_values,
            min_value: p.min_value,
            max_value: p.max_value,
          })),
        }));
      } else {
        acc[category] = filteredNodeTypes.filter((nt) => nt.category === category);
      }
      return acc;
    },
    {} as Record<string, NodeType[] | ToolMetadata[]>,
  );

  const onDragStart = (event: React.DragEvent, nodeType: BackendNodeType, nodeData: NodePaletteItem | ToolMetadata) => {
    event.dataTransfer.setData('application/reactflow', nodeType);
    event.dataTransfer.setData('application/json', JSON.stringify(nodeData));
    event.dataTransfer.effectAllowed = 'move';
    if (onNodeDragStart) {
      onNodeDragStart(nodeType);
    }
  };

  const NodeCard: React.FC<{ nodeType: NodePaletteItem | ToolMetadata }> = ({ nodeType }) => (
    <Card
      className={`
        w-full cursor-grab active:cursor-grabbing transition-all duration-200
        ${getNodeColor(nodeType.category)}
        hover:shadow-md
      `}
      draggable
      onDragStart={(event) => onDragStart(event, nodeType.type, nodeType)}
      onClick={() => {
        onNodeSelect?.(nodeType as NodeType);
      }}
    >
      <CardContent className="p-3">
        <div className="flex items-start gap-3">
          <div className="flex-shrink-0 mt-1">{getNodeIcon(nodeType.icon || 'settings')}</div>
          <div className="flex-1">
            <div className="flex items-center flex-wrap gap-2 mb-1">
              <h4 className="font-medium text-sm">{nodeType.name}</h4>
              <Badge variant="outline" className="text-xs">
                {nodeType.type}
              </Badge>
            </div>
            <p className="text-xs text-gray-600 line-clamp-2 mb-2">{nodeType.description}</p>
            <div className="flex items-center justify-between">
              <Badge variant="secondary" className="text-xs">
                {nodeType.category}
              </Badge>
              <span className="text-xs text-gray-500">{nodeType.parameters?.length || 0} params</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <Card className="w-full h-full flex flex-col rounded-none border-none">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Sparkles className="w-5 h-5" />
          Node Palette
        </CardTitle>

        <div className="space-y-2">
          <Input
            placeholder="Search nodes..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="h-8"
          />

          <div className="flex items-center gap-1 text-xs text-gray-500">
            <Filter className="w-3 h-3" />
            <span>Drag nodes to canvas</span>
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex-1 p-0">
        <Tabs
          value={selectedCategory}
          onValueChange={setSelectedCategory}
          className="h-full flex flex-col"
        >
          <TabsList className="grid grid-cols-3 mx-3 mb-3">
            <TabsTrigger value="all" className="text-xs">
              All
            </TabsTrigger>
            <TabsTrigger value="AI" className="text-xs">
              AI
            </TabsTrigger>
            <TabsTrigger value="Data" className="text-xs">
              Data
            </TabsTrigger>
          </TabsList>

          <ScrollArea className="flex-1 px-3">
            <TabsContent value="all" className="mt-0 space-y-2">
              {Object.entries(groupedNodeTypes).map(([category, nodes]) => (
                <div key={category} className="space-y-2">
                  {nodes.length > 0 && (
                    <>
                      <div className="flex items-center gap-2 py-2 border-b">
                        {getCategoryIcon(category)}
                        <span className="font-medium text-sm">{category}</span>
                        <Badge variant="outline" className="text-xs">
                          {nodes.length}
                        </Badge>
                      </div>
                      {(nodes as (NodePaletteItem | ToolMetadata)[]).map((nodeType) => (
                        <NodeCard key={nodeType.type === BackendNodeType.TOOL ? (nodeType as ToolMetadata).name : (nodeType as NodePaletteItem).type} nodeType={nodeType} />
                      ))}
                    </>
                  )}
                </div>
              ))}
            </TabsContent>

            {categories
              .filter((c) => c !== 'all')
              .map((category) => (
                <TabsContent key={category} value={category} className="mt-0 space-y-2">
                  {(groupedNodeTypes[category] as (NodePaletteItem | ToolMetadata)[])?.map((nodeType) => (
                    <NodeCard key={nodeType.type === BackendNodeType.TOOL ? (nodeType as ToolMetadata).name : (nodeType as NodePaletteItem).type} nodeType={nodeType} />
                  ))}
                  {(!groupedNodeTypes[category] || groupedNodeTypes[category].length === 0) && (
                    <div className="text-center py-8 text-gray-500">
                      <Settings className="w-8 h-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No nodes in this category</p>
                    </div>
                  )}
                </TabsContent>
              ))}
          </ScrollArea>
        </Tabs>
      </CardContent>

      <div className="p-3 border-t bg-gray-50">
        <div className="text-xs text-gray-600 space-y-1">
          <div className="flex items-center justify-between">
            <span>Total Nodes:</span>
            <span className="font-medium">{nodeTypes.length + availableToolsMetadata.length}</span>
          </div>
          <div className="flex items-center justify-between">
            <span>Filtered:</span>
            <span className="font-medium">{filteredNodeTypes.length + filteredTools.length}</span>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default NodePalette;
