'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import {
  Database,
  Clock,
  Zap,
  Brain,
  Archive,
  Trash2,
  Settings,
  Info,
  Plus,
  Minus
} from 'lucide-react';

import { MemoryConfig, MemoryType } from '@/types/workflow'; // Consolidated import

interface MemoryConfigEditorProps {
  config: MemoryConfig;
  onChange: (config: MemoryConfig) => void;
  onTest?: (memoryType: string) => void;
}

const defaultConfig: MemoryConfig = {
  enabled: true,
  types: [MemoryType.SHORT_TERM],
  max_total_entries: 1000,
  cleanup_interval_hours: 24,
  short_term: {
    type: MemoryType.SHORT_TERM,
    max_entries: 20,
    ttl_seconds: 3600
  },
  long_term: {
    type: MemoryType.LONG_TERM,
    max_entries: 500,
    embedding_model: 'text-embedding-ada-002',
    similarity_threshold: 0.7
  },
  episodic: {
    type: MemoryType.EPISODIC,
    max_entries: 100,
    episode_duration_minutes: 30,
    auto_summarize: true
  },
  semantic: {
    type: MemoryType.SEMANTIC,
    max_entries: 200,
    knowledge_categories: ['facts', 'preferences', 'context'],
    auto_categorize: true
  },
  working: {
    type: MemoryType.WORKING,
    max_entries: 10,
    auto_clear: true
  }
};

const MemoryConfigEditor: React.FC<MemoryConfigEditorProps> = ({
  config,
  onChange,
  onTest
}) => {
  const [localConfig, setLocalConfig] = useState<MemoryConfig>(config || defaultConfig);

  useEffect(() => {
    onChange(localConfig);
  }, [localConfig, onChange]);

  const updateConfig = (path: string, value: any) => {
    const keys = path.split('.');
    const newConfig = { ...localConfig };
    let current: any = newConfig;
    
    for (let i = 0; i < keys.length - 1; i++) {
      current = current[keys[i]];
    }
    current[keys[keys.length - 1]] = value;
    
    setLocalConfig(newConfig);
  };

  const toggleMemoryType = (type: MemoryType, enabled: boolean) => {
    const newTypes = enabled 
      ? [...localConfig.types, type]
      : localConfig.types.filter(t => t !== type);
    
    updateConfig('types', newTypes);
  };

  const addKnowledgeCategory = () => {
    const newCategory = `category_${Date.now()}`;
    updateConfig('semantic.knowledge_categories', [
      ...localConfig.semantic.knowledge_categories,
      newCategory
    ]);
  };

  const removeKnowledgeCategory = (index: number) => {
    const newCategories = localConfig.semantic.knowledge_categories.filter((_, i) => i !== index);
    updateConfig('semantic.knowledge_categories', newCategories);
  };

  const updateKnowledgeCategory = (index: number, value: string) => {
    const newCategories = [...localConfig.semantic.knowledge_categories];
    newCategories[index] = value;
    updateConfig('semantic.knowledge_categories', newCategories);
  };

  const getMemoryTypeIcon = (type: string) => {
    switch (type) {
      case MemoryType.SHORT_TERM: return <Clock className="w-4 h-4" />;
      case MemoryType.LONG_TERM: return <Archive className="w-4 h-4" />;
      case MemoryType.EPISODIC: return <Brain className="w-4 h-4" />;
      case MemoryType.SEMANTIC: return <Database className="w-4 h-4" />;
      case MemoryType.WORKING: return <Zap className="w-4 h-4" />;
      default: return <Settings className="w-4 h-4" />;
    }
  };

  const getMemoryTypeDescription = (type: string) => {
    switch (type) {
      case MemoryType.SHORT_TERM: return 'Temporary storage for recent conversation context';
      case MemoryType.LONG_TERM: return 'Persistent storage with vector embeddings for semantic search';
      case MemoryType.EPISODIC: return 'Conversation episodes with automatic summarization';
      case MemoryType.SEMANTIC: return 'Structured knowledge and facts with categorization';
      case MemoryType.WORKING: return 'Temporary variables and intermediate results';
      default: return 'Memory storage type';
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Database className="w-5 h-5" />
            Memory Configuration
          </CardTitle>
          <Switch
            checked={localConfig.enabled}
            onCheckedChange={(checked) => updateConfig('enabled', checked)}
          />
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {!localConfig.enabled && (
          <div className="text-center py-8 text-gray-500">
            <Database className="w-12 h-12 mx-auto mb-3 opacity-50" />
            <p>Memory is disabled</p>
            <p className="text-sm">Enable memory to configure storage options</p>
          </div>
        )}

        {localConfig.enabled && (
          <>
            {/* Global Settings */}
            <div className="space-y-4">
              <h3 className="font-medium flex items-center gap-2">
                <Settings className="w-4 h-4" />
                Global Settings
              </h3>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Max Total Entries</Label>
                  <Input
                    type="number"
                    value={localConfig.max_total_entries}
                    onChange={(e) => updateConfig('max_total_entries', parseInt(e.target.value))}
                    min={100}
                    max={10000}
                  />
                </div>
                <div>
                  <Label>Cleanup Interval (hours)</Label>
                  <Input
                    type="number"
                    value={localConfig.cleanup_interval_hours}
                    onChange={(e) => updateConfig('cleanup_interval_hours', parseInt(e.target.value))}
                    min={1}
                    max={168}
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Memory Types */}
            <div className="space-y-4">
              <h3 className="font-medium">Memory Types</h3>
              
              <Tabs defaultValue="short_term" className="w-full">
                <TabsList className="grid w-full grid-cols-5">
                  <TabsTrigger value="short_term">Short Term</TabsTrigger>
                  <TabsTrigger value="long_term">Long Term</TabsTrigger>
                  <TabsTrigger value="episodic">Episodic</TabsTrigger>
                  <TabsTrigger value="semantic">Semantic</TabsTrigger>
                  <TabsTrigger value="working">Working</TabsTrigger>
                </TabsList>

                {/* Short Term Memory */}
                <TabsContent value="short_term" className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {getMemoryTypeIcon('short_term')}
                      <div>
                        <h4 className="font-medium">Short Term Memory</h4>
                        <p className="text-sm text-gray-600">{getMemoryTypeDescription('short_term')}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Switch
                        checked={localConfig.types.includes(MemoryType.SHORT_TERM)}
                        onCheckedChange={(checked) => toggleMemoryType(MemoryType.SHORT_TERM, checked)}
                      />
                      {onTest && (
                        <Button size="sm" variant="outline" onClick={() => onTest('short_term')}>
                          Test
                        </Button>
                      )}
                    </div>
                  </div>

                  {localConfig.types.includes(MemoryType.SHORT_TERM) && (
                    <div className="grid grid-cols-2 gap-4 pl-6">
                      <div>
                        <Label>Max Entries</Label>
                        <Input
                          type="number"
                          value={localConfig.short_term.max_entries}
                          onChange={(e) => updateConfig('short_term.max_entries', parseInt(e.target.value))}
                          min={5}
                          max={100}
                        />
                      </div>
                      <div>
                        <Label>TTL (seconds)</Label>
                        <Input
                          type="number"
                          value={localConfig.short_term.ttl_seconds || 3600}
                          onChange={(e) => updateConfig('short_term.ttl_seconds', parseInt(e.target.value))}
                          min={60}
                          max={86400}
                        />
                      </div>
                    </div>
                  )}
                </TabsContent>

                {/* Long Term Memory */}
                <TabsContent value="long_term" className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {getMemoryTypeIcon('long_term')}
                      <div>
                        <h4 className="font-medium">Long Term Memory</h4>
                        <p className="text-sm text-gray-600">{getMemoryTypeDescription('long_term')}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Switch
                        checked={localConfig.types.includes(MemoryType.LONG_TERM)}
                        onCheckedChange={(checked) => toggleMemoryType(MemoryType.LONG_TERM, checked)}
                      />
                      {onTest && (
                        <Button size="sm" variant="outline" onClick={() => onTest('long_term')}>
                          Test
                        </Button>
                      )}
                    </div>
                  </div>

                  {localConfig.types.includes(MemoryType.LONG_TERM) && (
                    <div className="space-y-4 pl-6">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label>Max Entries</Label>
                          <Input
                            type="number"
                            value={localConfig.long_term.max_entries}
                            onChange={(e) => updateConfig('long_term.max_entries', parseInt(e.target.value))}
                            min={50}
                            max={2000}
                          />
                        </div>
                        <div>
                          <Label>Embedding Model</Label>
                          <Select
                            value={localConfig.long_term.embedding_model}
                            onValueChange={(value) => updateConfig('long_term.embedding_model', value)}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="text-embedding-ada-002">OpenAI Ada-002</SelectItem>
                              <SelectItem value="text-embedding-3-small">OpenAI 3-Small</SelectItem>
                              <SelectItem value="text-embedding-3-large">OpenAI 3-Large</SelectItem>
                              <SelectItem value="sentence-transformers">Sentence Transformers</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                      
                      <div>
                        <Label>Similarity Threshold: {localConfig.long_term.similarity_threshold}</Label>
                        <Slider
                          value={[localConfig.long_term.similarity_threshold]}
                          onValueChange={([value]) => updateConfig('long_term.similarity_threshold', value)}
                          min={0.1}
                          max={1.0}
                          step={0.1}
                          className="mt-2"
                        />
                      </div>
                    </div>
                  )}
                </TabsContent>

                {/* Episodic Memory */}
                <TabsContent value="episodic" className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {getMemoryTypeIcon('episodic')}
                      <div>
                        <h4 className="font-medium">Episodic Memory</h4>
                        <p className="text-sm text-gray-600">{getMemoryTypeDescription('episodic')}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Switch
                        checked={localConfig.types.includes(MemoryType.EPISODIC)}
                        onCheckedChange={(checked) => toggleMemoryType(MemoryType.EPISODIC, checked)}
                      />
                      {onTest && (
                        <Button size="sm" variant="outline" onClick={() => onTest('episodic')}>
                          Test
                        </Button>
                      )}
                    </div>
                  </div>

                  {localConfig.types.includes(MemoryType.EPISODIC) && (
                    <div className="space-y-4 pl-6">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label>Max Entries</Label>
                          <Input
                            type="number"
                            value={localConfig.episodic.max_entries}
                            onChange={(e) => updateConfig('episodic.max_entries', parseInt(e.target.value))}
                            min={10}
                            max={500}
                          />
                        </div>
                        <div>
                          <Label>Episode Duration (minutes)</Label>
                          <Input
                            type="number"
                            value={localConfig.episodic.episode_duration_minutes}
                            onChange={(e) => updateConfig('episodic.episode_duration_minutes', parseInt(e.target.value))}
                            min={5}
                            max={120}
                          />
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={localConfig.episodic.auto_summarize}
                          onCheckedChange={(checked) => updateConfig('episodic.auto_summarize', checked)}
                        />
                        <Label>Auto-summarize episodes</Label>
                      </div>
                    </div>
                  )}
                </TabsContent>

                {/* Semantic Memory */}
                <TabsContent value="semantic" className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {getMemoryTypeIcon('semantic')}
                      <div>
                        <h4 className="font-medium">Semantic Memory</h4>
                        <p className="text-sm text-gray-600">{getMemoryTypeDescription('semantic')}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Switch
                        checked={localConfig.types.includes(MemoryType.SEMANTIC)}
                        onCheckedChange={(checked) => toggleMemoryType(MemoryType.SEMANTIC, checked)}
                      />
                      {onTest && (
                        <Button size="sm" variant="outline" onClick={() => onTest('semantic')}>
                          Test
                        </Button>
                      )}
                    </div>
                  </div>

                  {localConfig.types.includes(MemoryType.SEMANTIC) && (
                    <div className="space-y-4 pl-6">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label>Max Entries</Label>
                          <Input
                            type="number"
                            value={localConfig.semantic.max_entries}
                            onChange={(e) => updateConfig('semantic.max_entries', parseInt(e.target.value))}
                            min={20}
                            max={1000}
                          />
                        </div>
                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={localConfig.semantic.auto_categorize}
                            onCheckedChange={(checked) => updateConfig('semantic.auto_categorize', checked)}
                          />
                          <Label>Auto-categorize</Label>
                        </div>
                      </div>
                      
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <Label>Knowledge Categories</Label>
                          <Button size="sm" variant="outline" onClick={addKnowledgeCategory}>
                            <Plus className="w-3 h-3 mr-1" />
                            Add
                          </Button>
                        </div>
                        <div className="space-y-2">
                          {localConfig.semantic.knowledge_categories.map((category, index) => (
                            <div key={index} className="flex items-center gap-2">
                              <Input
                                value={category}
                                onChange={(e) => updateKnowledgeCategory(index, e.target.value)}
                                placeholder="Category name"
                              />
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => removeKnowledgeCategory(index)}
                              >
                                <Minus className="w-3 h-3" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </TabsContent>

                {/* Working Memory */}
                <TabsContent value="working" className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {getMemoryTypeIcon('working')}
                      <div>
                        <h4 className="font-medium">Working Memory</h4>
                        <p className="text-sm text-gray-600">{getMemoryTypeDescription('working')}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Switch
                        checked={localConfig.types.includes(MemoryType.WORKING)}
                        onCheckedChange={(checked) => toggleMemoryType(MemoryType.WORKING, checked)}
                      />
                      {onTest && (
                        <Button size="sm" variant="outline" onClick={() => onTest('working')}>
                          Test
                        </Button>
                      )}
                    </div>
                  </div>

                  {localConfig.types.includes(MemoryType.WORKING) && (
                    <div className="grid grid-cols-2 gap-4 pl-6">
                      <div>
                        <Label>Max Entries</Label>
                        <Input
                          type="number"
                          value={localConfig.working.max_entries}
                          onChange={(e) => updateConfig('working.max_entries', parseInt(e.target.value))}
                          min={5}
                          max={50}
                        />
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={localConfig.working.auto_clear}
                          onCheckedChange={(checked) => updateConfig('working.auto_clear', checked)}
                        />
                        <Label>Auto-clear on completion</Label>
                      </div>
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </div>

            {/* Summary */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <Info className="w-4 h-4" />
                Configuration Summary
              </h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Enabled Types:</span>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {localConfig.types.map(type => (
                      <Badge key={type} variant="secondary" className="text-xs">
                        {type.replace('_', ' ')}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <span className="text-gray-600">Total Capacity:</span>
                  <div className="font-medium">{localConfig.max_total_entries} entries</div>
                </div>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default MemoryConfigEditor;
