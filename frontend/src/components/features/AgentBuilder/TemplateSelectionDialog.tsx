// frontend/src/components/features/AgentBuilder/TemplateSelectionDialog.tsx
'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Users,
  Star,
  Workflow,
  Search,
} from 'lucide-react';

interface AgentTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  workflow: any; // Simplified for this component
  preview_image?: string;
  author?: string;
  version: string;
  usage_count: number;
  rating?: number;
}

interface TemplateSelectionDialogProps {
  showTemplateDialog: boolean;
  setShowTemplateDialog: (show: boolean) => void;
  templateSearchTerm: string;
  setTemplateSearchTerm: (term: string) => void;
  selectedTemplateCategory: string;
  setSelectedTemplateCategory: (category: string) => void;
  templateCategories: Array<{ id: string; name: string; icon: string }>;
  filteredTemplates: AgentTemplate[];
  handleLoadTemplate: (template: AgentTemplate) => void;
}

const TemplateSelectionDialog: React.FC<TemplateSelectionDialogProps> = ({
  showTemplateDialog,
  setShowTemplateDialog,
  templateSearchTerm,
  setTemplateSearchTerm,
  selectedTemplateCategory,
  setSelectedTemplateCategory,
  templateCategories,
  filteredTemplates,
  handleLoadTemplate,
}) => {
  return (
    <Dialog open={showTemplateDialog} onOpenChange={setShowTemplateDialog}>
      <DialogContent className="max-w-6xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle>Choose Agent Template</DialogTitle>
          <p className="text-sm text-gray-600">
            Select from pre-built agent templates to get started quickly
          </p>
        </DialogHeader>

        {/* Search and Filter */}
        <div className="flex flex-col sm:flex-row gap-4 mb-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search templates..."
              value={templateSearchTerm}
              onChange={(e) => setTemplateSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={selectedTemplateCategory} onValueChange={setSelectedTemplateCategory}>
            <SelectTrigger className="w-full sm:w-48">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              {templateCategories.map((category) => (
                <SelectItem key={category.id} value={category.id}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Templates Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[50vh] overflow-y-auto">
          {filteredTemplates.length > 0 ? (
            filteredTemplates.map((template) => (
              <Card
                key={template.id}
                className="cursor-pointer hover:shadow-md transition-shadow"
              >
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{template.name}</CardTitle>
                    <Badge variant="secondary">{template.category}</Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-3">{template.description}</p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <Users className="w-3 h-3" />
                      {template.usage_count} uses
                      {template.rating && (
                        <>
                          <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                          {template.rating}
                        </>
                      )}
                    </div>
                    <Button size="sm" onClick={() => handleLoadTemplate(template)}>
                      Load Template
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            <div className="col-span-full text-center py-8">
              <Workflow className="w-16 h-16 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-semibold mb-2">No templates found</h3>
              <p className="text-gray-600">
                {templateSearchTerm || selectedTemplateCategory !== 'all'
                  ? 'Try adjusting your search or filter criteria'
                  : 'No templates available at the moment'}
              </p>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TemplateSelectionDialog;
