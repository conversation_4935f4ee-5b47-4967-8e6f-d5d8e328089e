// frontend/src/components/features/AgentBuilder/WorkflowValidationPanel.tsx
'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  CheckCircle,
  XCircle,
  AlertTriangle,
  RefreshCw,
  Download,
  FileText,
  Zap,
  GitBranch,
  Settings,
} from 'lucide-react';

import { Workflow, WorkflowValidationResult } from '@/types/workflow';
import { validateWorkflow, generateWorkflowReport } from '@/lib/workflow-validation';
import { exportWorkflowToJSON } from '@/lib/agent-utils';

interface WorkflowValidationPanelProps {
  workflow: Workflow;
  onValidationChange?: (result: WorkflowValidationResult) => void;
  className?: string;
}

const WorkflowValidationPanel: React.FC<WorkflowValidationPanelProps> = ({
  workflow,
  onValidationChange,
  className = '',
}) => {
  const [validationResult, setValidationResult] = useState<WorkflowValidationResult | null>(null);
  const [isValidating, setIsValidating] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  // Validate workflow whenever it changes
  useEffect(() => {
    validateWorkflowAsync();
  }, [workflow]);

  const validateWorkflowAsync = async () => {
    setIsValidating(true);
    
    try {
      // Add a small delay to show loading state
      await new Promise(resolve => setTimeout(resolve, 100));
      
      const result = validateWorkflow(workflow, {
        strictMode: false,
        validateConnections: true,
        validateNodeConfigurations: true,
        checkCircularDependencies: true,
        requireStartNode: true,
      });
      
      setValidationResult(result);
      
      if (onValidationChange) {
        onValidationChange(result);
      }
    } catch (error) {
      console.error('Validation error:', error);
      setValidationResult({
        isValid: false,
        errors: ['Validation failed: ' + (error instanceof Error ? error.message : 'Unknown error')],
        warnings: [],
        nodeValidations: {},
        connectionErrors: [],
        structuralIssues: [],
      });
    } finally {
      setIsValidating(false);
    }
  };

  const handleExportReport = () => {
    if (!validationResult) return;

    const report = generateWorkflowReport(workflow, validationResult);
    const blob = new Blob([report], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `validation-report-${workflow.name}-${Date.now()}.md`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const handleExportWorkflow = () => {
    const workflowJson = exportWorkflowToJSON(workflow);
    const blob = new Blob([workflowJson], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `workflow-${workflow.name}-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const getStatusIcon = (isValid: boolean) => {
    if (isValidating) {
      return <RefreshCw className="w-5 h-5 animate-spin text-blue-500" />;
    }
    return isValid ? (
      <CheckCircle className="w-5 h-5 text-green-500" />
    ) : (
      <XCircle className="w-5 h-5 text-red-500" />
    );
  };

  const getStatusColor = (isValid: boolean) => {
    if (isValidating) return 'bg-blue-50 border-blue-200';
    return isValid ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200';
  };

  const getStatusText = (isValid: boolean) => {
    if (isValidating) return 'Validating...';
    return isValid ? 'Valid Workflow' : 'Invalid Workflow';
  };

  if (!validationResult && !isValidating) {
    return (
      <Card className={`w-full h-full ${className}`}>
        <CardContent className="flex items-center justify-center h-full">
          <div className="text-center text-gray-500">
            <Settings className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>Workflow validation will appear here</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`w-full h-full ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Zap className="w-5 h-5" />
            <CardTitle className="text-lg">Workflow Validation</CardTitle>
          </div>
          <div className="flex items-center gap-2">
            <Button size="sm" variant="outline" onClick={validateWorkflowAsync}>
              <RefreshCw className="w-4 h-4 mr-1" />
              Refresh
            </Button>
            {validationResult && (
              <>
                <Button size="sm" variant="outline" onClick={handleExportReport}>
                  <FileText className="w-4 h-4 mr-1" />
                  Report
                </Button>
                <Button size="sm" variant="outline" onClick={handleExportWorkflow}>
                  <Download className="w-4 h-4 mr-1" />
                  Export
                </Button>
              </>
            )}
          </div>
        </div>

        {/* Status Summary */}
        {(validationResult || isValidating) && (
          <div className={`p-3 rounded-lg border ${getStatusColor(validationResult?.isValid || false)}`}>
            <div className="flex items-center gap-2 mb-2">
              {getStatusIcon(validationResult?.isValid || false)}
              <span className="font-medium">
                {getStatusText(validationResult?.isValid || false)}
              </span>
            </div>
            
            {validationResult && (
              <div className="flex items-center gap-4 text-sm">
                <div className="flex items-center gap-1">
                  <span className="text-red-600">{validationResult.errors.length} errors</span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="text-yellow-600">{validationResult.warnings.length} warnings</span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="text-gray-600">{Object.keys(validationResult.nodeValidations).length} nodes checked</span>
                </div>
              </div>
            )}
          </div>
        )}
      </CardHeader>

      <CardContent className="p-0">
        {validationResult && (
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4 mx-4 mb-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="nodes">Nodes</TabsTrigger>
              <TabsTrigger value="connections">Connections</TabsTrigger>
              <TabsTrigger value="structure">Structure</TabsTrigger>
            </TabsList>

            <div className="px-4 pb-4">
              <TabsContent value="overview" className="space-y-4 mt-0">
                {/* Errors */}
                {validationResult.errors.length > 0 && (
                  <div>
                    <h4 className="font-medium text-red-800 mb-2 flex items-center gap-2">
                      <XCircle className="w-4 h-4" />
                      Errors ({validationResult.errors.length})
                    </h4>
                    <ScrollArea className="h-32">
                      <div className="space-y-1">
                        {validationResult.errors.map((error, index) => (
                          <div key={index} className="text-sm text-red-700 bg-red-50 p-2 rounded">
                            • {error}
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </div>
                )}

                {/* Warnings */}
                {validationResult.warnings.length > 0 && (
                  <div>
                    <h4 className="font-medium text-yellow-800 mb-2 flex items-center gap-2">
                      <AlertTriangle className="w-4 h-4" />
                      Warnings ({validationResult.warnings.length})
                    </h4>
                    <ScrollArea className="h-32">
                      <div className="space-y-1">
                        {validationResult.warnings.map((warning, index) => (
                          <div key={index} className="text-sm text-yellow-700 bg-yellow-50 p-2 rounded">
                            • {warning}
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </div>
                )}

                {/* Success State */}
                {validationResult.isValid && (
                  <div className="text-center py-8">
                    <CheckCircle className="w-16 h-16 mx-auto mb-4 text-green-500" />
                    <h3 className="text-lg font-medium text-green-800 mb-2">Workflow is Valid!</h3>
                    <p className="text-sm text-green-600">
                      All nodes and connections are properly configured.
                    </p>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="nodes" className="space-y-2 mt-0">
                <ScrollArea className="h-96">
                  {Object.entries(validationResult.nodeValidations).map(([nodeId, validation]) => {
                    const node = workflow.nodes.find(n => n.id === nodeId);
                    return (
                      <Card key={nodeId} className="p-3 mb-2">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <span className="font-medium text-sm">{node?.name || nodeId}</span>
                            <Badge variant="outline" className="text-xs">{node?.type}</Badge>
                          </div>
                          {validation.isValid ? (
                            <CheckCircle className="w-4 h-4 text-green-500" />
                          ) : (
                            <XCircle className="w-4 h-4 text-red-500" />
                          )}
                        </div>
                        
                        {validation.errors.length > 0 && (
                          <div className="text-xs text-red-600 space-y-1">
                            {validation.errors.map((error, index) => (
                              <div key={index}>• {error}</div>
                            ))}
                          </div>
                        )}
                        
                        {validation.warnings.length > 0 && (
                          <div className="text-xs text-yellow-600 space-y-1 mt-1">
                            {validation.warnings.map((warning, index) => (
                              <div key={index}>⚠ {warning}</div>
                            ))}
                          </div>
                        )}
                      </Card>
                    );
                  })}
                </ScrollArea>
              </TabsContent>

              <TabsContent value="connections" className="space-y-2 mt-0">
                <ScrollArea className="h-96">
                  {validationResult.connectionErrors.length > 0 ? (
                    validationResult.connectionErrors.map((error, index) => (
                      <div key={index} className="text-sm text-red-700 bg-red-50 p-2 rounded mb-2">
                        <GitBranch className="w-4 h-4 inline mr-2" />
                        {error}
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <GitBranch className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <p className="text-sm">All connections are valid</p>
                    </div>
                  )}
                </ScrollArea>
              </TabsContent>

              <TabsContent value="structure" className="space-y-2 mt-0">
                <ScrollArea className="h-96">
                  {validationResult.structuralIssues.length > 0 ? (
                    validationResult.structuralIssues.map((issue, index) => (
                      <div key={index} className="text-sm text-red-700 bg-red-50 p-2 rounded mb-2">
                        <Settings className="w-4 h-4 inline mr-2" />
                        {issue}
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <Settings className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <p className="text-sm">Workflow structure is valid</p>
                    </div>
                  )}
                </ScrollArea>
              </TabsContent>
            </div>
          </Tabs>
        )}
      </CardContent>
    </Card>
  );
};

export default WorkflowValidationPanel;
