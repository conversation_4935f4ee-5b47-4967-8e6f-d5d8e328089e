// frontend/src/components/features/AgentBuilder/AgentPropertiesPanel.tsx
'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';

import { Workflow as BuilderWorkflow } from '@/types/workflow';

interface AgentPropertiesPanelProps {
  currentWorkflow: BuilderWorkflow;
  setCurrentWorkflow: React.Dispatch<React.SetStateAction<BuilderWorkflow>>;
}

const AgentPropertiesPanel: React.FC<AgentPropertiesPanelProps> = ({
  currentWorkflow,
  setCurrentWorkflow,
}) => {
  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b">
        <h3 className="font-medium">Agent Properties</h3>
        <p className="text-sm text-gray-600">Configure the overall agent workflow</p>
      </div>
      <div className="flex-1 relative">
        <ScrollArea className="absolute inset-0 p-4">
          <div className="space-y-4">
            <div>
              <Label>Agent ID</Label>
              <Input value={currentWorkflow.id} disabled />
            </div>
            <div>
              <Label>Name</Label>
              <Input
                key={`agent-name-${currentWorkflow.id}`}
                value={currentWorkflow.name}
                onChange={(e) =>
                  setCurrentWorkflow((prev) => ({ ...prev, name: e.target.value }))
                }
              />
            </div>
            <div>
              <Label>Description</Label>
              <Textarea
                key={`agent-desc-${currentWorkflow.id}`}
                value={currentWorkflow.description}
                onChange={(e) =>
                  setCurrentWorkflow((prev) => ({ ...prev, description: e.target.value }))
                }
                rows={3}
              />
            </div>
            <div>
              <Label>Category</Label>
              <Select
                value={currentWorkflow.category}
                onValueChange={(value) =>
                  setCurrentWorkflow((prev) => ({ ...prev, category: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="customer_service">Customer Service</SelectItem>
                  <SelectItem value="sales">Sales</SelectItem>
                  <SelectItem value="support">Support</SelectItem>
                  <SelectItem value="analysis">Analysis</SelectItem>
                  <SelectItem value="custom">Custom</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label>Agent Type</Label>
              <Badge variant="outline">{currentWorkflow.agent_type}</Badge>
            </div>
            <div>
              <Label>Version</Label>
              <Input value={currentWorkflow.version} disabled />
            </div>
          </div>
        </ScrollArea>
      </div>
    </div>
  );
};

export default AgentPropertiesPanel;
