'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  Rocket,
  Server,
  Globe,
  Key,
  Shield,
  Monitor,
  Settings,
  Copy,
  ExternalLink,
  CheckCircle,
  XCircle,
  AlertCircle,
  Clock,
  Activity,
  BarChart3,
  Download,
  Upload,
  RefreshCw,
  Pause,
  Play,
  Square
} from 'lucide-react';
import { toast } from 'react-hot-toast';

import { Workflow } from '@/services/agent-builder/agentBuilderService';
import { apiClient } from '@/services/api/apiClient';

interface AgentDeploymentProps {
  workflow?: Workflow;
  onDeploymentComplete?: (deploymentInfo: any) => void;
}

interface DeploymentConfig {
  name: string;
  description: string;
  environment: 'development' | 'staging' | 'production';
  scaling: {
    minInstances: number;
    maxInstances: number;
    targetCPU: number;
  };
  security: {
    apiKeyRequired: boolean;
    rateLimiting: boolean;
    allowedOrigins: string[];
  };
  monitoring: {
    enableLogs: boolean;
    enableMetrics: boolean;
    enableAlerts: boolean;
  };
}

interface DeploymentStatus {
  id: string;
  name: string;
  status: 'deploying' | 'running' | 'stopped' | 'failed';
  url?: string;
  apiKey?: string;
  createdAt: string;
  lastUpdated: string;
  metrics: {
    requests: number;
    errors: number;
    avgResponseTime: number;
    uptime: number;
  };
}

const AgentDeployment: React.FC<AgentDeploymentProps> = ({
  workflow,
  onDeploymentComplete
}) => {
  const [deployments, setDeployments] = useState<DeploymentStatus[]>([]);
  const [isDeploying, setIsDeploying] = useState(false);
  const [showDeployDialog, setShowDeployDialog] = useState(false);
  const [selectedDeployment, setSelectedDeployment] = useState<string>('');

  const [deployConfig, setDeployConfig] = useState<DeploymentConfig>({
    name: '',
    description: '',
    environment: 'development',
    scaling: {
      minInstances: 1,
      maxInstances: 3,
      targetCPU: 70
    },
    security: {
      apiKeyRequired: true,
      rateLimiting: true,
      allowedOrigins: ['*']
    },
    monitoring: {
      enableLogs: true,
      enableMetrics: true,
      enableAlerts: false
    }
  });

  useEffect(() => {
    loadDeployments();
  }, []);

  const loadDeployments = async () => {
    // Mock deployment data - in production this would come from the API
    const mockDeployments: DeploymentStatus[] = [
      {
        id: 'deploy-1',
        name: 'Customer Service Agent',
        status: 'running',
        url: 'https://api.example.com/agents/customer-service',
        apiKey: 'ak_1234567890abcdef',
        createdAt: '2024-01-15T10:00:00Z',
        lastUpdated: '2024-01-15T14:30:00Z',
        metrics: {
          requests: 1250,
          errors: 12,
          avgResponseTime: 850,
          uptime: 99.2
        }
      },
      {
        id: 'deploy-2',
        name: 'Sales Assistant',
        status: 'stopped',
        url: 'https://api.example.com/agents/sales-assistant',
        apiKey: 'ak_abcdef1234567890',
        createdAt: '2024-01-10T09:00:00Z',
        lastUpdated: '2024-01-14T16:45:00Z',
        metrics: {
          requests: 890,
          errors: 5,
          avgResponseTime: 1200,
          uptime: 98.8
        }
      }
    ];
    setDeployments(mockDeployments);
  };

  const handleDeploy = async () => {
    if (!workflow) {
      toast.error('No workflow available for deployment');
      return;
    }

    try {
      setIsDeploying(true);
      
      // Mock deployment process
      const deploymentId = `deploy-${Date.now()}`;
      const newDeployment: DeploymentStatus = {
        id: deploymentId,
        name: deployConfig.name,
        status: 'deploying',
        createdAt: new Date().toISOString(),
        lastUpdated: new Date().toISOString(),
        metrics: {
          requests: 0,
          errors: 0,
          avgResponseTime: 0,
          uptime: 0
        }
      };

      setDeployments(prev => [...prev, newDeployment]);
      
      // Simulate deployment process
      setTimeout(() => {
        setDeployments(prev => prev.map(d => 
          d.id === deploymentId 
            ? {
                ...d,
                status: 'running' as const,
                url: `https://api.example.com/agents/${deployConfig.name.toLowerCase().replace(/\s+/g, '-')}`,
                apiKey: `ak_${Math.random().toString(36).substr(2, 16)}`,
                lastUpdated: new Date().toISOString()
              }
            : d
        ));
        
        toast.success('Agent deployed successfully!');
        setShowDeployDialog(false);
        
        if (onDeploymentComplete) {
          onDeploymentComplete(newDeployment);
        }
      }, 3000);

    } catch (error) {
      console.error('Failed to deploy agent:', error);
      toast.error('Failed to deploy agent');
    } finally {
      setIsDeploying(false);
    }
  };

  const handleStopDeployment = async (deploymentId: string) => {
    try {
      setDeployments(prev => prev.map(d => 
        d.id === deploymentId 
          ? { ...d, status: 'stopped' as const, lastUpdated: new Date().toISOString() }
          : d
      ));
      toast.success('Deployment stopped');
    } catch (error) {
      console.error('Failed to stop deployment:', error);
      toast.error('Failed to stop deployment');
    }
  };

  const handleStartDeployment = async (deploymentId: string) => {
    try {
      setDeployments(prev => prev.map(d => 
        d.id === deploymentId 
          ? { ...d, status: 'running' as const, lastUpdated: new Date().toISOString() }
          : d
      ));
      toast.success('Deployment started');
    } catch (error) {
      console.error('Failed to start deployment:', error);
      toast.error('Failed to start deployment');
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard!');
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'stopped':
        return <Square className="w-4 h-4 text-gray-500" />;
      case 'deploying':
        return <Clock className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'bg-green-100 text-green-800';
      case 'stopped':
        return 'bg-gray-100 text-gray-800';
      case 'deploying':
        return 'bg-blue-100 text-blue-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Agent Deployment</h2>
          <p className="text-gray-600">Deploy and manage your agents in production</p>
        </div>
        <Button
          onClick={() => setShowDeployDialog(true)}
          disabled={!workflow || isDeploying}
        >
          <Rocket className="w-4 h-4 mr-2" />
          Deploy Agent
        </Button>
      </div>

      <Tabs defaultValue="deployments" className="space-y-4">
        <TabsList>
          <TabsTrigger value="deployments">Active Deployments</TabsTrigger>
          <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        {/* Deployments Tab */}
        <TabsContent value="deployments" className="space-y-4">
          {deployments.length === 0 ? (
            <div className="text-center py-12">
              <Server className="w-12 h-12 mx-auto mb-3 text-gray-400" />
              <p className="text-gray-600">No deployments yet</p>
              <p className="text-sm text-gray-500">Deploy your first agent to get started</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {deployments.map((deployment) => (
                <Card key={deployment.id}>
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="flex items-center gap-2">
                          {getStatusIcon(deployment.status)}
                          {deployment.name}
                        </CardTitle>
                        <Badge className={`mt-1 ${getStatusColor(deployment.status)}`}>
                          {deployment.status}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-1">
                        {deployment.status === 'running' && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleStopDeployment(deployment.id)}
                          >
                            <Pause className="w-4 h-4" />
                          </Button>
                        )}
                        {deployment.status === 'stopped' && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleStartDeployment(deployment.id)}
                          >
                            <Play className="w-4 h-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Deployment Info */}
                    <div className="space-y-2">
                      {deployment.url && (
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Endpoint:</span>
                          <div className="flex items-center gap-2">
                            <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                              {deployment.url}
                            </code>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => copyToClipboard(deployment.url!)}
                            >
                              <Copy className="w-3 h-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => window.open(deployment.url, '_blank')}
                            >
                              <ExternalLink className="w-3 h-3" />
                            </Button>
                          </div>
                        </div>
                      )}
                      {deployment.apiKey && (
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">API Key:</span>
                          <div className="flex items-center gap-2">
                            <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                              {deployment.apiKey.substring(0, 8)}...
                            </code>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => copyToClipboard(deployment.apiKey!)}
                            >
                              <Copy className="w-3 h-3" />
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>

                    <Separator />

                    {/* Metrics */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center">
                        <div className="text-lg font-semibold">{deployment.metrics.requests}</div>
                        <div className="text-xs text-gray-600">Requests</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-semibold">{deployment.metrics.errors}</div>
                        <div className="text-xs text-gray-600">Errors</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-semibold">{deployment.metrics.avgResponseTime}ms</div>
                        <div className="text-xs text-gray-600">Avg Response</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-semibold">{deployment.metrics.uptime}%</div>
                        <div className="text-xs text-gray-600">Uptime</div>
                      </div>
                    </div>

                    <div className="text-xs text-gray-500">
                      Last updated: {new Date(deployment.lastUpdated).toLocaleString()}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        {/* Monitoring Tab */}
        <TabsContent value="monitoring" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="w-5 h-5" />
                  Request Volume
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-48 flex items-center justify-center text-gray-500">
                  <BarChart3 className="w-8 h-8 mr-2" />
                  Chart placeholder - would show request volume over time
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Monitor className="w-5 h-5" />
                  Response Times
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-48 flex items-center justify-center text-gray-500">
                  <BarChart3 className="w-8 h-8 mr-2" />
                  Chart placeholder - would show response times
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertCircle className="w-5 h-5" />
                  Error Rates
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-48 flex items-center justify-center text-gray-500">
                  <BarChart3 className="w-8 h-8 mr-2" />
                  Chart placeholder - would show error rates
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="w-5 h-5" />
                  Geographic Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-48 flex items-center justify-center text-gray-500">
                  <Globe className="w-8 h-8 mr-2" />
                  Map placeholder - would show request origins
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Settings Tab */}
        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Deployment Settings</CardTitle>
              <p className="text-sm text-gray-600">Configure default deployment settings</p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="default-env">Default Environment</Label>
                  <Select defaultValue="development">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="development">Development</SelectItem>
                      <SelectItem value="staging">Staging</SelectItem>
                      <SelectItem value="production">Production</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="default-instances">Default Min Instances</Label>
                  <Input
                    id="default-instances"
                    type="number"
                    defaultValue="1"
                    min="1"
                    max="10"
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label>Security Settings</Label>
                <div className="space-y-2">
                  <label className="flex items-center space-x-2">
                    <input type="checkbox" defaultChecked />
                    <span className="text-sm">Require API key authentication</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input type="checkbox" defaultChecked />
                    <span className="text-sm">Enable rate limiting</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input type="checkbox" />
                    <span className="text-sm">Enable CORS restrictions</span>
                  </label>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Monitoring Settings</Label>
                <div className="space-y-2">
                  <label className="flex items-center space-x-2">
                    <input type="checkbox" defaultChecked />
                    <span className="text-sm">Enable request logging</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input type="checkbox" defaultChecked />
                    <span className="text-sm">Enable performance metrics</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input type="checkbox" />
                    <span className="text-sm">Enable error alerts</span>
                  </label>
                </div>
              </div>

              <Button className="w-full">
                Save Settings
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Deploy Dialog */}
      {showDeployDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-2xl max-h-[80vh] overflow-y-auto">
            <CardHeader>
              <CardTitle>Deploy Agent</CardTitle>
              <p className="text-sm text-gray-600">Configure your agent deployment</p>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Basic Info */}
              <div className="space-y-4">
                <h3 className="font-medium">Basic Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="deploy-name">Deployment Name</Label>
                    <Input
                      id="deploy-name"
                      value={deployConfig.name}
                      onChange={(e) => setDeployConfig(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="My Agent Deployment"
                    />
                  </div>
                  <div>
                    <Label htmlFor="deploy-env">Environment</Label>
                    <Select
                      value={deployConfig.environment}
                      onValueChange={(value: 'development' | 'staging' | 'production') =>
                        setDeployConfig(prev => ({ ...prev, environment: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="development">Development</SelectItem>
                        <SelectItem value="staging">Staging</SelectItem>
                        <SelectItem value="production">Production</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div>
                  <Label htmlFor="deploy-description">Description</Label>
                  <Textarea
                    id="deploy-description"
                    value={deployConfig.description}
                    onChange={(e) => setDeployConfig(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Describe this deployment"
                    rows={2}
                  />
                </div>
              </div>

              <Separator />

              {/* Scaling */}
              <div className="space-y-4">
                <h3 className="font-medium">Scaling Configuration</h3>
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="min-instances">Min Instances</Label>
                    <Input
                      id="min-instances"
                      type="number"
                      value={deployConfig.scaling.minInstances}
                      onChange={(e) => setDeployConfig(prev => ({
                        ...prev,
                        scaling: { ...prev.scaling, minInstances: parseInt(e.target.value) || 1 }
                      }))}
                      min="1"
                      max="10"
                    />
                  </div>
                  <div>
                    <Label htmlFor="max-instances">Max Instances</Label>
                    <Input
                      id="max-instances"
                      type="number"
                      value={deployConfig.scaling.maxInstances}
                      onChange={(e) => setDeployConfig(prev => ({
                        ...prev,
                        scaling: { ...prev.scaling, maxInstances: parseInt(e.target.value) || 3 }
                      }))}
                      min="1"
                      max="20"
                    />
                  </div>
                  <div>
                    <Label htmlFor="target-cpu">Target CPU %</Label>
                    <Input
                      id="target-cpu"
                      type="number"
                      value={deployConfig.scaling.targetCPU}
                      onChange={(e) => setDeployConfig(prev => ({
                        ...prev,
                        scaling: { ...prev.scaling, targetCPU: parseInt(e.target.value) || 70 }
                      }))}
                      min="10"
                      max="90"
                    />
                  </div>
                </div>
              </div>

              <Separator />

              {/* Security */}
              <div className="space-y-4">
                <h3 className="font-medium">Security Settings</h3>
                <div className="space-y-2">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={deployConfig.security.apiKeyRequired}
                      onChange={(e) => setDeployConfig(prev => ({
                        ...prev,
                        security: { ...prev.security, apiKeyRequired: e.target.checked }
                      }))}
                    />
                    <span className="text-sm">Require API key authentication</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={deployConfig.security.rateLimiting}
                      onChange={(e) => setDeployConfig(prev => ({
                        ...prev,
                        security: { ...prev.security, rateLimiting: e.target.checked }
                      }))}
                    />
                    <span className="text-sm">Enable rate limiting</span>
                  </label>
                </div>
              </div>

              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowDeployDialog(false)}>
                  Cancel
                </Button>
                <Button
                  onClick={handleDeploy}
                  disabled={!deployConfig.name || isDeploying}
                >
                  {isDeploying ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      Deploying...
                    </>
                  ) : (
                    <>
                      <Rocket className="w-4 h-4 mr-2" />
                      Deploy Agent
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default AgentDeployment;
