'use client';

import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  Send,
  Bot,
  User,
  Zap,
  Clock,
  CheckCircle,
  AlertCircle,
  Loader2,
  Eye,
  Settings,
  Play,
  Square
} from 'lucide-react';

interface ChatMessage {
  id: string;
  type: 'user' | 'agent' | 'system';
  content: string;
  timestamp: string;
  node_id?: string;
  node_name?: string;
  execution_time?: number;
  metadata?: Record<string, any>;
}

interface ExecutionStep {
  node_id: string;
  node_name: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  start_time?: string;
  end_time?: string;
  duration_ms?: number;
  input_data?: any;
  output_data?: any;
  error_message?: string;
}

interface ChatInterfaceProps {
  agentId?: string;
  workflow?: {
    id: string;
    name: string;
    nodes: Array<{
      id: string;
      name: string;
      type: string;
    }>;
  };
  onExecutionUpdate?: (steps: ExecutionStep[]) => void;
  isConnected?: boolean;
  onConnect?: () => void;
  onDisconnect?: () => void;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({
  agentId,
  workflow,
  onExecutionUpdate,
  isConnected = false,
  onConnect,
  onDisconnect
}) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [executionSteps, setExecutionSteps] = useState<ExecutionStep[]>([]);
  const [showExecutionDetails, setShowExecutionDetails] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (onExecutionUpdate) {
      onExecutionUpdate(executionSteps);
    }
  }, [executionSteps, onExecutionUpdate]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      id: `msg_${Date.now()}`,
      type: 'user',
      content: inputMessage,
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    try {
      // Simulate agent execution with multiple nodes
      await simulateAgentExecution(inputMessage);
    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage: ChatMessage = {
        id: `msg_${Date.now()}`,
        type: 'system',
        content: 'Error: Failed to process message',
        timestamp: new Date().toISOString()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const simulateAgentExecution = async (userInput: string) => {
    if (!workflow) return;

    const steps: ExecutionStep[] = [];
    
    // Simulate execution through workflow nodes
    for (let i = 0; i < workflow.nodes.length; i++) {
      const node = workflow.nodes[i];
      const step: ExecutionStep = {
        node_id: node.id,
        node_name: node.name,
        status: 'running',
        start_time: new Date().toISOString()
      };

      steps.push(step);
      setExecutionSteps([...steps]);

      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

      // Update step as completed
      step.status = 'completed';
      step.end_time = new Date().toISOString();
      step.duration_ms = Math.floor(500 + Math.random() * 2000);
      step.output_data = { processed: true, node_type: node.type };

      setExecutionSteps([...steps]);

      // Add intermediate message for some nodes
      if (node.type === 'llm' || i === workflow.nodes.length - 1) {
        const nodeMessage: ChatMessage = {
          id: `msg_${Date.now()}_${node.id}`,
          type: 'agent',
          content: i === workflow.nodes.length - 1 
            ? `Based on your message "${userInput}", I've processed it through our ${workflow.nodes.length}-step workflow. Here's my response: This is a simulated response from the ${node.name} node.`
            : `Processing through ${node.name}...`,
          timestamp: new Date().toISOString(),
          node_id: node.id,
          node_name: node.name,
          execution_time: step.duration_ms
        };

        setMessages(prev => [...prev, nodeMessage]);
      }
    }
  };

  const handleConnect = () => {
    if (onConnect) {
      onConnect();
    }
  };

  const handleDisconnect = () => {
    if (onDisconnect) {
      onDisconnect();
    }
  };

  const getMessageIcon = (type: string) => {
    switch (type) {
      case 'user': return <User className="w-4 h-4" />;
      case 'agent': return <Bot className="w-4 h-4" />;
      case 'system': return <Settings className="w-4 h-4" />;
      default: return <Bot className="w-4 h-4" />;
    }
  };

  const getMessageBgColor = (type: string) => {
    switch (type) {
      case 'user': return 'bg-blue-50 border-blue-200';
      case 'agent': return 'bg-green-50 border-green-200';
      case 'system': return 'bg-yellow-50 border-yellow-200';
      default: return 'bg-gray-50 border-gray-200';
    }
  };

  const getStepStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="w-4 h-4 text-gray-400" />;
      case 'running': return <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'completed': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed': return <AlertCircle className="w-4 h-4 text-red-500" />;
      default: return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="border-b p-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-medium">Chat Test</h3>
            <p className="text-sm text-gray-600">
              {workflow ? `Testing ${workflow.name}` : 'No workflow selected'}
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowExecutionDetails(!showExecutionDetails)}
            >
              <Eye className="w-4 h-4 mr-1" />
              {showExecutionDetails ? 'Hide' : 'Show'} Details
            </Button>
            {isConnected ? (
              <Button variant="destructive" size="sm" onClick={handleDisconnect}>
                <Square className="w-4 h-4 mr-1" />
                Disconnect
              </Button>
            ) : (
              <Button size="sm" onClick={handleConnect}>
                <Play className="w-4 h-4 mr-1" />
                Connect
              </Button>
            )}
          </div>
        </div>
      </div>

      <div className="flex-1 flex">
        {/* Chat Area */}
        <div className="flex-1 flex flex-col">
          {/* Messages */}
          <ScrollArea className="flex-1 p-4">
            <div className="space-y-4">
              {messages.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <Bot className="w-12 h-12 mx-auto mb-3 opacity-50" />
                  <p>Start a conversation to test your agent</p>
                  <p className="text-sm">Messages will appear here as you chat</p>
                </div>
              )}

              {messages.map((message) => (
                <div key={message.id} className={`
                  p-3 rounded-lg border ${getMessageBgColor(message.type)}
                  ${message.type === 'user' ? 'ml-12' : 'mr-12'}
                `}>
                  <div className="flex items-start gap-2">
                    <div className="flex-shrink-0 mt-1">
                      {getMessageIcon(message.type)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-sm capitalize">
                          {message.type}
                        </span>
                        {message.node_name && (
                          <Badge variant="outline" className="text-xs">
                            {message.node_name}
                          </Badge>
                        )}
                        {message.execution_time && (
                          <span className="text-xs text-gray-500">
                            {message.execution_time}ms
                          </span>
                        )}
                        <span className="text-xs text-gray-500">
                          {new Date(message.timestamp).toLocaleTimeString()}
                        </span>
                      </div>
                      <p className="text-sm">{message.content}</p>
                    </div>
                  </div>
                </div>
              ))}

              {isLoading && (
                <div className="flex items-center gap-2 text-gray-500 mr-12">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span className="text-sm">Agent is thinking...</span>
                </div>
              )}

              <div ref={messagesEndRef} />
            </div>
          </ScrollArea>

          {/* Input */}
          <div className="border-t p-4">
            <div className="flex gap-2">
              <Input
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                placeholder="Type your message..."
                onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                disabled={!isConnected || isLoading}
              />
              <Button 
                onClick={handleSendMessage} 
                disabled={!inputMessage.trim() || !isConnected || isLoading}
              >
                <Send className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Execution Details Panel */}
        {showExecutionDetails && (
          <div className="w-80 border-l bg-gray-50">
            <div className="p-4 border-b bg-white">
              <h4 className="font-medium">Execution Flow</h4>
              <p className="text-sm text-gray-600">Real-time node execution</p>
            </div>
            <ScrollArea className="h-full p-4">
              <div className="space-y-3">
                {executionSteps.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <Zap className="w-8 h-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No execution steps yet</p>
                  </div>
                )}

                {executionSteps.map((step, index) => (
                  <Card key={`${step.node_id}_${index}`} className="p-3">
                    <div className="flex items-center gap-2 mb-2">
                      {getStepStatusIcon(step.status)}
                      <span className="font-medium text-sm">{step.node_name}</span>
                      <Badge variant="outline" className="text-xs">
                        {step.status}
                      </Badge>
                    </div>
                    
                    {step.duration_ms && (
                      <div className="text-xs text-gray-600">
                        Duration: {step.duration_ms}ms
                      </div>
                    )}
                    
                    {step.error_message && (
                      <div className="text-xs text-red-600 mt-1">
                        Error: {step.error_message}
                      </div>
                    )}
                  </Card>
                ))}
              </div>
            </ScrollArea>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatInterface;
