// frontend/src/components/features/AgentBuilder/WorkflowCanvas.tsx
'use client';

import React, { useCallback, useRef, useState, useEffect } from 'react';
import ReactFlow, {
  Node,
  Edge,
  addEdge,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  Connection,
  NodeTypes,
  EdgeTypes,
  ReactFlowProvider,
  useReactFlow,
  Panel,
  BackgroundVariant,
  ConnectionMode,
} from 'reactflow';
import 'reactflow/dist/style.css';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Brain,
  Database,
  Zap,
  Wrench,
  GitBranch,
  Search,
  ArrowDown,
  ArrowUp,
  Settings,
  Plus,
  Play,
  Save,
  Download,
  Upload,
} from 'lucide-react';

import { NodeConfig, NodeType as BackendNodeType } from '@/types/workflow'; // Consolidated import

// Custom Node Component
const CustomNode = ({ data, selected }: { data: any; selected: boolean }) => {
  const getNodeIcon = (type: string) => {
    switch (type) {
      case BackendNodeType.LLM:
        return <Brain className="w-4 h-4" />;
      case BackendNodeType.MEMORY:
        return <Database className="w-4 h-4" />;
      case BackendNodeType.DSPY_MODULE:
        return <Zap className="w-4 h-4" />;
      case BackendNodeType.TOOL:
        return <Wrench className="w-4 h-4" />;
      case BackendNodeType.CONDITIONAL:
        return <GitBranch className="w-4 h-4" />;
      case BackendNodeType.RAG:
        return <Search className="w-4 h-4" />;
      case BackendNodeType.INPUT:
        return <ArrowDown className="w-4 h-4" />;
      case BackendNodeType.OUTPUT:
        return <ArrowUp className="w-4 h-4" />;
      default:
        return <Settings className="w-4 h-4" />;
    }
  };

  const getNodeColor = (type: string) => {
    switch (type) {
      case BackendNodeType.LLM:
        return 'bg-blue-100 border-blue-300 text-blue-800';
      case BackendNodeType.MEMORY:
        return 'bg-green-100 border-green-300 text-green-800';
      case BackendNodeType.DSPY_MODULE:
        return 'bg-purple-100 border-purple-300 text-purple-800';
      case BackendNodeType.TOOL:
        return 'bg-orange-100 border-orange-300 text-orange-800';
      case BackendNodeType.CONDITIONAL:
        return 'bg-yellow-100 border-yellow-300 text-yellow-800';
      case BackendNodeType.RAG:
        return 'bg-indigo-100 border-indigo-300 text-indigo-800';
      case BackendNodeType.INPUT:
        return 'bg-gray-100 border-gray-300 text-gray-800';
      case BackendNodeType.OUTPUT:
        return 'bg-gray-100 border-gray-300 text-gray-800';
      default:
        return 'bg-gray-100 border-gray-300 text-gray-800';
    }
  };

  return (
    <Card
      className={`
      min-w-[200px] p-3 cursor-pointer transition-all duration-200
      ${getNodeColor(data.type)}
      ${selected ? 'ring-2 ring-blue-500 shadow-lg' : 'hover:shadow-md'}
    `}
    >
      <div className="flex items-center gap-2 mb-2">
        {getNodeIcon(data.type)}
        <span className="font-medium text-sm">{data.label}</span>
        <Badge variant="outline" className="text-xs">
          {data.type}
        </Badge>
      </div>

      {data.description && (
        <p className="text-xs text-gray-600 mb-2 line-clamp-2">{data.description}</p>
      )}

      <div className="flex items-center justify-between text-xs">
        <span className="text-gray-500">{data.status || 'Ready'}</span>
        {data.executionTime && <span className="text-gray-500">{data.executionTime}ms</span>}
      </div>

      {/* Connection handles are automatically added by ReactFlow */}
    </Card>
  );
};

// Custom Edge Component
const CustomEdge = ({ id, sourceX, sourceY, targetX, targetY, selected }: any) => {
  const edgePath = `M${sourceX},${sourceY} C${sourceX + 50},${sourceY} ${targetX - 50},${targetY} ${targetX},${targetY}`;

  return (
    <g>
      <path
        id={id}
        d={edgePath}
        stroke={selected ? '#3b82f6' : '#6b7280'}
        strokeWidth={selected ? 3 : 2}
        fill="none"
        markerEnd="url(#arrowhead)"
      />
      <defs>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
          <polygon points="0 0, 10 3.5, 0 7" fill={selected ? '#3b82f6' : '#6b7280'} />
        </marker>
      </defs>
    </g>
  );
};

const nodeTypes: NodeTypes = {
  custom: CustomNode,
};

const edgeTypes: EdgeTypes = {
  custom: CustomEdge,
};

interface WorkflowCanvasProps {
  workflow?: {
    nodes: NodeConfig[];
    connections: Record<string, any>;
  };
  onWorkflowChange?: (workflow: { nodes: NodeConfig[]; connections: Record<string, any> }) => void;
  onNodeSelect?: (node: NodeConfig | null) => void;
  onNodeEdit?: (node: NodeConfig) => void;
  isExecuting?: boolean;
  executionState?: {
    currentNodeId?: string;
    executionPath: string[];
    nodeMetrics: Array<{
      node_id: string;
      status: string;
      duration_ms?: number;
    }>;
  };
}

const WorkflowCanvasInner: React.FC<WorkflowCanvasProps> = ({
  workflow,
  onWorkflowChange,
  onNodeSelect,
  onNodeEdit,
  isExecuting,
  executionState,
}) => {
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const { project } = useReactFlow();

  // Convert NodeConfig to ReactFlow Node
  const convertToReactFlowNodes = useCallback(
    (nodeConfigs: NodeConfig[]): Node[] => {
      return nodeConfigs.map((nodeConfig, index) => ({
        id: nodeConfig.id,
        type: 'custom',
        position: nodeConfig.position
          ? { x: nodeConfig.position[0], y: nodeConfig.position[1] }
          : { x: 100 + index * 250, y: 100 },
        data: {
          label: nodeConfig.name,
          type: nodeConfig.type,
          description: nodeConfig.description,
          status:
            executionState?.nodeMetrics.find((m) => m.node_id === nodeConfig.id)?.status || 'ready',
          executionTime: executionState?.nodeMetrics.find((m) => m.node_id === nodeConfig.id)
            ?.duration_ms,
          config: nodeConfig,
        },
      }));
    },
    [executionState],
  );

  // Convert connections to ReactFlow edges
  const convertToReactFlowEdges = useCallback(
    (connections: Record<string, any>): Edge[] => {
      const edges: Edge[] = [];

      Object.entries(connections).forEach(([sourceId, connectionData]) => {
        if (connectionData.main) {
          connectionData.main.forEach((target: any, index: number) => {
            if (target.node !== 'END') {
              edges.push({
                id: `${sourceId}-${target.node}-${index}`,
                source: sourceId,
                target: target.node,
                type: 'custom',
                animated: executionState?.currentNodeId === sourceId,
              });
            }
          });
        }

        if (connectionData.conditional?.paths) {
          Object.entries(connectionData.conditional.paths).forEach(
            ([condition, targets]: [string, any]) => {
              targets.forEach((target: any, index: number) => {
                if (target.node !== 'END') {
                  edges.push({
                    id: `${sourceId}-${target.node}-${condition}-${index}`,
                    source: sourceId,
                    target: target.node,
                    type: 'custom',
                    label: condition,
                    animated: executionState?.currentNodeId === sourceId,
                  });
                }
              });
            },
          );
        }
      });

      return edges;
    },
    [executionState],
  );

  const [nodes, setNodes, onNodesChange] = useNodesState(
    workflow ? convertToReactFlowNodes(workflow.nodes) : [],
  );
  const [edges, setEdges, onEdgesChange] = useEdgesState(
    workflow ? convertToReactFlowEdges(workflow.connections) : [],
  );

  // Update nodes when workflow changes
  useEffect(() => {
    if (workflow) {
      setNodes(convertToReactFlowNodes(workflow.nodes));
      setEdges(convertToReactFlowEdges(workflow.connections));
    }
  }, [workflow, convertToReactFlowNodes, convertToReactFlowEdges, setNodes, setEdges]);

  // Update nodes when execution state changes
  useEffect(() => {
    if (workflow && executionState) {
      setNodes(convertToReactFlowNodes(workflow.nodes));
    }
  }, [executionState, workflow, convertToReactFlowNodes, setNodes]);

  const onConnect = useCallback(
    (params: Connection) => {
      const newEdge = {
        ...params,
        type: 'custom',
        id: `${params.source}-${params.target}`,
      };
      setEdges((eds) => addEdge(newEdge, eds));

      // Update workflow connections
      if (onWorkflowChange && workflow) {
        const newConnections = { ...workflow.connections };
        if (!newConnections[params.source!]) {
          newConnections[params.source!] = { main: [] };
        }
        if (!newConnections[params.source!].main) {
          newConnections[params.source!].main = [];
        }
        newConnections[params.source!].main.push({
          node: params.target!,
          type: 'main',
          index: 0,
        });

        onWorkflowChange({
          nodes: workflow.nodes,
          connections: newConnections,
        });
      }
    },
    [setEdges, onWorkflowChange, workflow],
  );

  const onNodeClick = useCallback(
    (event: React.MouseEvent, node: Node) => {
      if (onNodeSelect) {
        onNodeSelect(node.data.config);
      }
    },
    [onNodeSelect],
  );

  const onNodeDoubleClick = useCallback(
    (event: React.MouseEvent, node: Node) => {
      if (onNodeEdit) {
        onNodeEdit(node.data.config);
      }
    },
    [onNodeEdit],
  );

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();

      const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect();
      if (!reactFlowBounds) return;

      const type = event.dataTransfer.getData('application/reactflow');
      const nodeDataString = event.dataTransfer.getData('application/json');

      if (!type || !nodeDataString) return;

      const droppedNodeData = JSON.parse(nodeDataString);

      const position = project({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      });

      const newNode: Node = {
        id: `${droppedNodeData.type}_${Date.now()}`,
        type: 'custom',
        position,
        data: {
          label: droppedNodeData.name,
          type: droppedNodeData.type,
          description: droppedNodeData.description,
          status: 'ready',
          config: {
            id: `${droppedNodeData.type}_${Date.now()}`,
            name: droppedNodeData.name,
            type: droppedNodeData.type as BackendNodeType,
            parameters: droppedNodeData.parameters || {},
            dependencies: [], // Default empty, can be configured in NodeEditor
            description: droppedNodeData.description,
            category: droppedNodeData.category,
            ui_config: {},
            // For tool nodes, ensure 'tools' array is populated
            tools: droppedNodeData.type === BackendNodeType.TOOL ? [droppedNodeData.name] : [],
            // For LLM nodes, ensure 'system_prompt' is populated
            system_prompt: droppedNodeData.type === BackendNodeType.LLM ? droppedNodeData.system_prompt || 'You are a helpful AI assistant.' : undefined,
            // For DSPy nodes, ensure 'dspy_config' is populated
            dspy_config: droppedNodeData.type === BackendNodeType.DSPY_MODULE ? droppedNodeData.dspy_config || { module_type: 'predict', signature: 'input -> output', examples: [] } : undefined,
            // For Memory nodes, ensure 'memory_config' is populated
            memory_config: droppedNodeData.type === BackendNodeType.MEMORY ? droppedNodeData.memory_config || { type: 'short_term' } : undefined,
            // For PythonFunction nodes, ensure 'functionPath' is populated
            functionPath: droppedNodeData.type === BackendNodeType.PYTHON_FUNCTION ? droppedNodeData.functionPath : undefined,
          },
        },
      };

      setNodes((nds) => nds.concat(newNode));
    },
    [project, setNodes],
  );

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  return (
    <div className="w-full h-full" ref={reactFlowWrapper}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onNodeClick={onNodeClick}
        onNodeDoubleClick={onNodeDoubleClick}
        onDrop={onDrop}
        onDragOver={onDragOver}
        onPaneClick={() => onNodeSelect && onNodeSelect(null)}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        fitView
        attributionPosition="bottom-left"
        snapToGrid={true}
        snapGrid={[15, 15]}
        connectionMode={ConnectionMode.Loose}
        defaultViewport={{ x: 0, y: 0, zoom: 1 }}
        minZoom={0.1}
        maxZoom={2}
        panOnScroll={true}
        panOnScrollSpeed={0.5}
        zoomOnScroll={true}
        zoomOnPinch={true}
        zoomOnDoubleClick={true}
        selectNodesOnDrag={false}
        multiSelectionKeyCode="Shift"
        deleteKeyCode="Delete"
      >
        <Controls
          showZoom={true}
          showFitView={true}
          showInteractive={true}
          position="bottom-right"
        />
        <MiniMap
          nodeColor={(node) => {
            switch (node.data?.config?.type) {
              case BackendNodeType.LLM:
                return '#3b82f6';
              case BackendNodeType.MEMORY:
                return '#10b981';
              case BackendNodeType.TOOL:
                return '#f59e0b';
              case BackendNodeType.CONDITIONAL:
                return '#ef4444';
              default:
                return '#6b7280';
            }
          }}
          nodeStrokeWidth={3}
          nodeBorderRadius={2}
          position="bottom-left"
          pannable={true}
          zoomable={true}
        />
        <Background variant={BackgroundVariant.Dots} gap={20} size={1} color="#e5e7eb" />

        {isExecuting && executionState && (
          <Panel position="top-right">
            <Card className="p-3 bg-white shadow-md">
              <div className="text-sm font-medium mb-2">Execution Status</div>
              <div className="text-xs text-gray-600">
                Current: {executionState.currentNodeId || 'None'}
              </div>
              <div className="text-xs text-gray-600">
                Path: {executionState.executionPath.join(' → ')}
              </div>
            </Card>
          </Panel>
        )}
      </ReactFlow>
    </div>
  );
};

const WorkflowCanvas: React.FC<WorkflowCanvasProps> = (props) => {
  return (
    <ReactFlowProvider>
      <WorkflowCanvasInner {...props} />
    </ReactFlowProvider>
  );
};

export default WorkflowCanvas;