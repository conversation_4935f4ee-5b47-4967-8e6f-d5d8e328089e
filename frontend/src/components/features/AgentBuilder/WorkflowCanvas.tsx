// frontend/src/components/features/AgentBuilder/WorkflowCanvas.tsx
'use client';

import React from 'react';
import { ReactFlowProvider } from 'reactflow';
import EnhancedWorkflowCanvas from './ReactFlow/EnhancedWorkflowCanvas';
import { NodeConfig, Workflow, AgentExecutionState } from '@/types/workflow';

interface WorkflowCanvasProps {
  workflow: Workflow;
  onWorkflowChange: (workflow: Workflow) => void;
  onNodeSelect?: (node: NodeConfig | null) => void;
  onNodeEdit?: (node: NodeConfig) => void;
  onNodeTest?: (node: NodeConfig) => void;
  executionState?: AgentExecutionState;
  isExecuting?: boolean;
  onExecute?: () => void;
  onStop?: () => void;
  className?: string;
}

/**
 * WorkflowCanvas - Enhanced React Flow wrapper for agent workflow editing
 *
 * This component provides a comprehensive visual editor for agent workflows with:
 * - Drag and drop node creation
 * - Visual connection management
 * - Real-time execution state visualization
 * - Node testing and validation
 * - LangGraph-compatible edge types (main and conditional)
 */
const WorkflowCanvas: React.FC<WorkflowCanvasProps> = (props) => {
  return (
    <ReactFlowProvider>
      <EnhancedWorkflowCanvas {...props} />
    </ReactFlowProvider>
  );
};

export default WorkflowCanvas;
