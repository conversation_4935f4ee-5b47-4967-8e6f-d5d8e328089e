// frontend/src/components/features/AgentBuilder/AgentJsonViewers.tsx
'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Copy, Download } from 'lucide-react';
import { toast } from 'react-hot-toast';

import { NodeConfig } from '@/types/workflow';

interface AgentJsonViewersProps {
  currentWorkflow: {
    name: string;
    id: string;
    nodes: NodeConfig[];
    connections: Record<string, any>;
  };
  selectedNode: NodeConfig | null;
  showAgentJsonDialog: boolean;
  setShowAgentJsonDialog: (show: boolean) => void;
  showNodeJsonDialog: boolean;
  setShowNodeJsonDialog: (show: boolean) => void;
}

const AgentJsonViewers: React.FC<AgentJsonViewersProps> = ({
  currentWorkflow,
  selectedNode,
  showAgentJsonDialog,
  setShowAgentJsonDialog,
  showNodeJsonDialog,
  setShowNodeJsonDialog,
}) => {
  return (
    <>
      {/* Agent JSON Dialog */}
      <Dialog open={showAgentJsonDialog} onOpenChange={setShowAgentJsonDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Agent Configuration JSON</DialogTitle>
            <p className="text-sm text-gray-600">
              Complete agent configuration including workflow, nodes, and connections
            </p>
          </DialogHeader>
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  navigator.clipboard.writeText(JSON.stringify(currentWorkflow, null, 2));
                  toast.success('Agent JSON copied to clipboard!');
                }}
              >
                <Copy className="w-4 h-4 mr-2" />
                Copy JSON
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  const blob = new Blob([JSON.stringify(currentWorkflow, null, 2)], {
                    type: 'application/json',
                  });
                  const url = URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `${currentWorkflow.name || 'agent'}-config.json`;
                  document.body.appendChild(a);
                  a.click();
                  document.body.removeChild(a);
                  URL.revokeObjectURL(url);
                  toast.success('Agent JSON downloaded!');
                }}
              >
                <Download className="w-4 h-4 mr-2" />
                Download JSON
              </Button>
            </div>
            <pre className="bg-gray-100 p-4 rounded-md text-sm overflow-auto max-h-[60vh] border">
              <code>{JSON.stringify(currentWorkflow, null, 2)}</code>
            </pre>
          </div>
        </DialogContent>
      </Dialog>

      {/* Node JSON Dialog */}
      <Dialog open={showNodeJsonDialog} onOpenChange={setShowNodeJsonDialog}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Node JSON</DialogTitle>
          </DialogHeader>
          {selectedNode && (
            <pre className="bg-gray-100 p-4 rounded-md text-sm overflow-auto max-h-[70vh]">
              <code>{JSON.stringify(selectedNode, null, 2)}</code>
            </pre>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default AgentJsonViewers;
