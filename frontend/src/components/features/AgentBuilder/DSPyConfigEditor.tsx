// frontend/src/components/features/AgentBuilder/DSPyConfigEditor.tsx
'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Zap,
  Brain,
  Target,
  BookOpen,
  Settings,
  Plus,
  Trash2,
  Play,
  Save,
  Info,
  Lightbulb,
  Code,
} from 'lucide-react';

import { DSPyConfig, DSPyModuleType } from '@/types/workflow'; // Consolidated import

interface DSPyConfigEditorProps {
  config: DSPyConfig;
  onChange: (config: DSPyConfig) => void;
  onTest?: (config: DSPyConfig) => void;
  onOptimize?: (config: DSPyConfig) => void;
  availableModules?: Array<{
    type: DSPyModuleType; // Use the enum
    name: string;
    description: string;
    signature: string;
    examples: string[];
  }>;
}

const defaultConfig: DSPyConfig = {
  enabled: false,
  module_type: DSPyModuleType.PREDICT, // Use enum value
  signature: 'input -> output',
  examples: [],
  optimizer: 'bootstrap',
  max_bootstrapped_demos: 4,
  max_labeled_demos: 16,
  optimization_metric: 'accuracy',
  auto_optimize: false,
  optimization_examples: [],
};

const DSPyConfigEditor: React.FC<DSPyConfigEditorProps> = ({
  config,
  onChange,
  onTest,
  onOptimize,
  availableModules = [],
}) => {
  const [localConfig, setLocalConfig] = useState<DSPyConfig>(config || defaultConfig);
  const [activeTab, setActiveTab] = useState('basic');

  useEffect(() => {
    onChange(localConfig);
  }, [localConfig, onChange]);

  const updateConfig = (path: string, value: any) => {
    const keys = path.split('.');
    const newConfig = { ...localConfig };
    let current: any = newConfig;

    for (let i = 0; i < keys.length - 1; i++) {
      current = current[keys[i]];
    }
    current[keys[keys.length - 1]] = value;

    setLocalConfig(newConfig);
  };

  const addExample = () => {
    const newExample = {
      input: { input: '' },
      output: '',
      quality_score: 1.0,
    };
    updateConfig('examples', [...localConfig.examples, newExample]);
  };

  const removeExample = (index: number) => {
    const newExamples = localConfig.examples.filter((_, i) => i !== index);
    updateConfig('examples', newExamples);
  };

  const updateExample = (index: number, field: string, value: any) => {
    const newExamples = [...localConfig.examples];
    if (field === 'input') {
      newExamples[index].input = { input: value };
    } else {
      (newExamples[index] as any)[field] = value;
    }
    updateConfig('examples', newExamples);
  };

  const addOptimizationExample = () => {
    const newExample = {
      input_data: { input: '' },
      expected_output: '',
      quality_score: 1.0,
    };
    updateConfig('optimization_examples', [...localConfig.optimization_examples, newExample]);
  };

  const removeOptimizationExample = (index: number) => {
    const newExamples = localConfig.optimization_examples.filter((_, i) => i !== index);
    updateConfig('optimization_examples', newExamples);
  };

  const updateOptimizationExample = (index: number, field: string, value: any) => {
    const newExamples = [...localConfig.optimization_examples];
    if (field === 'input_data') {
      newExamples[index].input_data = { input: value };
    } else {
      (newExamples[index] as any)[field] = value;
    }
    updateConfig('optimization_examples', newExamples);
  };

  const getModuleIcon = (type: string) => {
    switch (type) {
      case DSPyModuleType.PREDICT:
        return <Target className="w-4 h-4" />;
      case DSPyModuleType.CHAIN_OF_THOUGHT:
        return <Brain className="w-4 h-4" />;
      case DSPyModuleType.REACT:
        return <Zap className="w-4 h-4" />;
      case DSPyModuleType.RETRIEVE:
        return <BookOpen className="w-4 h-4" />;
      default:
        return <Settings className="w-4 h-4" />;
    }
  };

  const getModuleDescription = (type: string) => {
    switch (type) {
      case DSPyModuleType.PREDICT:
        return 'Direct prediction without reasoning steps';
      case DSPyModuleType.CHAIN_OF_THOUGHT:
        return 'Step-by-step reasoning before prediction';
      case DSPyModuleType.REACT:
        return 'Reasoning and acting with tool usage';
      case DSPyModuleType.RETRIEVE:
        return 'Information retrieval from knowledge base';
      case DSPyModuleType.GENERATE:
        return 'Text generation with context';
      case DSPyModuleType.CLASSIFY:
        return 'Classification with multiple categories';
      case DSPyModuleType.SUMMARIZE:
        return 'Text summarization and condensation';
      default:
        return 'DSPy module for AI processing';
    }
  };

  const selectedModule = availableModules.find((m) => m.type === localConfig.module_type);

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Zap className="w-5 h-5" />
            DSPy Configuration
          </CardTitle>
          <div className="flex items-center gap-2">
            <Switch
              checked={localConfig.enabled}
              onCheckedChange={(checked) => updateConfig('enabled', checked)}
            />
            {onTest && localConfig.enabled && (
              <Button size="sm" variant="outline" onClick={() => onTest(localConfig)}>
                <Play className="w-4 h-4 mr-1" />
                Test
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {!localConfig.enabled && (
          <div className="text-center py-8 text-gray-500">
            <Zap className="w-12 h-12 mx-auto mb-3 opacity-50" />
            <p>DSPy is disabled</p>
            <p className="text-sm">Enable DSPy to configure advanced AI modules with optimization</p>
          </div>
        )}

        {localConfig.enabled && (
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="basic">Basic</TabsTrigger>
              <TabsTrigger value="examples">Examples</TabsTrigger>
              <TabsTrigger value="optimization">Optimization</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Module Type</Label>
                  <Select
                    value={localConfig.module_type}
                    onValueChange={(value: DSPyModuleType) => updateConfig('module_type', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.values(DSPyModuleType).map((type) => (
                        <SelectItem key={type} value={type}>
                          <div className="flex items-center gap-2">
                            {getModuleIcon(type)}
                            {type.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase())}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Optimizer</Label>
                  <Select
                    value={localConfig.optimizer}
                    onValueChange={(value) => updateConfig('optimizer', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="bootstrap">Bootstrap Few-Shot</SelectItem>
                      <SelectItem value="teleprompt">Teleprompt</SelectItem>
                      <SelectItem value="random_search">Random Search</SelectItem>
                      <SelectItem value="bayesian">Bayesian Optimization</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label>Signature</Label>
                <Input
                  value={localConfig.signature}
                  onChange={(e) => updateConfig('signature', e.target.value)}
                  placeholder="input -> output"
                />
                <p className="text-xs text-gray-500 mt-1">
                  {'Define input and output fields (e.g., "question, context -> answer")'}
                </p>
              </div>

              {selectedModule && (
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    {getModuleIcon(selectedModule.type)}
                    <h4 className="font-medium">{selectedModule.name}</h4>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{selectedModule.description}</p>
                  <div className="text-xs text-gray-500">
                    <strong>Suggested signature:</strong> {selectedModule.signature}
                  </div>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Max Bootstrapped Demos</Label>
                  <Input
                    type="number"
                    value={localConfig.max_bootstrapped_demos}
                    onChange={(e) => updateConfig('max_bootstrapped_demos', parseInt(e.target.value))}
                    min={1}
                    max={20}
                  />
                </div>
                <div>
                  <Label>Max Labeled Demos</Label>
                  <Input
                    type="number"
                    value={localConfig.max_labeled_demos}
                    onChange={(e) => updateConfig('max_labeled_demos', parseInt(e.target.value))}
                    min={1}
                    max={50}
                  />
                </div>
              </div>

              <div>
                <Label>Optimization Metric</Label>
                <Select
                  value={localConfig.optimization_metric}
                  onValueChange={(value) => updateConfig('optimization_metric', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="accuracy">Accuracy</SelectItem>
                    <SelectItem value="f1">F1 Score</SelectItem>
                    <SelectItem value="bleu">BLEU Score</SelectItem>
                    <SelectItem value="rouge">ROUGE Score</SelectItem>
                    <SelectItem value="semantic_similarity">Semantic Similarity</SelectItem>
                    <SelectItem value="custom">Custom Metric</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  checked={localConfig.auto_optimize}
                  onCheckedChange={(checked) => updateConfig('auto_optimize', checked)}
                />
                <Label>Auto-optimize with new examples</Label>
              </div>
            </TabsContent>

            <TabsContent value="examples" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="font-medium">Training Examples</h3>
                <Button size="sm" onClick={addExample}>
                  <Plus className="w-4 h-4 mr-1" />
                  Add Example
                </Button>
              </div>

              <ScrollArea className="h-96">
                <div className="space-y-4">
                  {localConfig.examples.map((example, index) => (
                    <Card key={index} className="p-4">
                      <div className="flex items-center justify-between mb-3">
                        <Badge variant="outline">Example {index + 1}</Badge>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => removeExample(index)}
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </div>

                      <div className="space-y-3">
                        <div>
                          <Label className="text-xs">Input</Label>
                          <Textarea
                            value={example.input.input || ''}
                            onChange={(e) => updateExample(index, 'input', e.target.value)}
                            placeholder="Enter input example"
                            rows={2}
                          />
                        </div>

                        <div>
                          <Label className="text-xs">Expected Output</Label>
                          <Textarea
                            value={example.output}
                            onChange={(e) => updateExample(index, 'output', e.target.value)}
                            placeholder="Enter expected output"
                            rows={2}
                          />
                        </div>

                        <div>
                          <Label className="text-xs">Quality Score (0-1)</Label>
                          <Input
                            type="number"
                            step="0.1"
                            min="0"
                            max="1"
                            value={example.quality_score || 1.0}
                            onChange={(e) => updateExample(index, 'quality_score', parseFloat(e.target.value))}
                          />
                        </div>
                      </div>
                    </Card>
                  ))}

                  {localConfig.examples.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      <BookOpen className="w-8 h-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No examples added yet</p>
                      <p className="text-xs">Add examples to improve DSPy module performance</p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="optimization" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="font-medium">Optimization Settings</h3>
                {onOptimize && (
                  <Button size="sm" onClick={() => onOptimize(localConfig)}>
                    <Lightbulb className="w-4 h-4 mr-1" />
                    Optimize Now
                  </Button>
                )}
              </div>

              <div className="bg-yellow-50 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Info className="w-4 h-4 text-yellow-600" />
                  <h4 className="font-medium text-yellow-800">Optimization Examples</h4>
                </div>
                <p className="text-sm text-yellow-700">
                  These examples are used specifically for optimizing the module's performance.
                  They should represent real-world usage patterns.
                </p>
              </div>

              <div className="flex items-center justify-between">
                <Label>Optimization Examples</Label>
                <Button size="sm" variant="outline" onClick={addOptimizationExample}>
                  <Plus className="w-4 h-4 mr-1" />
                  Add
                </Button>
              </div>

              <ScrollArea className="h-80">
                <div className="space-y-4">
                  {localConfig.optimization_examples.map((example, index) => (
                    <Card key={index} className="p-4">
                      <div className="flex items-center justify-between mb-3">
                        <Badge variant="secondary">Optimization {index + 1}</Badge>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => removeOptimizationExample(index)}
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </div>

                      <div className="space-y-3">
                        <div>
                          <Label className="text-xs">Input Data</Label>
                          <Textarea
                            value={example.input_data.input || ''}
                            onChange={(e) => updateOptimizationExample(index, 'input_data', e.target.value)}
                            placeholder="Enter input for optimization"
                            rows={2}
                          />
                        </div>

                        <div>
                          <Label className="text-xs">Expected Output</Label>
                          <Textarea
                            value={example.expected_output}
                            onChange={(e) => updateOptimizationExample(index, 'expected_output', e.target.value)}
                            placeholder="Enter expected output"
                            rows={2}
                          />
                        </div>

                        <div>
                          <Label className="text-xs">Quality Score</Label>
                          <Input
                            type="number"
                            step="0.1"
                            min="0"
                            max="1"
                            value={example.quality_score || 1.0}
                            onChange={(e) => updateOptimizationExample(index, 'quality_score', parseFloat(e.target.value))}
                          />
                        </div>
                      </div>
                    </Card>
                  ))}

                  {localConfig.optimization_examples.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      <Lightbulb className="w-8 h-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No optimization examples</p>
                      <p className="text-xs">Add examples to enable prompt optimization</p>
                    </div>
                  )}
                </div>
              </ScrollArea>

              <Separator />

              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium mb-2 flex items-center gap-2">
                  <Code className="w-4 h-4" />
                  Configuration Summary
                </h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Module:</span>
                    <div className="font-medium">{localConfig.module_type}</div>
                  </div>
                  <div>
                    <span className="text-gray-600">Optimizer:</span>
                    <div className="font-medium">{localConfig.optimizer}</div>
                  </div>
                  <div>
                    <span className="text-gray-600">Examples:</span>
                    <div className="font-medium">{localConfig.examples.length}</div>
                  </div>
                  <div>
                    <span className="text-gray-600">Optimization Examples:</span>
                    <div className="font-medium">{localConfig.optimization_examples.length}</div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        )}
      </CardContent>
    </Card>
  );
};

export default DSPyConfigEditor;