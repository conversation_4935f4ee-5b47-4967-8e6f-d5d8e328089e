// frontend/src/components/features/AgentBuilder/NodeTestingPanel.tsx
'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Play,
  Square,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Loader2,
  Clock,
  Zap,
  FileText,
  Plus,
  Trash2,
  Copy,
  Download,
} from 'lucide-react';

import { NodeConfig, NodeTestResult, NodeValidationResult } from '@/types/workflow';
import { useNodeTesting, generateTestInput, createNodeTestSuite } from '@/hooks/useNodeTesting';

interface NodeTestingPanelProps {
  selectedNode: NodeConfig | null;
  onClose?: () => void;
  className?: string;
}

interface TestCase {
  id: string;
  name: string;
  input: string; // JSON string
  expectedOutput?: string; // JSON string
  description?: string;
}

const NodeTestingPanel: React.FC<NodeTestingPanelProps> = ({
  selectedNode,
  onClose,
  className = '',
}) => {
  const {
    isTestingNode,
    testResult,
    testError,
    validationResult,
    testNode,
    validateNode,
    clearTestResult,
    createTestCase,
    runTestCase,
  } = useNodeTesting();

  const [activeTab, setActiveTab] = useState('single');
  const [testInput, setTestInput] = useState('{}');
  const [testCases, setTestCases] = useState<TestCase[]>([]);
  const [selectedTestCase, setSelectedTestCase] = useState<string | null>(null);
  const [newTestCaseName, setNewTestCaseName] = useState('');

  // Initialize test input when node changes
  useEffect(() => {
    if (selectedNode) {
      const defaultInput = generateTestInput(selectedNode.type);
      setTestInput(JSON.stringify(defaultInput, null, 2));
      validateNode(selectedNode);
      
      // Load existing test cases for this node (from localStorage for now)
      const savedTestCases = localStorage.getItem(`testCases_${selectedNode.id}`);
      if (savedTestCases) {
        setTestCases(JSON.parse(savedTestCases));
      } else {
        // Create default test cases
        const suite = createNodeTestSuite(selectedNode);
        const defaultTestCases = suite.testCases.map((tc, index) => ({
          id: `${selectedNode.id}_test_${index}`,
          name: tc.name,
          input: JSON.stringify(tc.input, null, 2),
          description: tc.description,
        }));
        setTestCases(defaultTestCases);
      }
    }
  }, [selectedNode, validateNode]);

  // Save test cases to localStorage when they change
  useEffect(() => {
    if (selectedNode && testCases.length > 0) {
      localStorage.setItem(`testCases_${selectedNode.id}`, JSON.stringify(testCases));
    }
  }, [testCases, selectedNode]);

  const handleRunTest = async () => {
    if (!selectedNode) return;

    try {
      const inputData = JSON.parse(testInput);
      await testNode(selectedNode, inputData);
    } catch (error) {
      console.error('Invalid JSON input:', error);
    }
  };

  const handleRunTestCase = async (testCase: TestCase) => {
    if (!selectedNode) return;

    try {
      const inputData = JSON.parse(testCase.input);
      const expectedOutput = testCase.expectedOutput ? JSON.parse(testCase.expectedOutput) : undefined;
      
      const nodeTestCase = createTestCase(selectedNode, inputData, expectedOutput);
      await runTestCase(selectedNode, nodeTestCase);
    } catch (error) {
      console.error('Error running test case:', error);
    }
  };

  const handleAddTestCase = () => {
    if (!selectedNode || !newTestCaseName.trim()) return;

    const newTestCase: TestCase = {
      id: `${selectedNode.id}_test_${Date.now()}`,
      name: newTestCaseName,
      input: testInput,
      description: `Test case for ${selectedNode.name}`,
    };

    setTestCases(prev => [...prev, newTestCase]);
    setNewTestCaseName('');
  };

  const handleDeleteTestCase = (testCaseId: string) => {
    setTestCases(prev => prev.filter(tc => tc.id !== testCaseId));
    if (selectedTestCase === testCaseId) {
      setSelectedTestCase(null);
    }
  };

  const handleCopyTestCase = (testCase: TestCase) => {
    const copy: TestCase = {
      ...testCase,
      id: `${testCase.id}_copy_${Date.now()}`,
      name: `${testCase.name} (Copy)`,
    };
    setTestCases(prev => [...prev, copy]);
  };

  const getStatusIcon = (result: NodeTestResult | null) => {
    if (!result) return <Clock className="w-4 h-4 text-gray-400" />;
    
    if (result.success) {
      return <CheckCircle className="w-4 h-4 text-green-500" />;
    } else {
      return <XCircle className="w-4 h-4 text-red-500" />;
    }
  };

  const exportTestResults = () => {
    if (!testResult || !selectedNode) return;

    const exportData = {
      nodeId: selectedNode.id,
      nodeName: selectedNode.name,
      nodeType: selectedNode.type,
      testResult,
      timestamp: new Date().toISOString(),
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `test-results-${selectedNode.name}-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  if (!selectedNode) {
    return (
      <Card className={`w-full h-full ${className}`}>
        <CardContent className="flex items-center justify-center h-full">
          <div className="text-center text-gray-500">
            <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>Select a node to start testing</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`w-full h-full ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Zap className="w-5 h-5" />
            <CardTitle className="text-lg">Node Testing</CardTitle>
            <Badge variant="outline">{selectedNode.name}</Badge>
          </div>
          <div className="flex items-center gap-2">
            {testResult && (
              <Button size="sm" variant="outline" onClick={exportTestResults}>
                <Download className="w-4 h-4 mr-1" />
                Export
              </Button>
            )}
            {onClose && (
              <Button size="sm" variant="outline" onClick={onClose}>
                Close
              </Button>
            )}
          </div>
        </div>

        {/* Validation Status */}
        {validationResult && (
          <div className={`p-2 rounded-lg text-sm ${
            validationResult.isValid 
              ? 'bg-green-50 text-green-800 border border-green-200' 
              : 'bg-red-50 text-red-800 border border-red-200'
          }`}>
            <div className="flex items-center gap-2">
              {validationResult.isValid ? (
                <CheckCircle className="w-4 h-4" />
              ) : (
                <AlertTriangle className="w-4 h-4" />
              )}
              <span className="font-medium">
                {validationResult.isValid ? 'Node configuration is valid' : 'Configuration issues found'}
              </span>
            </div>
            {validationResult.errors.length > 0 && (
              <div className="mt-1 ml-6">
                {validationResult.errors.map((error, index) => (
                  <div key={index}>• {error}</div>
                ))}
              </div>
            )}
          </div>
        )}
      </CardHeader>

      <CardContent className="p-0">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2 mx-4 mb-4">
            <TabsTrigger value="single">Single Test</TabsTrigger>
            <TabsTrigger value="cases">Test Cases</TabsTrigger>
          </TabsList>

          <div className="px-4 pb-4">
            <TabsContent value="single" className="space-y-4 mt-0">
              {/* Test Input */}
              <div>
                <Label htmlFor="test-input">Test Input (JSON)</Label>
                <Textarea
                  id="test-input"
                  value={testInput}
                  onChange={(e) => setTestInput(e.target.value)}
                  placeholder='{"message": "Hello, world!"}'
                  className="font-mono text-sm"
                  rows={6}
                />
              </div>

              {/* Test Button */}
              <div className="flex items-center gap-2">
                <Button
                  onClick={handleRunTest}
                  disabled={isTestingNode || !validationResult?.isValid}
                  className="flex items-center gap-2"
                >
                  {isTestingNode ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <Play className="w-4 h-4" />
                  )}
                  {isTestingNode ? 'Testing...' : 'Run Test'}
                </Button>
                
                {testResult && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={clearTestResult}
                  >
                    Clear
                  </Button>
                )}
              </div>

              {/* Test Results */}
              {(testResult || testError) && (
                <div className={`p-4 rounded-lg border ${
                  testResult?.success 
                    ? 'bg-green-50 border-green-200' 
                    : 'bg-red-50 border-red-200'
                }`}>
                  <div className="flex items-center gap-2 mb-3">
                    {getStatusIcon(testResult)}
                    <span className={`font-medium ${
                      testResult?.success ? 'text-green-800' : 'text-red-800'
                    }`}>
                      {testResult?.success ? 'Test Passed' : 'Test Failed'}
                    </span>
                    {testResult?.executionTime && (
                      <Badge variant="outline" className="ml-auto">
                        {testResult.executionTime}ms
                      </Badge>
                    )}
                  </div>

                  {testResult?.output && (
                    <div className="mb-3">
                      <div className="text-sm font-medium text-gray-700 mb-1">Output:</div>
                      <ScrollArea className="h-32">
                        <pre className="text-xs bg-white p-2 rounded border overflow-x-auto">
                          {JSON.stringify(testResult.output, null, 2)}
                        </pre>
                      </ScrollArea>
                    </div>
                  )}

                  {(testResult?.error || testError) && (
                    <div className="mb-3">
                      <div className="text-sm font-medium text-red-700 mb-1">Error:</div>
                      <div className="text-sm text-red-600 bg-red-100 p-2 rounded">
                        {testResult?.error || testError}
                      </div>
                    </div>
                  )}

                  {testResult?.logs && testResult.logs.length > 0 && (
                    <div>
                      <div className="text-sm font-medium text-gray-700 mb-1">Logs:</div>
                      <ScrollArea className="h-24">
                        <div className="text-xs bg-gray-100 p-2 rounded">
                          {testResult.logs.map((log, index) => (
                            <div key={index} className="mb-1">{log}</div>
                          ))}
                        </div>
                      </ScrollArea>
                    </div>
                  )}
                </div>
              )}
            </TabsContent>

            <TabsContent value="cases" className="space-y-4 mt-0">
              {/* Add New Test Case */}
              <div className="flex items-center gap-2">
                <Input
                  placeholder="Test case name"
                  value={newTestCaseName}
                  onChange={(e) => setNewTestCaseName(e.target.value)}
                  className="flex-1"
                />
                <Button
                  size="sm"
                  onClick={handleAddTestCase}
                  disabled={!newTestCaseName.trim()}
                >
                  <Plus className="w-4 h-4 mr-1" />
                  Add
                </Button>
              </div>

              {/* Test Cases List */}
              <ScrollArea className="h-96">
                <div className="space-y-2">
                  {testCases.map((testCase) => (
                    <Card key={testCase.id} className="p-3">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-sm">{testCase.name}</span>
                          {selectedTestCase === testCase.id && (
                            <Badge variant="secondary" className="text-xs">Selected</Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-1">
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleRunTestCase(testCase)}
                            disabled={isTestingNode}
                          >
                            <Play className="w-3 h-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleCopyTestCase(testCase)}
                          >
                            <Copy className="w-3 h-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleDeleteTestCase(testCase.id)}
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                      
                      {testCase.description && (
                        <p className="text-xs text-gray-600 mb-2">{testCase.description}</p>
                      )}
                      
                      <div className="text-xs bg-gray-50 p-2 rounded font-mono">
                        {testCase.input.substring(0, 100)}
                        {testCase.input.length > 100 && '...'}
                      </div>
                    </Card>
                  ))}

                  {testCases.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      <FileText className="w-8 h-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No test cases yet</p>
                      <p className="text-xs opacity-75">Add a test case to get started</p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </TabsContent>
          </div>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default NodeTestingPanel;
