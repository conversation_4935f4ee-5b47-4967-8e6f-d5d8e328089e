'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  Save,
  Upload,
  Download,
  Share2,
  Star,
  Copy,
  Edit,
  Trash2,
  Plus,
  Search,
  Filter,
  BookOpen,
  Users,
  TrendingUp,
  Clock,
  Tag,
  FileText,
  Sparkles
} from 'lucide-react';
import { toast } from 'react-hot-toast';

import { Workflow, AgentTemplate } from '@/services/agent-builder/agentBuilderService';
import { templateService, TemplateCategory } from '@/services/templates/templateService';
import { apiClient } from '@/services/api/apiClient';

interface TemplateManagerProps {
  currentWorkflow?: Workflow;
  onLoadTemplate?: (template: AgentTemplate) => void;
  onSaveAsTemplate?: (templateData: Partial<AgentTemplate>) => void;
}

const TemplateManager: React.FC<TemplateManagerProps> = ({
  currentWorkflow,
  onLoadTemplate,
  onSaveAsTemplate
}) => {
  const [templates, setTemplates] = useState<AgentTemplate[]>([]);
  const [categories, setCategories] = useState<TemplateCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [showShareDialog, setShowShareDialog] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<AgentTemplate | null>(null);

  // Save template form state
  const [saveForm, setSaveForm] = useState({
    name: '',
    description: '',
    category: '',
    tags: '',
    isPublic: true
  });

  useEffect(() => {
    loadTemplates();
    loadCategories();
  }, []);

  const loadTemplates = async () => {
    try {
      setLoading(true);
      const data = await templateService.getAll({
        category: selectedCategory !== 'all' ? selectedCategory : undefined,
        search: searchTerm || undefined
      });
      setTemplates(data);
    } catch (error) {
      console.error('Failed to load templates:', error);
      toast.error('Failed to load templates');
    } finally {
      setLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      const data = await templateService.getCategories();
      setCategories(data);
    } catch (error) {
      console.error('Failed to load categories:', error);
    }
  };

  const handleSaveAsTemplate = async () => {
    if (!currentWorkflow) {
      toast.error('No workflow to save');
      return;
    }

    try {
      const templateData: Partial<AgentTemplate> = {
        name: saveForm.name,
        description: saveForm.description,
        category: saveForm.category,
        tags: saveForm.tags.split(',').map(tag => tag.trim()).filter(Boolean),
        workflow: currentWorkflow,
        author: 'Current User', // Would come from auth context
        version: '1.0.0'
      };

      await templateService.create(templateData as AgentTemplate);
      toast.success('Template saved successfully!');
      setShowSaveDialog(false);
      setSaveForm({ name: '', description: '', category: '', tags: '', isPublic: true });
      loadTemplates();
      
      if (onSaveAsTemplate) {
        onSaveAsTemplate(templateData);
      }
    } catch (error) {
      console.error('Failed to save template:', error);
      toast.error('Failed to save template');
    }
  };

  const handleLoadTemplate = async (template: AgentTemplate) => {
    try {
      const workflow = await templateService.useTemplate(template.id);
      toast.success(`Loaded template: ${template.name}`);
      
      if (onLoadTemplate) {
        onLoadTemplate(template);
      }
    } catch (error) {
      console.error('Failed to load template:', error);
      toast.error('Failed to load template');
    }
  };

  const handleExportTemplate = async (template: AgentTemplate) => {
    try {
      const blob = await templateService.export(template.id);
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${template.name.replace(/\s+/g, '_')}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      toast.success('Template exported successfully!');
    } catch (error) {
      console.error('Failed to export template:', error);
      toast.error('Failed to export template');
    }
  };

  const handleImportTemplate = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const template = await templateService.import(file);
      toast.success('Template imported successfully!');
      loadTemplates();
    } catch (error) {
      console.error('Failed to import template:', error);
      toast.error('Failed to import template');
    }
  };

  const handleRateTemplate = async (template: AgentTemplate, rating: number) => {
    try {
      await templateService.rateTemplate(template.id, rating);
      toast.success('Rating submitted!');
      loadTemplates();
    } catch (error) {
      console.error('Failed to rate template:', error);
      toast.error('Failed to submit rating');
    }
  };

  const getComplexityBadge = (template: AgentTemplate) => {
    const nodeCount = template.workflow.nodes.length;
    if (nodeCount <= 2) return <Badge variant="secondary">Simple</Badge>;
    if (nodeCount <= 5) return <Badge variant="outline">Moderate</Badge>;
    return <Badge variant="destructive">Complex</Badge>;
  };

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = !searchTerm || 
      template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Template Manager</h2>
          <p className="text-gray-600">Save, load, and manage agent templates</p>
        </div>
        <div className="flex items-center gap-2">
          <input
            type="file"
            accept=".json"
            onChange={handleImportTemplate}
            className="hidden"
            id="import-template"
          />
          <Button
            variant="outline"
            onClick={() => document.getElementById('import-template')?.click()}
          >
            <Upload className="w-4 h-4 mr-2" />
            Import
          </Button>
          <Button
            onClick={() => setShowSaveDialog(true)}
            disabled={!currentWorkflow}
          >
            <Save className="w-4 h-4 mr-2" />
            Save as Template
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex items-center gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              placeholder="Search templates..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="All Categories" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {categories.map((category) => (
              <SelectItem key={category.id} value={category.id}>
                {category.name} ({category.count})
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {loading ? (
          Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : filteredTemplates.length === 0 ? (
          <div className="col-span-full text-center py-12">
            <BookOpen className="w-12 h-12 mx-auto mb-3 text-gray-400" />
            <p className="text-gray-600">No templates found</p>
            <p className="text-sm text-gray-500">Try adjusting your search or filters</p>
          </div>
        ) : (
          filteredTemplates.map((template) => (
            <Card key={template.id} className="cursor-pointer transition-all duration-200 hover:shadow-lg">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg mb-1">{template.name}</CardTitle>
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="outline" className="text-xs">
                        {template.category.replace('_', ' ')}
                      </Badge>
                      {getComplexityBadge(template)}
                      {template.rating && (
                        <div className="flex items-center gap-1">
                          <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                          <span className="text-xs">{template.rating}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-1">
                    <Users className="w-4 h-4 text-gray-400" />
                    <span className="text-sm text-gray-600">{template.usage_count}</span>
                  </div>
                </div>
              </CardHeader>

              <CardContent>
                <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                  {template.description}
                </p>

                <div className="flex flex-wrap gap-1 mb-3">
                  {template.tags.slice(0, 3).map((tag) => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {template.tags.length > 3 && (
                    <Badge variant="secondary" className="text-xs">
                      +{template.tags.length - 3}
                    </Badge>
                  )}
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      onClick={() => handleLoadTemplate(template)}
                    >
                      Load Template
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleExportTemplate(template)}
                    >
                      <Download className="w-4 h-4" />
                    </Button>
                  </div>
                  
                  <div className="flex items-center gap-1">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star
                        key={star}
                        className={`w-4 h-4 cursor-pointer transition-colors ${
                          template.rating && star <= template.rating
                            ? 'fill-yellow-400 text-yellow-400'
                            : 'text-gray-300 hover:text-yellow-400'
                        }`}
                        onClick={() => handleRateTemplate(template, star)}
                      />
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Save Template Dialog */}
      <Dialog open={showSaveDialog} onOpenChange={setShowSaveDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Save as Template</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="template-name">Template Name</Label>
              <Input
                id="template-name"
                value={saveForm.name}
                onChange={(e) => setSaveForm(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter template name"
              />
            </div>
            <div>
              <Label htmlFor="template-description">Description</Label>
              <Textarea
                id="template-description"
                value={saveForm.description}
                onChange={(e) => setSaveForm(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Describe what this template does"
                rows={3}
              />
            </div>
            <div>
              <Label htmlFor="template-category">Category</Label>
              <Select
                value={saveForm.category}
                onValueChange={(value) => setSaveForm(prev => ({ ...prev, category: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="template-tags">Tags (comma-separated)</Label>
              <Input
                id="template-tags"
                value={saveForm.tags}
                onChange={(e) => setSaveForm(prev => ({ ...prev, tags: e.target.value }))}
                placeholder="e.g., customer-service, basic, intent-analysis"
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowSaveDialog(false)}>
                Cancel
              </Button>
              <Button onClick={handleSaveAsTemplate} disabled={!saveForm.name || !saveForm.description}>
                Save Template
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default TemplateManager;
