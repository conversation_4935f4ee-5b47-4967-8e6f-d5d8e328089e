// frontend/src/components/features/AgentBuilder/NodePropertiesPanel.tsx
'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Edit } from 'lucide-react';

import { NodeConfig } from '@/types/workflow';

interface NodePropertiesPanelProps {
  selectedNode: NodeConfig;
  handleNodeEdit: (node: NodeConfig) => void;
}

const NodePropertiesPanel: React.FC<NodePropertiesPanelProps> = ({
  selectedNode,
  handleNodeEdit,
}) => {
  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b">
        <h3 className="font-medium">Node Properties</h3>
        <p className="text-sm text-gray-600">{selectedNode.name}</p>
      </div>
      <div className="flex-1 relative">
        <ScrollArea className="absolute inset-0 p-4">
          <div className="space-y-4">
            <div>
              <Label>Node ID</Label>
              <Input value={selectedNode.id} disabled />
            </div>
            <div>
              <Label>Type</Label>
              <Badge variant="outline">{selectedNode.type}</Badge>
            </div>
            <div>
              <Label>Description</Label>
              <Textarea value={selectedNode.description || ''} rows={2} disabled />
            </div>
            <Button onClick={() => handleNodeEdit(selectedNode)} className="w-full">
              <Edit className="w-4 h-4 mr-2" />
              Edit Node
            </Button>
          </div>
        </ScrollArea>
      </div>
    </div>
  );
};

export default NodePropertiesPanel;
