// frontend/src/components/features/TestCenter/AgentConfig.tsx
'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import {
  Brain,
  Database,
  Zap,
  Settings,
  Plus,
  Trash2,
  Edit,
  Save,
  RefreshCw,
  Eye,
  Code,
  CheckCircle,
  AlertCircle,
  Clock,
  Activity,
  MessageSquare,
  Mic,
  Phone,
} from 'lucide-react';
import { toast } from 'react-hot-toast';

import { NodeConfig, NodeType, Connections, Workflow as AgentWorkflow } from '@/types/workflow'; // Consolidated imports

interface AgentConfigProps {
  workflow: AgentWorkflow;
  onWorkflowChange: (workflow: AgentWorkflow) => void;
  onViewJson: (json: string) => void;
  onTestAgent: () => void;
  isTestingAgent?: boolean;
}

const NODE_TYPES = [
  {
    id: NodeType.PYTHON_FUNCTION,
    name: 'Python Function',
    icon: Code,
    description: 'Execute custom Python code',
  },
  { id: NodeType.LLM, name: 'LLM Node', icon: Brain, description: 'Large Language Model processing' },
  { id: NodeType.DSPY_MODULE, name: 'DSPy Module', icon: Zap, description: 'DSPy-powered AI module' },
  {
    id: NodeType.MEMORY,
    name: 'Memory Node',
    icon: Database,
    description: 'Memory storage and retrieval',
  },
  { id: NodeType.TOOL, name: 'Tool Node', icon: Settings, description: 'External tool integration' },
  { id: NodeType.RAG, name: 'RAG Node', icon: Database, description: 'Retrieval Augmented Generation' },
];

const DEPENDENCY_OPTIONS = [
  'llm_service',
  'db',
  'embedding_service',
  'vector_db',
  'memory_service',
  'tool_service',
  'web_search',
  'file_service',
];

const AgentConfig: React.FC<AgentConfigProps> = ({
  workflow,
  onWorkflowChange,
  onViewJson,
  onTestAgent,
  isTestingAgent = false,
}) => {
  const [selectedNode, setSelectedNode] = useState<NodeConfig | null>(null);
  const [showAddNodeDialog, setShowAddNodeDialog] = useState(false);
  const [editingNode, setEditingNode] = useState<NodeConfig | null>(null);

  const generateNodeId = () => `node_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  const handleAddNode = (nodeType: string) => {
    const newNode: NodeConfig = {
      id: generateNodeId(),
      name: `New ${NODE_TYPES.find((t) => t.id === nodeType)?.name || 'Node'}`,
      type: nodeType as NodeType, // Use NodeType enum
      dependencies: [],
      parameters: {},
      description: '',
      ui_config: {}, // Ensure ui_config is initialized
    };

    // Set default configurations based on node type
    switch (nodeType) {
      case NodeType.LLM:
        newNode.system_prompt = 'You are a helpful AI assistant.';
        newNode.dependencies = ['llm_service'];
        break;
      case NodeType.DSPY_MODULE:
        newNode.dspy_config = {
          module_type: 'generate',
          signature: 'input -> output',
          examples: [],
        };
        break;
      case NodeType.MEMORY:
        newNode.memory_config = {
          type: 'short_term',
          max_entries: 100,
        };
        newNode.dependencies = ['memory_service'];
        break;
      case NodeType.PYTHON_FUNCTION:
        newNode.functionPath = 'orchestrator.nodes.custom.function_name';
        newNode.dependencies = ['db'];
        break;
    }

    setEditingNode(newNode);
    setShowAddNodeDialog(false);
  };

  const handleSaveNode = () => {
    if (!editingNode) return;

    const updatedNodes = workflow.nodes.some((n) => n.id === editingNode.id)
      ? workflow.nodes.map((n) => (n.id === editingNode.id ? editingNode : n))
      : [...workflow.nodes, editingNode];

    onWorkflowChange({
      ...workflow,
      nodes: updatedNodes,
    });

    setEditingNode(null);
    toast.success('Node saved successfully!');
  };

  const handleDeleteNode = (nodeId: string) => {
    if (!confirm('Are you sure you want to delete this node?')) return;

    const updatedNodes = workflow.nodes.filter((n) => n.id !== nodeId);
    const updatedConnections = { ...workflow.connections };

    // Remove connections to/from this node
    delete updatedConnections[nodeId];
    Object.keys(updatedConnections).forEach((key) => {
      const connection = updatedConnections[key];
      if (connection && 'main' in connection && connection.main) {
        connection.main = connection.main.filter(
          (conn) => conn.node !== nodeId,
        );
      }
      if (connection && 'conditional' in connection && connection.conditional?.paths) {
        Object.keys(connection.conditional.paths).forEach(pathKey => {
          connection.conditional!.paths[pathKey] = connection.conditional!.paths[pathKey].filter(
            (conn) => conn.node !== nodeId,
          );
        });
      }
    });

    onWorkflowChange({
      ...workflow,
      nodes: updatedNodes,
      connections: updatedConnections,
    });

    toast.success('Node deleted successfully!');
  };

  const handleViewJson = () => {
    const workflowJson = JSON.stringify({ workflow }, null, 2);
    onViewJson(workflowJson);
  };

  const getNodeIcon = (type: string) => {
    return NODE_TYPES.find((t) => t.id === type)?.icon || Settings;
  };

  const getNodeTypeColor = (type: string) => {
    const colors = {
      [NodeType.PYTHON_FUNCTION]: 'bg-blue-100 text-blue-800',
      [NodeType.LLM]: 'bg-purple-100 text-purple-800',
      [NodeType.DSPY_MODULE]: 'bg-green-100 text-green-800',
      [NodeType.MEMORY]: 'bg-orange-100 text-orange-800',
      [NodeType.TOOL]: 'bg-gray-100 text-gray-800',
      [NodeType.RAG]: 'bg-indigo-100 text-indigo-800',
    };
    return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Agent Configuration</h2>
          <p className="text-gray-600">Configure your agent's workflow and behavior</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleViewJson}>
            <Code className="w-4 h-4 mr-2" />
            View JSON
          </Button>
          <Button onClick={onTestAgent} disabled={isTestingAgent}>
            {isTestingAgent ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                Testing...
              </>
            ) : (
              <>
                <MessageSquare className="w-4 h-4 mr-2" />
                Test Agent
              </>
            )}
          </Button>
        </div>
      </div>

      <Tabs defaultValue="basic" className="space-y-4">
        <TabsList>
          <TabsTrigger value="basic">Basic Settings</TabsTrigger>
          <TabsTrigger value="nodes">Nodes ({workflow.nodes.length})</TabsTrigger>
          <TabsTrigger value="connections">Connections</TabsTrigger>
          <TabsTrigger value="conversation">Conversation</TabsTrigger>
        </TabsList>

        {/* Basic Settings Tab */}
        <TabsContent value="basic" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Agent Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="agent-name">Agent Name</Label>
                  <Input
                    id="agent-name"
                    value={workflow.name}
                    onChange={(e) => onWorkflowChange({ ...workflow, name: e.target.value })}
                    placeholder="Enter agent name"
                  />
                </div>
                <div>
                  <Label htmlFor="agent-id">Agent ID</Label>
                  <Input
                    id="agent-id"
                    value={workflow.id}
                    onChange={(e) => onWorkflowChange({ ...workflow, id: e.target.value })}
                    placeholder="Enter unique agent ID"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="agent-description">Description</Label>
                <Textarea
                  id="agent-description"
                  value={workflow.description}
                  onChange={(e) => onWorkflowChange({ ...workflow, description: e.target.value })}
                  placeholder="Describe what this agent does"
                  rows={3}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  checked={workflow.active}
                  onCheckedChange={(checked) => onWorkflowChange({ ...workflow, active: checked })}
                />
                <Label>Agent Active</Label>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Nodes Tab */}
        <TabsContent value="nodes" className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">Agent Nodes</h3>
            <Button onClick={() => setShowAddNodeDialog(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Add Node
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {workflow.nodes.map((node) => {
              const NodeIcon = getNodeIcon(node.type);
              return (
                <Card key={node.id} className="transition-all duration-200 hover:shadow-lg">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-2">
                        <NodeIcon className="w-5 h-5 text-blue-500" />
                        <div>
                          <CardTitle className="text-lg">{node.name}</CardTitle>
                          <Badge className={`text-xs mt-1 ${getNodeTypeColor(node.type)}`}>
                            {NODE_TYPES.find((t) => t.id === node.type)?.name || node.type}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  <CardContent>
                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                      {node.description || 'No description provided'}
                    </p>

                    <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                      <span>{node.dependencies.length} dependencies</span>
                      <span>{Object.keys(node.parameters).length} parameters</span>
                    </div>

                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setEditingNode({ ...node })}
                      >
                        <Edit className="w-3 h-3 mr-1" />
                        Edit
                      </Button>
                      <Button size="sm" variant="outline" onClick={() => setSelectedNode(node)}>
                        <Eye className="w-3 h-3 mr-1" />
                        View
                      </Button>
                      <Button size="sm" variant="outline" onClick={() => handleDeleteNode(node.id)}>
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}

            {workflow.nodes.length === 0 && (
              <div className="col-span-full text-center py-12">
                <Brain className="w-12 h-12 mx-auto mb-3 text-gray-400" />
                <p className="text-gray-600">No nodes configured</p>
                <p className="text-sm text-gray-500">Add your first node to get started</p>
              </div>
            )}
          </div>
        </TabsContent>

        {/* Connections Tab */}
        <TabsContent value="connections" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Node Connections</CardTitle>
              <p className="text-sm text-gray-600">Define how nodes connect to each other</p>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="start-node">Start Node</Label>
                  <Select
                    value={workflow.start_node_id}
                    onValueChange={(value) =>
                      onWorkflowChange({ ...workflow, start_node_id: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select start node" />
                    </SelectTrigger>
                    <SelectContent>
                      {workflow.nodes.map((node) => (
                        <SelectItem key={node.id} value={node.id}>
                          {node.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-3">
                  <h4 className="font-medium">Connection Flow</h4>
                  {Object.entries(workflow.connections).map(([nodeId, connection]) => {
                    const node = workflow.nodes.find((n) => n.id === nodeId);
                    return (
                      <div key={nodeId} className="p-3 border rounded-lg">
                        <div className="font-medium mb-2">{node?.name || nodeId}</div>
                        {connection && 'main' in connection && connection.main && (
                          <div className="text-sm text-gray-600">
                            Connects to:{' '}
                            {connection.main
                              .map((conn) => {
                                const targetNode = workflow.nodes.find((n) => n.id === conn.node);
                                return targetNode?.name || conn.node;
                              })
                              .join(', ')}
                          </div>
                        )}
                        {connection && 'conditional' in connection && connection.conditional && (
                          <div className="text-sm text-gray-600">
                            Conditional: {connection.conditional.conditionFunctionPath || connection.conditional.prompt}
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Conversation Tab */}
        <TabsContent value="conversation" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Conversation Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="first-message">First Message</Label>
                <Textarea
                  id="first-message"
                  value={workflow.first_message || ''}
                  onChange={(e) =>
                    onWorkflowChange({
                      ...workflow,
                      first_message: e.target.value,
                    })
                  }
                  placeholder="Enter the agent's greeting message"
                  rows={2}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="silence-timeout">Silence Timeout (seconds)</Label>
                  <Input
                    id="silence-timeout"
                    type="number"
                    value={workflow.conversation_settings?.silence_timeout_seconds || 30}
                    onChange={(e) =>
                      onWorkflowChange({
                        ...workflow,
                        conversation_settings: {
                          ...workflow.conversation_settings,
                          silence_timeout_seconds: parseInt(e.target.value) || 30,
                        },
                      })
                    }
                  />
                </div>
                <div>
                  <Label htmlFor="max-turns">Max Conversation Turns</Label>
                  <Input
                    id="max-turns"
                    type="number"
                    value={workflow.conversation_settings?.max_turns || 50}
                    onChange={(e) =>
                      onWorkflowChange({
                        ...workflow,
                        conversation_settings: {
                          ...workflow.conversation_settings,
                          max_turns: parseInt(e.target.value) || 50,
                        },
                      })
                    }
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  checked={workflow.conversation_settings?.enable_interruption || false}
                  onCheckedChange={(checked) =>
                    onWorkflowChange({
                      ...workflow,
                      conversation_settings: {
                        ...workflow.conversation_settings,
                        enable_interruption: checked,
                      },
                    })
                  }
                />
                <Label>Enable Interruption</Label>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Add Node Dialog */}
      {showAddNodeDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-2xl">
            <CardHeader>
              <CardTitle>Add New Node</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {NODE_TYPES.map((nodeType) => {
                  const Icon = nodeType.icon;
                  return (
                    <Button
                      key={nodeType.id}
                      variant="outline"
                      className="justify-start h-auto p-4"
                      onClick={() => handleAddNode(nodeType.id)}
                    >
                      <Icon className="w-6 h-6 mr-3" />
                      <div className="text-left">
                        <div className="font-medium">{nodeType.name}</div>
                        <div className="text-sm text-gray-500">{nodeType.description}</div>
                      </div>
                    </Button>
                  );
                })}
              </div>
              <div className="flex justify-end mt-4">
                <Button variant="outline" onClick={() => setShowAddNodeDialog(false)}>
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Edit Node Dialog */}
      {editingNode && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-4xl max-h-[80vh] overflow-y-auto">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {React.createElement(getNodeIcon(editingNode.type), { className: 'w-5 h-5' })}
                {workflow.nodes.some((n) => n.id === editingNode.id) ? 'Edit Node' : 'Create Node'}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Basic Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="node-name">Node Name</Label>
                  <Input
                    id="node-name"
                    value={editingNode.name}
                    onChange={(e) => setEditingNode({ ...editingNode, name: e.target.value })}
                    placeholder="Enter node name"
                  />
                </div>
                <div>
                  <Label htmlFor="node-type">Node Type</Label>
                  <Select
                    value={editingNode.type}
                    onValueChange={(value) =>
                      setEditingNode({ ...editingNode, type: value as NodeType })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {NODE_TYPES.map((type) => (
                        <SelectItem key={type.id} value={type.id}>
                          {type.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="node-description">Description</Label>
                <Textarea
                  id="node-description"
                  value={editingNode.description || ''}
                  onChange={(e) => setEditingNode({ ...editingNode, description: e.target.value })}
                  placeholder="Describe what this node does"
                  rows={2}
                />
              </div>

              {/* Function Path for Python Functions */}
              {editingNode.type === NodeType.PYTHON_FUNCTION && (
                <div>
                  <Label htmlFor="function-path">Function Path</Label>
                  <Input
                    id="function-path"
                    value={editingNode.functionPath || ''}
                    onChange={(e) =>
                      setEditingNode({ ...editingNode, functionPath: e.target.value })
                    }
                    placeholder="orchestrator.nodes.custom.function_name"
                  />
                </div>
              )}

              {/* System Prompt for LLM nodes */}
              {editingNode.type === NodeType.LLM && (
                <div>
                  <Label htmlFor="system-prompt">System Prompt</Label>
                  <Textarea
                    id="system-prompt"
                    value={editingNode.system_prompt || ''}
                    onChange={(e) =>
                      setEditingNode({ ...editingNode, system_prompt: e.target.value })
                    }
                    placeholder="You are a helpful AI assistant..."
                    rows={4}
                  />
                </div>
              )}

              {/* DSPy Configuration */}
              {editingNode.type === NodeType.DSPY_MODULE && (
                <div className="space-y-4">
                  <h4 className="font-medium">DSPy Configuration</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="dspy-module-type">Module Type</Label>
                      <Select
                        value={editingNode.dspy_config?.module_type || 'generate'}
                        onValueChange={(value) =>
                          setEditingNode({
                            ...editingNode,
                            dspy_config: { ...editingNode.dspy_config, module_type: value },
                          })
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="generate">Generate</SelectItem>
                          <SelectItem value="classify">Classify</SelectItem>
                          <SelectItem value="chain_of_thought">Chain of Thought</SelectItem>
                          <SelectItem value="react">ReAct</SelectItem>
                          <SelectItem value="retrieve">Retrieve</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="dspy-signature">Signature</Label>
                      <Input
                        id="dspy-signature"
                        value={editingNode.dspy_config?.signature || ''}
                        onChange={(e) =>
                          setEditingNode({
                            ...editingNode,
                            dspy_config: { ...editingNode.dspy_config, signature: e.target.value },
                          })
                        }
                        placeholder="input -> output"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Memory Configuration */}
              {editingNode.type === NodeType.MEMORY && (
                <div className="space-y-4">
                  <h4 className="font-medium">Memory Configuration</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="memory-type">Memory Type</Label>
                      <Select
                        value={editingNode.memory_config?.type || 'short_term'}
                        onValueChange={(value) =>
                          setEditingNode({
                            ...editingNode,
                            memory_config: { ...editingNode.memory_config, type: value },
                          })
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="short_term">Short Term</SelectItem>
                          <SelectItem value="long_term">Long Term</SelectItem>
                          <SelectItem value="episodic">Episodic</SelectItem>
                          <SelectItem value="semantic">Semantic</SelectItem>
                          <SelectItem value="working">Working</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="memory-max-entries">Max Entries</Label>
                      <Input
                        id="memory-max-entries"
                        type="number"
                        value={editingNode.memory_config?.max_entries || 100}
                        onChange={(e) =>
                          setEditingNode({
                            ...editingNode,
                            memory_config: {
                              ...editingNode.memory_config,
                              max_entries: parseInt(e.target.value) || 100,
                            },
                          })
                        }
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* N8N Configuration */}
              {editingNode.type === 'n8n' && (
                <div className="space-y-4">
                  <h4 className="font-medium">N8N Configuration</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="n8n-workflow-id">Workflow ID</Label>
                      <Input
                        id="n8n-workflow-id"
                        value={editingNode.n8n_config?.workflow_id || ''}
                        onChange={(e) => setEditingNode({
                          ...editingNode,
                          n8n_config: { ...editingNode.n8n_config, workflow_id: e.target.value }
                        })}
                        placeholder="workflow-123"
                      />
                    </div>
                    <div>
                      <Label htmlFor="n8n-webhook-url">Webhook URL</Label>
                      <Input
                        id="n8n-webhook-url"
                        value={editingNode.n8n_config?.webhook_url || ''}
                        onChange={(e) => setEditingNode({
                          ...editingNode,
                          n8n_config: { ...editingNode.n8n_config, webhook_url: e.target.value }
                        })}
                        placeholder="http://localhost:5678/webhook/..."
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="n8n-auth-token">Auth Token</Label>
                    <Input
                      id="n8n-auth-token"
                      type="password"
                      value={editingNode.n8n_config?.auth_token || ''}
                      onChange={(e) => setEditingNode({
                        ...editingNode,
                        n8n_config: { ...editingNode.n8n_config, auth_token: e.target.value }
                      })}
                      placeholder="n8n auth token"
                    />
                  </div>
                </div>
              )}

              {/* Dependencies */}
              <div className="space-y-4">
                <h4 className="font-medium">Dependencies</h4>
                <div className="flex flex-wrap gap-2">
                  {DEPENDENCY_OPTIONS.map((dep) => (
                    <Button
                      key={dep}
                      size="sm"
                      variant={editingNode.dependencies.includes(dep) ? 'default' : 'outline'}
                      onClick={() => {
                        const deps = editingNode.dependencies.includes(dep)
                          ? editingNode.dependencies.filter((d) => d !== dep)
                          : [...editingNode.dependencies, dep];
                        setEditingNode({ ...editingNode, dependencies: deps });
                      }}
                    >
                      {dep}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Actions */}
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setEditingNode(null)}>
                  Cancel
                </Button>
                <Button onClick={handleSaveNode} disabled={!editingNode.name}>
                  Save Node
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Node Details Dialog */}
      {selectedNode && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-2xl max-h-[80vh] overflow-y-auto">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {React.createElement(getNodeIcon(selectedNode.type), { className: 'w-5 h-5' })}
                {selectedNode.name}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="max-h-96">
                <div className="space-y-4">
                  <div>
                    <Label>Type</Label>
                    <Badge className={getNodeTypeColor(selectedNode.type)}>
                      {NODE_TYPES.find((t) => t.id === selectedNode.type)?.name ||
                        selectedNode.type}
                    </Badge>
                  </div>

                  <div>
                    <Label>Description</Label>
                    <p className="text-sm text-gray-600">
                      {selectedNode.description || 'No description'}
                    </p>
                  </div>

                  <div>
                    <Label>Dependencies ({selectedNode.dependencies.length})</Label>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {selectedNode.dependencies.map((dep) => (
                        <Badge key={dep} variant="secondary" className="text-xs">
                          {dep}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {selectedNode.system_prompt && (
                    <div>
                      <Label>System Prompt</Label>
                      <p className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                        {selectedNode.system_prompt}
                      </p>
                    </div>
                  )}

                  {selectedNode.functionPath && (
                    <div>
                      <Label>Function Path</Label>
                      <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                        {selectedNode.functionPath}
                      </code>
                    </div>
                  )}
                </div>
              </ScrollArea>
              <div className="flex justify-end mt-4">
                <Button onClick={() => setSelectedNode(null)}>Close</Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default AgentConfig;
