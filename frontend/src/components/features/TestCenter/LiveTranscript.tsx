'use client';

import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, User, Co<PERSON>, Send, Wrench } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ConversationLogEntry } from '@/types';

const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '';
  try {
    return format(new Date(dateStr), 'PPpp');
  } catch (error) {
    return dateStr;
  }
};

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (error) {
    console.error('Failed to copy:', error);
    return false;
  }
};

export default function LiveTranscript({
  conversationLog,
  isActive,
  isTextChatActive,
  onSendMessage,
  onViewToolJson,
}: {
  conversationLog: ConversationLogEntry[];
  isActive: boolean;
  isTextChatActive: boolean;
  onSendMessage: (message: string) => void;
  onViewToolJson: (jsonString: string) => void;
}) {
  const transcriptEndRef = useRef<HTMLDivElement>(null);
  const [userInput, setUserInput] = useState('');

  useEffect(() => {
    transcriptEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [conversationLog]);

  const handleCopyTranscript = async () => {
    const transcriptText = conversationLog
      .map((entry: ConversationLogEntry) => {
        if (entry.event_type === 'tool_call') {
          return `[${formatDateTime(entry.timestamp)}] Tool Call: ${entry.data.name} with args ${JSON.stringify(entry.data.args)}`;
        }
        return `[${formatDateTime(entry.timestamp)}] ${entry.source || ''}: ${entry.data.content || ''}`;
      })
      .join('\n');

    if (await copyToClipboard(transcriptText)) {
      toast.success('Transcript copied to clipboard');
    } else {
      toast.error('Failed to copy transcript');
    }
  };

  const handleSendMessage = () => {
    if (!userInput.trim()) return;
    onSendMessage(userInput);
    setUserInput('');
  };

  const renderEntry = (entry: ConversationLogEntry, index: number) => {
    if (entry.event_type === 'tool_call') {
      return (
        <div key={index} className="flex items-start space-x-3">
          <div className="flex-shrink-0 h-8 w-8 rounded-full flex items-center justify-center bg-yellow-100 text-yellow-600">
            <Wrench className="h-4 w-4" />
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-1">
              <span className="text-sm font-medium text-yellow-700">Tool Call</span>
              <span className="text-xs text-muted-foreground">{formatDateTime(entry.timestamp)}</span>
            </div>
            <div className="text-sm p-3 rounded-lg bg-yellow-50 border border-yellow-200">
              <p className="font-medium text-gray-800">
                <code className="font-mono bg-yellow-200 text-yellow-800 px-1 rounded">
                  {entry.data.name}
                </code>
              </p>
              <Button
                variant="outline"
                size="sm"
                className="mt-2"
                onClick={() => onViewToolJson(entry.data.args)}
              >
                View JSON
              </Button>
            </div>
          </div>
        </div>
      );
    }

    const speaker = entry.event_type === 'user_message' ? 'user' : 'agent';
    const text = entry.data.content;
    const timestamp = entry.timestamp;

    return (
      <div key={index} className="flex items-start space-x-3">
        <div
          className={`flex-shrink-0 h-8 w-8 rounded-full flex items-center justify-center ${speaker === 'agent' ? 'bg-primary/10 text-primary' : 'bg-accent'}`}
        >
          {speaker === 'agent' ? <Bot className="h-4 w-4" /> : <User className="h-4 w-4" />}
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-1">
            <span className={`text-sm font-medium ${speaker === 'agent' ? 'text-primary' : ''}`}
            >
              {speaker === 'agent' ? 'AI Agent' : 'Customer'}
            </span>
            <span className="text-xs text-muted-foreground">{formatDateTime(timestamp)}</span>
          </div>
          <div className={`text-sm p-3 rounded-lg ${speaker === 'agent' ? 'bg-primary/10' : 'bg-accent'}`}>
            {text}
          </div>
        </div>
      </div>
    );
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <MessageCircle className="h-5 w-5 text-gray-400" />
            <CardTitle>Live Transcript</CardTitle>
            {isActive && (
              <div className="flex items-center space-x-1">
                <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm text-green-600">Live</span>
              </div>
            )}
          </div>
          {conversationLog.length > 0 && (
            <Button onClick={handleCopyTranscript} variant="ghost" size="sm" title="Copy transcript">
              <Copy className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-96 overflow-y-auto p-4 space-y-4">
          {conversationLog.length > 0 ? (
            conversationLog.map(renderEntry)
          ) : (
            <div className="flex flex-col items-center justify-center h-full text-center">
              <MessageCircle className="h-12 w-12 text-gray-400 mb-4" />
              <h4 className="text-sm font-medium mb-2">
                {isActive ? 'Waiting for conversation...' : 'No transcript available'}
              </h4>
              <p className="text-sm text-muted-foreground">
                {isActive
                  ? 'The conversation will appear here in real-time'
                  : 'Start a call or chat to see the transcript'}
              </p>
            </div>
          )}
          <div ref={transcriptEndRef} />
        </div>
        {isTextChatActive && (
          <div className="p-4 border-t">
            <div className="flex items-center space-x-2">
              <Input
                value={userInput}
                onChange={(e) => setUserInput(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                placeholder="Type your message..."
              />
              <Button onClick={handleSendMessage} disabled={!userInput.trim()}>
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
