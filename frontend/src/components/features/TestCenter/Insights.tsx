'use client';

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Lightbulb, Zap } from 'lucide-react';
import { Insight, Action } from '@/types';

export default function Insights({
  insights,
  actions,
  isActive,
}: {
  insights: Insight[];
  actions: Action[];
  isActive: boolean;
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Lightbulb className="mr-2" /> Insights & Actions
          {isActive && (
            <div className="flex items-center space-x-1 ml-2">
              <div className="h-2 w-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span className="text-sm text-blue-600">Analyzing...</span>
            </div>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="h-48 overflow-y-auto">
        {isActive || (insights && insights.length > 0) || (actions && actions.length > 0) ? (
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium text-gray-500 mb-2">Real-time Insights</h4>
              {insights && insights.length > 0 ? (
                <ul className="space-y-2">
                  {insights.map((insight: Insight, index: number) => (
                    <li key={index} className="flex items-start">
                      <Lightbulb className="h-4 w-4 text-yellow-500 mr-2 mt-1 flex-shrink-0" />
                      <span className="text-sm text-gray-800">{insight.content}</span>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-sm text-gray-400">No insights generated yet.</p>
              )}
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-500 mb-2">Suggested Actions</h4>
              {actions && actions.length > 0 ? (
                <ul className="space-y-2">
                  {actions.map((action: Action, index: number) => (
                    <li key={index} className="flex items-start">
                      <Zap className="h-4 w-4 text-blue-500 mr-2 mt-1 flex-shrink-0" />
                      <span className="text-sm text-gray-800">{action.content}</span>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-sm text-gray-400">No actions suggested yet.</p>
              )}
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <Lightbulb className="h-12 w-12 text-gray-400 mb-4" />
            <h4 className="text-sm font-medium text-gray-900 mb-2">Insights will appear here</h4>
            <p className="text-sm text-gray-500">
              Start a call to get real-time analysis and suggested actions.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
