'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { User } from 'lucide-react';
import { Customer, Order } from '@/types';

export default function CustomerDetails({
  customer,
  onOrderClick,
}: {
  customer: Customer;
  onOrderClick: (order: Order) => void;
}) {
  if (!customer) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Customer Details</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">Select a customer to see their details.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <User className="mr-2" /> Customer Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p>
            <strong>Name:</strong> {customer.name}
          </p>
          <p>
            <strong>Phone:</strong> {customer.phone}
          </p>
          <p>
            <strong>Email:</strong> {customer.email}
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
