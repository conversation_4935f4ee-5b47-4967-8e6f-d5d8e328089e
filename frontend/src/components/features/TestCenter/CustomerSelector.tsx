'use client';

import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { customerAPI } from '@/lib/api';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ChevronsUpDown, Check } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Customer } from '@/types';

export default function CustomerSelector({
  selectedCustomer,
  onCustomerSelect,
}: {
  selectedCustomer: Customer | undefined;
  onCustomerSelect: (customer: Customer) => void;
}) {
  const [open, setOpen] = useState(false);

  const { data: customers, isLoading } = useQuery({
    queryKey: ['customers'],
    queryFn: () => customerAPI.getAll().then((res) => res.data),
  });

  const handleSelect = (customer: Customer) => {
    onCustomerSelect(customer);
    setOpen(false);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer Selection</CardTitle>
      </CardHeader>
      <CardContent>
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              aria-expanded={open}
              className="w-full justify-between"
            >
              {selectedCustomer
                ? `${selectedCustomer.name} (${selectedCustomer.phone})`
                : 'Select a customer...'}
              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
            <Command>
              <CommandInput placeholder="Search customer..." />
              <CommandList>
                <CommandEmpty>{isLoading ? 'Loading...' : 'No customer found.'}</CommandEmpty>
                <CommandGroup>
                  {customers?.map((customer: Customer) => (
                    <CommandItem
                      key={customer.id}
                      value={`${customer.name} ${customer.email} ${customer.phone}`}
                      onSelect={() => handleSelect(customer)}
                    >
                      <Check
                        className={cn(
                          'mr-2 h-4 w-4',
                          selectedCustomer?.id === customer.id ? 'opacity-100' : 'opacity-0',
                        )}
                      />
                      <div>
                        <p className="font-medium">{customer.name}</p>
                        <p className="text-sm text-muted-foreground">{customer.phone}</p>
                      </div>
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      </CardContent>
    </Card>
  );
}
