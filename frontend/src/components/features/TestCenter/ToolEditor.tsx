import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Save } from 'lucide-react';

interface ToolEditorProps {
  toolConfig: string;
  setToolConfig: (config: string) => void;
  isEditing: boolean;
}

const exampleToolConfig = JSON.stringify(
  [
    {
      "name": "get_weather",
      "description": "Get the current weather in a given location",
      "parameters": {
        "type": "object",
        "properties": {
          "location": {
            "type": "string",
            "description": "The city and state, e.g. San Francisco, CA"
          }
        },
        "required": ["location"]
      }
    }
  ],
  null,
  2
);

export default function ToolEditor({ toolConfig, setToolConfig, isEditing }: ToolEditorProps) {
  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>Tool Configuration (Optional)</CardTitle>
        </div>
        <p className="text-sm text-gray-500">
          Define functions the agent can call, using a JSON array format.
        </p>
      </CardHeader>
      <CardContent>
        <Textarea
          value={toolConfig}
          onChange={(e) => setToolConfig(e.target.value)}
          rows={12}
          placeholder={exampleToolConfig}
          disabled={!isEditing}
        />
      </CardContent>
    </Card>
  );
}
