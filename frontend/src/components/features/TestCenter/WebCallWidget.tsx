'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { toast } from 'react-hot-toast';
import { ServerMessage } from '@/types';

interface WebCallWidgetProps {
  callHistoryId: string;
  onConnect: () => void;
  onClose: () => void;
  onTranscriptUpdate: (message: ServerMessage) => void;
  customPrompt: string;
  customFirstMessage: string;
}

const WebCallWidget = ({
  callHistoryId,
  onConnect,
  onClose,
  onTranscriptUpdate,
  customPrompt,
  customFirstMessage,
}: WebCallWidgetProps) => {
  const [audioQueue, setAudioQueue] = useState<Blob[]>([]);
  const [isPlaying, setIsPlaying] = useState(false);
  const utteranceEndTimerRef = useRef<NodeJS.Timeout | undefined>(undefined);
  const audioBufferRef = useRef<Uint8Array[]>([]);

  const audioRef = useRef<HTMLAudioElement>(null);
  const wsRef = useRef<WebSocket | undefined>(undefined);
  const audioContextRef = useRef<AudioContext | undefined>(undefined);
  const audioNodeRef = useRef<AudioWorkletNode | undefined>(undefined);
  const streamRef = useRef<MediaStream | undefined>(undefined);

  const createWavHeader = useCallback(
    (dataLength: number, sampleRate: number, numChannels: number, bitsPerSample: number) => {
      const buffer = new ArrayBuffer(44);
      const view = new DataView(buffer);
      const writeString = (offset: number, string: string) => {
        for (let i = 0; i < string.length; i++) {
          view.setUint8(offset + i, string.charCodeAt(i));
        }
      };
      const blockAlign = numChannels * (bitsPerSample / 8);
      const byteRate = sampleRate * blockAlign;
      writeString(0, 'RIFF');
      view.setUint32(4, 36 + dataLength, true);
      writeString(8, 'WAVE');
      writeString(12, 'fmt ');
      view.setUint32(16, 16, true);
      view.setUint16(20, 1, true);
      view.setUint16(22, numChannels, true);
      view.setUint32(24, sampleRate, true);
      view.setUint32(28, byteRate, true);
      view.setUint16(32, blockAlign, true);
      view.setUint16(34, bitsPerSample, true);
      writeString(36, 'data');
      view.setUint32(40, dataLength, true);
      return buffer;
    },
    [],
  );

  const playBufferedAudio = useCallback(() => {
    if (audioBufferRef.current.length === 0) return;

    const totalLength = audioBufferRef.current.reduce((acc, chunk) => acc + chunk.length, 0);
    const concatenated = new Uint8Array(totalLength);
    let offset = 0;
    for (const chunk of audioBufferRef.current) {
      concatenated.set(chunk, offset);
      offset += chunk.length;
    }

    const wavHeader = new Uint8Array(createWavHeader(concatenated.length, 24000, 1, 16));
    const wavBlob = new Blob([wavHeader, concatenated], { type: 'audio/wav' });
    setAudioQueue((prev) => [...prev, wavBlob]);

    audioBufferRef.current = [];
  }, [createWavHeader]);

  const handleServerMessage = useCallback(
    (event: MessageEvent) => {
      try {
        const message: ServerMessage = JSON.parse(event.data);
        onTranscriptUpdate(message);

        if (message.type === 'audio' && message.audio) {
          if (utteranceEndTimerRef.current) clearTimeout(utteranceEndTimerRef.current);
          const pcmData = Uint8Array.from(atob(message.audio), (c) => c.charCodeAt(0));
          audioBufferRef.current.push(pcmData);
          utteranceEndTimerRef.current = setTimeout(playBufferedAudio, 300);
        }
      } catch (error) {
        console.error('Error handling server message:', error);
      }
    },
    [onTranscriptUpdate, playBufferedAudio],
  );

  const startCall = useCallback(async () => {
    const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const apiUrl = new URL(process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000');
    const wsHost = apiUrl.host;
    const wsUrl = `${wsProtocol}//${wsHost}/api/call/ws/web-call`;

    wsRef.current = new WebSocket(wsUrl);

    wsRef.current.onopen = async () => {
      onConnect();
      const initMessage = {
        data: {
          call_history_id: callHistoryId,
          custom_prompt: customPrompt,
          custom_first_message: customFirstMessage,
        },
      };
      wsRef.current?.send(JSON.stringify(initMessage));

      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: { sampleRate: 16000, channelCount: 1 },
        });
        streamRef.current = stream;
        audioContextRef.current = new AudioContext({
          sampleRate: 16000,
        });
        await audioContextRef.current.audioWorklet.addModule('/recorderProcessor.js');
        const source = audioContextRef.current.createMediaStreamSource(stream);
        audioNodeRef.current = new AudioWorkletNode(audioContextRef.current, 'recorder-processor');
        audioNodeRef.current.port.onmessage = (event) => {
          if (wsRef.current?.readyState === WebSocket.OPEN) {
            wsRef.current.send(event.data);
          }
        };
        source.connect(audioNodeRef.current);
        audioNodeRef.current.connect(audioContextRef.current.destination);
      } catch (err) {
        toast.error('Could not start microphone. Please check permissions.');
        onClose();
      }
    };

    wsRef.current.onmessage = handleServerMessage;
    wsRef.current.onerror = (error) => {
      console.error('WebSocket Error:', error);
      toast.error('Web call connection error.');
      onClose();
    };
    wsRef.current.onclose = () => onClose();
  }, [callHistoryId, customPrompt, customFirstMessage, onConnect, onClose, handleServerMessage]);

  useEffect(() => {
    if (audioQueue.length > 0 && !isPlaying && audioRef.current) {
      const nextAudioBlob = audioQueue[0];
      const url = URL.createObjectURL(nextAudioBlob);
      audioRef.current.src = url;
      audioRef.current
        .play()
        .then(() => setIsPlaying(true))
        .catch((e) => console.error('Audio play failed:', e));
      audioRef.current.onended = () => {
        URL.revokeObjectURL(url);
        setIsPlaying(false);
        setAudioQueue((prev) => prev.slice(1));
      };
    }
  }, [audioQueue, isPlaying]);

  useEffect(() => {
    startCall();
    return () => {
      wsRef.current?.close();
      streamRef.current?.getTracks().forEach((track) => track.stop());
      audioContextRef.current?.close();
      if (utteranceEndTimerRef.current) clearTimeout(utteranceEndTimerRef.current);
    };
  }, [startCall]);

  return <audio ref={audioRef} className="hidden" />;
};

export default WebCallWidget;
