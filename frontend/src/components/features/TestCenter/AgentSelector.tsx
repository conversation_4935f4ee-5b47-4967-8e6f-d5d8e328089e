'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { 
  Bot, 
  Settings, 
  RefreshCw, 
  Search,
  Filter,
  ChevronDown,
  ChevronUp,
  Mic,
  MessageSquare,
  Phone
} from 'lucide-react';
import { agentService, Agent } from '@/services/agents/agentService';
import { toast } from 'react-hot-toast';

interface AgentSelectorProps {
  selectedAgent: Agent | null;
  onAgentSelect: (agent: Agent | null) => void;
  onCustomConfigChange: (config: {
    customPrompt: string;
    customFirstMessage: string;
    useCustomConfig: boolean;
  }) => void;
  customConfig: {
    customPrompt: string;
    customFirstMessage: string;
    useCustomConfig: boolean;
  };
}

export default function AgentSelector({
  selectedAgent,
  onAgentSelect,
  onCustomConfigChange,
  customConfig,
}: AgentSelectorProps) {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showActiveOnly, setShowActiveOnly] = useState(true);
  const [showAdvanced, setShowAdvanced] = useState(false);

  useEffect(() => {
    loadAgents();
  }, []);

  const loadAgents = async () => {
    try {
      setLoading(true);
      const agentsData = await agentService.getAll();
      setAgents(agentsData);
    } catch (error) {
      console.error('Failed to load agents:', error);
      toast.error('Failed to load agents');
    } finally {
      setLoading(false);
    }
  };

  const filteredAgents = agents.filter(agent => {
    const matchesSearch = agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (agent.description || '').toLowerCase().includes(searchTerm.toLowerCase());
    const matchesActiveFilter = !showActiveOnly || agent.is_active;
    return matchesSearch && matchesActiveFilter;
  });

  const handleAgentChange = (agentId: string) => {
    if (agentId === 'none') {
      onAgentSelect(null);
      return;
    }
    
    const agent = agents.find(a => a.id.toString() === agentId);
    if (agent) {
      onAgentSelect(agent);
      // Auto-populate custom config with agent's settings
      onCustomConfigChange({
        customPrompt: agent.system_prompt || '',
        customFirstMessage: agent.first_message || '',
        useCustomConfig: false,
      });
    }
  };

  const getSupportedPlatforms = (agent: Agent) => {
    const platforms = [];
    platforms.push('text'); // All agents support text
    
    if (agent.speech_config?.stt_provider) {
      platforms.push('voice');
    }
    if (agent.speech_config?.tts_provider) {
      platforms.push('web');
    }
    
    return platforms;
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'text':
        return <MessageSquare className="w-3 h-3" />;
      case 'voice':
        return <Phone className="w-3 h-3" />;
      case 'web':
        return <Mic className="w-3 h-3" />;
      default:
        return null;
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Bot className="w-5 h-5" />
              Agent Selection
            </CardTitle>
            <CardDescription>
              Choose an agent to test or use custom configuration
            </CardDescription>
          </div>
          <Button variant="outline" size="sm" onClick={loadAgents} disabled={loading}>
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search and Filters */}
        <div className="space-y-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search agents..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Switch
                id="active-only"
                checked={showActiveOnly}
                onCheckedChange={setShowActiveOnly}
              />
              <Label htmlFor="active-only" className="text-sm">
                Active agents only
              </Label>
            </div>
            <Badge variant="secondary">
              {filteredAgents.length} agent{filteredAgents.length !== 1 ? 's' : ''}
            </Badge>
          </div>
        </div>

        {/* Agent Selection */}
        <div className="space-y-2">
          <Label>Select Agent</Label>
          <Select
            value={selectedAgent?.id.toString() || 'none'}
            onValueChange={handleAgentChange}
          >
            <SelectTrigger>
              <SelectValue placeholder="Choose an agent or use custom config" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="none">Custom Configuration</SelectItem>
              <Separator />
              {filteredAgents.map((agent) => (
                <SelectItem key={agent.id} value={agent.id.toString()}>
                  <div className="flex items-center justify-between w-full">
                    <span>{agent.name}</span>
                    <div className="flex items-center gap-1 ml-2">
                      {getSupportedPlatforms(agent).map((platform) => (
                        <Badge key={platform} variant="outline" className="text-xs px-1 py-0">
                          {getPlatformIcon(platform)}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Selected Agent Info */}
        {selectedAgent && (
          <Card className="bg-blue-50 border-blue-200">
            <CardContent className="pt-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <h4 className="font-semibold">{selectedAgent.name}</h4>
                  <Badge className={selectedAgent.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                    {selectedAgent.is_active ? 'Active' : 'Inactive'}
                  </Badge>
                </div>
                {selectedAgent.description && (
                  <p className="text-sm text-gray-600">{selectedAgent.description}</p>
                )}
                <div className="flex items-center gap-2">
                  <span className="text-xs text-gray-500">Supports:</span>
                  {getSupportedPlatforms(selectedAgent).map((platform) => (
                    <Badge key={platform} variant="outline" className="text-xs">
                      {getPlatformIcon(platform)}
                      <span className="ml-1 capitalize">{platform}</span>
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Custom Configuration Toggle */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label>Custom Configuration</Label>
            <div className="flex items-center space-x-2">
              <Switch
                id="use-custom"
                checked={customConfig.useCustomConfig}
                onCheckedChange={(checked) => 
                  onCustomConfigChange({ ...customConfig, useCustomConfig: checked })
                }
              />
              <Label htmlFor="use-custom" className="text-sm">
                Override agent settings
              </Label>
            </div>
          </div>

          {/* Advanced Settings Toggle */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="w-full justify-between"
          >
            <span>Advanced Settings</span>
            {showAdvanced ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
          </Button>

          {/* Custom Configuration Fields */}
          {(customConfig.useCustomConfig || showAdvanced) && (
            <div className="space-y-3 pt-2 border-t">
              <div className="space-y-2">
                <Label htmlFor="custom-prompt">Custom System Prompt</Label>
                <Textarea
                  id="custom-prompt"
                  value={customConfig.customPrompt}
                  onChange={(e) => 
                    onCustomConfigChange({ ...customConfig, customPrompt: e.target.value })
                  }
                  placeholder="Enter custom system prompt..."
                  rows={4}
                  className="text-sm"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="custom-first-message">Custom First Message</Label>
                <Textarea
                  id="custom-first-message"
                  value={customConfig.customFirstMessage}
                  onChange={(e) => 
                    onCustomConfigChange({ ...customConfig, customFirstMessage: e.target.value })
                  }
                  placeholder="Enter custom first message..."
                  rows={2}
                  className="text-sm"
                />
              </div>
            </div>
          )}
        </div>

        {/* Quick Actions */}
        {selectedAgent && (
          <div className="flex gap-2 pt-2 border-t">
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open(`/agents/${selectedAgent.id}`, '_blank')}
            >
              <Settings className="w-4 h-4 mr-2" />
              Configure
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
