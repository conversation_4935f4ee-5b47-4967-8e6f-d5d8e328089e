'use client';

import React, { useState, useEffect } from 'react';
import { performanceAPI } from '@/lib/api';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Line<PERSON>hart,
  Line,
  XAxis,
  <PERSON>Axis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { format } from 'date-fns';
import { PerformanceMetric } from '@/types';

const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '';
  return format(new Date(dateStr), 'HH:mm:ss');
};

const PALETTE = [
  '#6366F1',
  '#10B981',
  '#F59E0B',
  '#EF4444',
  '#3B82F6',
  '#8B5CF6',
  '#EC4899',
  '#6B7280',
  '#F97316',
  '#06B6D4',
];

const generateColor = (str: string, index: number) => {
  return PALETTE[index % PALETTE.length];
};

export default function LivePerformanceChart({
  callHistoryId,
  startTime,
}: {
  callHistoryId: number;
  startTime: string;
}) {
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([]);

  // useEffect(() => {
  //   if (!callHistoryId || !startTime) return;

  //   const fetchMetrics = async () => {
  //     try {
  //       const response = await performanceAPI.getLivePerformance();
  //       // Assuming the backend doesn't filter by callHistoryId, we do it here.
  //       const filtered = response.data.filter(
  //         (m: PerformanceMetric) => m.call_history_id == callHistoryId,
  //       );
  //       setMetrics(filtered);
  //     } catch (err) {
  //       console.error('Failed to fetch performance metrics', err);
  //     }
  //   };

  //   fetchMetrics();
  //   const interval = setInterval(fetchMetrics, 60000);
  //   return () => clearInterval(interval);
  // }, [callHistoryId, startTime]);

  const dataMap = new Map();
  metrics.forEach((metric) => {
    const timestamp = formatDateTime(metric.timestamp);
    if (!dataMap.has(timestamp)) {
      dataMap.set(timestamp, { timestamp });
    }
    dataMap.get(timestamp)[`${metric.service}:${metric.action}`] = metric.latency;
  });

  const formattedData = Array.from(dataMap.values());
  const eventNames = [
    ...new Set(metrics.map((m: PerformanceMetric) => `${m.service}:${m.action}`)),
  ] as string[];

  return (
    <Card>
      <CardHeader>
        <CardTitle>Live Performance Metrics</CardTitle>
      </CardHeader>
      <CardContent>
        {metrics.length > 0 ? (
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={formattedData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="timestamp" />
              <YAxis />
              <Tooltip />
              <Legend />
              {eventNames.map((name, index) => (
                <Line
                  key={name}
                  type="monotone"
                  dataKey={name}
                  stroke={generateColor(name, index)}
                  connectNulls
                />
              ))}
            </LineChart>
          </ResponsiveContainer>
        ) : (
          <p className="text-muted-foreground">No performance data for this call yet.</p>
        )}
      </CardContent>
    </Card>
  );
}
