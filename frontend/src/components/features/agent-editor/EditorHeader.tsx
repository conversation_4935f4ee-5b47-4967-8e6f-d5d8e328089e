'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Save, Code } from 'lucide-react';

interface EditorHeaderProps {
  onSave: () => void;
  onViewJson: () => void;
  isJsonViewActive: boolean;
}

const EditorHeader = ({ onSave, onViewJson, isJsonViewActive }: EditorHeaderProps) => {
  return (
    <div className="flex items-center justify-between p-2 border-b">
      <h1 className="text-xl font-bold">Agent Editor</h1>
      <div className="flex items-center space-x-2">
        <Button variant={isJsonViewActive ? 'secondary' : 'outline'} onClick={onViewJson}>
          <Code className="h-4 w-4 mr-2" />
          View JSON
        </Button>
        <Button onClick={onSave}>
          <Save className="h-4 w-4 mr-2" />
          Save Workflow
        </Button>
      </div>
    </div>
  );
};

export default EditorHeader;
