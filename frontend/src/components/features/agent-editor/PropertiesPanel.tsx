'use client';

import React, { useEffect, useState } from 'react';
import { Node } from 'reactflow';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import NodeTestPanel from './NodeTestPanel';
import { agentEditorService } from '@/services/agent-editor/agentEditorService';

import { Workflow, NodeData, NodeConfig } from '@/types';

interface PropertiesPanelProps {
  selectedNode: Node<NodeData> | undefined;
  onNodeDataChange: (nodeId: string, data: Partial<NodeData>) => void;
  workflow: Workflow;
  onWorkflowChange: (newData: Partial<Workflow>) => void;
}

interface UiConfig {
  node_types: { type: string; label: string }[];
  tool_types: { type: string; label: string }[];
  llm_types: string[];
}

interface NodePropertiesProps {
  node: Node<NodeData>;
  onNodeDataChange: (nodeId: string, data: Partial<NodeData>) => void;
  workflow: Workflow;
  uiConfig: UiConfig | undefined;
}

const NodeProperties = ({ node, onNodeDataChange, workflow, uiConfig }: NodePropertiesProps) => {
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target;
    onNodeDataChange(node.id, { [id]: value });
  };

  const handleSelectChange = (id: string, value: string) => {
    onNodeDataChange(node.id, { [id]: value });
  };

  const renderNodeParams = () => {
    switch (node.type) {
      case 'intent_detect':
      case 'response_planner':
        return (
          <div className="space-y-2">
            <Label htmlFor="llm">LLM</Label>
            <Select
              value={node.data.llm}
              onValueChange={(value) => handleSelectChange('llm', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select LLM" />
              </SelectTrigger>
              <SelectContent>
                {uiConfig?.llm_types.map((llm) => (
                  <SelectItem key={llm} value={llm}>
                    {llm}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        );
      case 'book_appointment':
        return (
          <div className="space-y-2">
            <Label htmlFor="tool">Tool</Label>
            <Select
              value={node.data.tool}
              onValueChange={(value) => handleSelectChange('tool', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select Tool" />
              </SelectTrigger>
              <SelectContent>
                {uiConfig?.tool_types.map((tool) => (
                  <SelectItem key={tool.type} value={tool.type}>
                    {tool.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Node: {node.data.label}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="label">Label</Label>
            <Input id="label" value={node.data.label} onChange={handleInputChange} />
          </div>
          {renderNodeParams()}
        </CardContent>
      </Card>
      <NodeTestPanel node={node} workflow={workflow} />
    </>
  );
};

const WorkflowProperties = ({
  workflow,
  onWorkflowChange,
}: {
  workflow: Workflow;
  onWorkflowChange: (newData: Partial<Workflow>) => void;
}) => {
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target;
    onWorkflowChange({ [id]: value });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Workflow Properties</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="name">Name</Label>
          <Input id="name" value={workflow.name} onChange={handleInputChange} />
        </div>
        <div className="space-y-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={workflow.description || ''}
            onChange={handleInputChange}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="first_message">First Message</Label>
          <Textarea
            id="first_message"
            value={workflow.first_message || ''}
            onChange={handleInputChange}
          />
        </div>
      </CardContent>
    </Card>
  );
};

const PropertiesPanel = ({
  selectedNode,
  onNodeDataChange,
  workflow,
  onWorkflowChange,
}: PropertiesPanelProps) => {
  const [uiConfig, setUiConfig] = useState<UiConfig | undefined>(undefined);

  useEffect(() => {
    const fetchUiConfig = async () => {
      try {
        const config = await agentEditorService.getUiConfig();
        setUiConfig(config);
      } catch (error) {
        console.error('Failed to fetch UI config:', error);
      }
    };

    fetchUiConfig();
  }, []);

  return (
    <div className="p-4 h-full overflow-y-auto">
      {selectedNode ? (
        <NodeProperties
          node={selectedNode}
          onNodeDataChange={onNodeDataChange}
          workflow={workflow}
          uiConfig={uiConfig}
        />
      ) : (
        <WorkflowProperties workflow={workflow} onWorkflowChange={onWorkflowChange} />
      )}
    </div>
  );
};

export default PropertiesPanel;
