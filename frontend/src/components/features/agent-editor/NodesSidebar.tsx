'use client';

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';

const nodeTypes = [
  { type: 'input_gate', label: 'Input Gate' },
  { type: 'state_load', label: 'State Load' },
  { type: 'intent_detect', label: 'Intent Detect' },
  { type: 'rag_fetch', label: 'RAG Fetch' },
  { type: 'book_appointment', label: 'Book Appointment' },
  { type: 'response_planner', label: 'Response Planner' },
  { type: 'tts', label: 'TTS' },
  { type: 'state_persist', label: 'State Persist' },
];

const NodesSidebar = () => {
  const onDragStart = (event: React.DragEvent, nodeType: string) => {
    event.dataTransfer.setData('application/reactflow', nodeType);
    event.dataTransfer.effectAllowed = 'move';
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Nodes</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {nodeTypes.map((node) => (
            <div
              key={node.type}
              onDragStart={(event) => onDragStart(event, node.type)}
              draggable
              className="p-2 border rounded cursor-move"
            >
              {node.label}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default NodesSidebar;
