'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Settings, 
  MessageSquare, 
  Mic, 
  Volume2, 
  Brain, 
  Zap,
  Save,
  Play,
  Eye,
  EyeOff
} from 'lucide-react';
import { Agent, AgentUpdate, SpeechConfig, ConversationConfig } from '@/services/agents/agentService';

interface AgentConfigurationPanelProps {
  agent: Agent;
  onUpdate: (updates: AgentUpdate) => void;
  onSave: () => void;
  onTest: () => void;
  isLoading?: boolean;
}

export default function AgentConfigurationPanel({
  agent,
  onUpdate,
  onSave,
  onTest,
  isLoading = false,
}: AgentConfigurationPanelProps) {
  const [showSystemPrompt, setShowSystemPrompt] = useState(false);

  const updateBasicInfo = (field: keyof AgentUpdate, value: any) => {
    onUpdate({ [field]: value });
  };

  const updateSpeechConfig = (updates: Partial<SpeechConfig>) => {
    const currentConfig = agent.speech_config || {};
    onUpdate({
      speech_config: {
        ...currentConfig,
        ...updates,
      },
    });
  };

  const updateConversationConfig = (updates: Partial<ConversationConfig>) => {
    const currentConfig = agent.conversation_config || {};
    onUpdate({
      conversation_config: {
        ...currentConfig,
        ...updates,
      },
    });
  };

  const updateVoiceSettings = (updates: Partial<SpeechConfig['voice_settings']>) => {
    const currentConfig = agent.speech_config || {};
    const currentVoiceSettings = currentConfig.voice_settings || {};
    updateSpeechConfig({
      voice_settings: {
        ...currentVoiceSettings,
        ...updates,
      },
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Agent Configuration</h2>
          <p className="text-gray-600">Configure your agent's behavior and capabilities</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={onTest} disabled={isLoading}>
            <Play className="w-4 h-4 mr-2" />
            Test Agent
          </Button>
          <Button onClick={onSave} disabled={isLoading}>
            <Save className="w-4 h-4 mr-2" />
            Save Changes
          </Button>
        </div>
      </div>

      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="basic" className="flex items-center gap-2">
            <Settings className="w-4 h-4" />
            Basic
          </TabsTrigger>
          <TabsTrigger value="conversation" className="flex items-center gap-2">
            <MessageSquare className="w-4 h-4" />
            Conversation
          </TabsTrigger>
          <TabsTrigger value="speech" className="flex items-center gap-2">
            <Mic className="w-4 h-4" />
            Speech
          </TabsTrigger>
          <TabsTrigger value="workflow" className="flex items-center gap-2">
            <Brain className="w-4 h-4" />
            Workflow
          </TabsTrigger>
          <TabsTrigger value="tools" className="flex items-center gap-2">
            <Zap className="w-4 h-4" />
            Tools
          </TabsTrigger>
        </TabsList>

        {/* Basic Configuration */}
        <TabsContent value="basic" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>Configure the basic details of your agent</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Agent Name</Label>
                  <Input
                    id="name"
                    value={agent.name}
                    onChange={(e) => updateBasicInfo('name', e.target.value)}
                    placeholder="Enter agent name"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="status"
                      checked={agent.is_active}
                      onCheckedChange={(checked) => updateBasicInfo('is_active', checked)}
                    />
                    <Label htmlFor="status">
                      {agent.is_active ? 'Active' : 'Inactive'}
                    </Label>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={agent.description || ''}
                  onChange={(e) => updateBasicInfo('description', e.target.value)}
                  placeholder="Describe what this agent does"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="system-prompt">System Prompt</Label>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowSystemPrompt(!showSystemPrompt)}
                  >
                    {showSystemPrompt ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    {showSystemPrompt ? 'Hide' : 'Show'}
                  </Button>
                </div>
                {showSystemPrompt && (
                  <Textarea
                    id="system-prompt"
                    value={agent.system_prompt || ''}
                    onChange={(e) => updateBasicInfo('system_prompt', e.target.value)}
                    placeholder="Enter the system prompt that defines the agent's behavior"
                    rows={6}
                    className="font-mono text-sm"
                  />
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="first-message">First Message</Label>
                <Textarea
                  id="first-message"
                  value={agent.first_message || ''}
                  onChange={(e) => updateBasicInfo('first_message', e.target.value)}
                  placeholder="Enter the first message the agent will send"
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Conversation Configuration */}
        <TabsContent value="conversation" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Conversation Settings</CardTitle>
              <CardDescription>Configure how the agent handles conversations</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label>Max Conversation Turns</Label>
                  <Input
                    type="number"
                    value={agent.conversation_config?.max_turns || 50}
                    onChange={(e) => updateConversationConfig({ max_turns: parseInt(e.target.value) })}
                    min={1}
                    max={1000}
                  />
                  <p className="text-sm text-gray-500">Maximum number of conversation exchanges</p>
                </div>

                <div className="space-y-2">
                  <Label>Timeout (seconds)</Label>
                  <Input
                    type="number"
                    value={agent.conversation_config?.timeout_seconds || 300}
                    onChange={(e) => updateConversationConfig({ timeout_seconds: parseInt(e.target.value) })}
                    min={10}
                    max={3600}
                  />
                  <p className="text-sm text-gray-500">Conversation timeout in seconds</p>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label>Enable Interruptions</Label>
                    <p className="text-sm text-gray-500">Allow users to interrupt the agent while speaking</p>
                  </div>
                  <Switch
                    checked={agent.conversation_config?.enable_interruptions || false}
                    onCheckedChange={(checked) => updateConversationConfig({ enable_interruptions: checked })}
                  />
                </div>

                <Separator />

                <div className="space-y-2">
                  <Label>Conversation Memory</Label>
                  <Select
                    value={agent.conversation_config?.conversation_memory || 'medium'}
                    onValueChange={(value) => updateConversationConfig({ conversation_memory: value as any })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="short">Short (5 messages)</SelectItem>
                      <SelectItem value="medium">Medium (20 messages)</SelectItem>
                      <SelectItem value="long">Long (50 messages)</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-gray-500">How much conversation history to remember</p>
                </div>

                <div className="space-y-2">
                  <Label>Context Window</Label>
                  <Input
                    type="number"
                    value={agent.conversation_config?.context_window || 4000}
                    onChange={(e) => updateConversationConfig({ context_window: parseInt(e.target.value) })}
                    min={1000}
                    max={32000}
                  />
                  <p className="text-sm text-gray-500">Maximum tokens for conversation context</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Speech Configuration */}
        <TabsContent value="speech" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Speech-to-Text Settings</CardTitle>
              <CardDescription>Configure how the agent processes voice input</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>STT Provider</Label>
                  <Select
                    value={agent.speech_config?.stt_provider || 'openai'}
                    onValueChange={(value) => updateSpeechConfig({ stt_provider: value as any })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="openai">OpenAI Whisper</SelectItem>
                      <SelectItem value="google">Google Speech-to-Text</SelectItem>
                      <SelectItem value="azure">Azure Speech Services</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Language</Label>
                  <Select
                    value={agent.speech_config?.language || 'en-US'}
                    onValueChange={(value) => updateSpeechConfig({ language: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="en-US">English (US)</SelectItem>
                      <SelectItem value="en-GB">English (UK)</SelectItem>
                      <SelectItem value="es-ES">Spanish</SelectItem>
                      <SelectItem value="fr-FR">French</SelectItem>
                      <SelectItem value="de-DE">German</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Text-to-Speech Settings</CardTitle>
              <CardDescription>Configure how the agent generates voice output</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>TTS Provider</Label>
                  <Select
                    value={agent.speech_config?.tts_provider || 'openai'}
                    onValueChange={(value) => updateSpeechConfig({ tts_provider: value as any })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="openai">OpenAI TTS</SelectItem>
                      <SelectItem value="elevenlabs">ElevenLabs</SelectItem>
                      <SelectItem value="google">Google Text-to-Speech</SelectItem>
                      <SelectItem value="azure">Azure Speech Services</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Voice ID</Label>
                  <Input
                    value={agent.speech_config?.voice_id || ''}
                    onChange={(e) => updateSpeechConfig({ voice_id: e.target.value })}
                    placeholder="Enter voice ID"
                  />
                </div>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Speed: {agent.speech_config?.voice_settings?.speed || 1.0}</Label>
                  <Slider
                    value={[agent.speech_config?.voice_settings?.speed || 1.0]}
                    onValueChange={([value]) => updateVoiceSettings({ speed: value })}
                    min={0.5}
                    max={2.0}
                    step={0.1}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <Label>Pitch: {agent.speech_config?.voice_settings?.pitch || 1.0}</Label>
                  <Slider
                    value={[agent.speech_config?.voice_settings?.pitch || 1.0]}
                    onValueChange={([value]) => updateVoiceSettings({ pitch: value })}
                    min={0.5}
                    max={2.0}
                    step={0.1}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <Label>Stability: {agent.speech_config?.voice_settings?.stability || 0.5}</Label>
                  <Slider
                    value={[agent.speech_config?.voice_settings?.stability || 0.5]}
                    onValueChange={([value]) => updateVoiceSettings({ stability: value })}
                    min={0}
                    max={1}
                    step={0.1}
                    className="w-full"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Workflow Configuration */}
        <TabsContent value="workflow" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Workflow Configuration</CardTitle>
              <CardDescription>Configure the agent's conversation flow and logic</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Brain className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-semibold mb-2">Workflow Editor</h3>
                <p className="text-gray-600 mb-4">
                  Visual workflow editor coming soon. For now, you can configure basic conversation settings.
                </p>
                <Button variant="outline">
                  Open Advanced Editor
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tools Configuration */}
        <TabsContent value="tools" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Global Tools</CardTitle>
              <CardDescription>Configure tools and integrations available to your agent</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Zap className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-semibold mb-2">Tools & Integrations</h3>
                <p className="text-gray-600 mb-4">
                  Add external tools and APIs that your agent can use during conversations.
                </p>
                <Button variant="outline">
                  Browse Tool Library
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
