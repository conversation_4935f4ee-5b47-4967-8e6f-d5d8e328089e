'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useMutation } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { Play } from 'lucide-react';

import { Node } from 'reactflow'; // Add Node import
import { Workflow, NodeConfig, NodeData } from '@/types'; // Ensure NodeData is imported

// A mock API function for now. Replace with your actual API call.
interface TestNodePayload {
  node_config: NodeConfig;
  input_state: Record<string, unknown>;
  workflow: Workflow;
}

const testNodeApi = async (payload: TestNodePayload) => {
  const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/orchestrator/test-node`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(payload),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.detail || 'Failed to test node');
  }
  return response.json();
};

interface NodeTestPanelProps {
  node: Node<NodeData>; // Change type to Node<NodeData>
  workflow: Workflow;
}

const NodeTestPanel = ({ node, workflow }: NodeTestPanelProps) => {
  const [inputState, setInputState] = useState('{}');
  const [outputState, setOutputState] = useState('');

  const testMutation = useMutation({
    mutationFn: testNodeApi,
    onSuccess: (data) => {
      setOutputState(JSON.stringify(data, null, 2));
      toast.success('Node executed successfully!');
    },
    onError: (error: Error) => {
      setOutputState(`Error: ${error.message}`);
      toast.error(`Error: ${error.message}`);
    },
  });

  const handleRunTest = () => {
    try {
      const parsedInput = JSON.parse(inputState);
      const payload: TestNodePayload = {
        node_config: { ...node.data, id: node.id, position: [node.position.x, node.position.y] }, // Construct NodeConfig
        input_state: parsedInput,
        workflow: workflow,
      };
      testMutation.mutate(payload);
    } catch (e) {
      toast.error('Invalid JSON in Input State.');
    }
  };

  return (
    <Card className="mt-4">
      <CardHeader>
        <CardTitle>Node Testing</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="input-state">Input State</Label>
          <Textarea
            id="input-state"
            value={inputState}
            onChange={(e) => setInputState(e.target.value)}
            placeholder='{ "key": "value" }'
            rows={6}
          />
        </div>
        <Button onClick={handleRunTest} disabled={testMutation.isPending}>
          <Play className="h-4 w-4 mr-2" />
          {testMutation.isPending ? 'Running...' : 'Run Test'}
        </Button>
        <div className="space-y-2">
          <Label htmlFor="output-state">Output State</Label>
          <Textarea
            id="output-state"
            value={outputState}
            readOnly
            placeholder="Node output will appear here..."
            rows={8}
            className="bg-muted"
          />
        </div>
      </CardContent>
    </Card>
  );
};

export default NodeTestPanel;
