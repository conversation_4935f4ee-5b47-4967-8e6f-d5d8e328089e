'use client';

import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { PlusIcon } from 'lucide-react';
import { agentTemplateService } from '@/services/agent-templates/agentTemplateService';
import { useRouter } from 'next/navigation';

interface AgentTemplate {
  id: number;
  name: string;
  description: string | undefined;
}

interface CreateAgentDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const CreateAgentDialog = ({ isOpen, onClose }: CreateAgentDialogProps) => {
  const [templates, setTemplates] = useState<AgentTemplate[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const router = useRouter();

  useEffect(() => {
    if (isOpen) {
      const fetchTemplates = async () => {
        try {
          const fetchedTemplates = await agentTemplateService.getAllTemplates();
          setTemplates(fetchedTemplates);
        } catch (error) {
          console.error('Failed to fetch agent templates:', error);
        }
      };
      fetchTemplates();
    }
  }, [isOpen]);

  const filteredTemplates = templates.filter((template) =>
    template.name.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  const handleCreateBlank = () => {
    router.push('/agents/new');
    onClose();
  };

  const handleSelectTemplate = (templateId: number) => {
    router.push(`/agents/new?template=${templateId}`);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>Create a new agent</DialogTitle>
        </DialogHeader>
        <div className="p-6 space-y-6">
          <Input
            placeholder="Search templates..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card
              className="cursor-pointer hover:shadow-lg transition-shadow"
              onClick={handleCreateBlank}
            >
              <CardHeader>
                <CardTitle className="flex items-center justify-center">
                  <PlusIcon className="h-12 w-12" />
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="font-semibold">Start from scratch</p>
              </CardContent>
            </Card>
            {filteredTemplates.map((template) => (
              <Card
                key={template.id}
                className="cursor-pointer hover:shadow-lg transition-shadow"
                onClick={() => handleSelectTemplate(template.id)}
              >
                <CardHeader>
                  <CardTitle>{template.name}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-500">{template.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CreateAgentDialog;
