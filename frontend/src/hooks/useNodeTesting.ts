// frontend/src/hooks/useNodeTesting.ts
'use client';

import { useState, useCallback } from 'react';
import { NodeConfig, NodeTestResult, NodeValidationResult } from '@/types/workflow';
import { apiClient } from '@/services/api/apiClient';
import { validateNodeConfig } from '@/lib/agent-utils';

interface UseNodeTestingReturn {
  // Testing state
  isTestingNode: boolean;
  testResult: NodeTestResult | null;
  testError: string | null;
  
  // Validation state
  validationResult: NodeValidationResult | null;
  
  // Testing functions
  testNode: (nodeConfig: NodeConfig, testInput: Record<string, any>) => Promise<NodeTestResult>;
  validateNode: (nodeConfig: NodeConfig) => NodeValidationResult;
  clearTestResult: () => void;
  
  // Batch testing
  batchTestNodes: (nodeConfigs: NodeConfig[], testInput: Record<string, any>) => Promise<NodeTestResult[]>;
  
  // Test case management
  createTestCase: (nodeConfig: NodeConfig, input: Record<string, any>, expectedOutput?: Record<string, any>) => TestCase;
  runTestCase: (nodeConfig: NodeConfig, testCase: TestCase) => Promise<NodeTestResult>;
}

interface TestCase {
  id: string;
  name: string;
  description?: string;
  input: Record<string, any>;
  expectedOutput?: Record<string, any>;
  nodeId: string;
  createdAt: string;
}

export const useNodeTesting = (): UseNodeTestingReturn => {
  const [isTestingNode, setIsTestingNode] = useState(false);
  const [testResult, setTestResult] = useState<NodeTestResult | null>(null);
  const [testError, setTestError] = useState<string | null>(null);
  const [validationResult, setValidationResult] = useState<NodeValidationResult | null>(null);

  const testNode = useCallback(async (
    nodeConfig: NodeConfig, 
    testInput: Record<string, any>
  ): Promise<NodeTestResult> => {
    setIsTestingNode(true);
    setTestError(null);
    
    try {
      // First validate the node configuration
      const validation = validateNodeConfig(nodeConfig);
      setValidationResult(validation);
      
      if (!validation.isValid) {
        const result: NodeTestResult = {
          success: false,
          error: `Node configuration is invalid: ${validation.errors.join(', ')}`,
          executionTime: 0,
          logs: ['Validation failed'],
        };
        setTestResult(result);
        return result;
      }

      // Call the backend API to test the node
      const response = await apiClient.testNode(nodeConfig, testInput);
      
      const result: NodeTestResult = {
        success: response.success || false,
        output: response.response || response.result,
        error: response.error,
        executionTime: response.execution_time_ms || 0,
        tokensUsed: response.tokens_used,
        logs: response.logs || [],
      };
      
      setTestResult(result);
      return result;
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      setTestError(errorMessage);
      
      const result: NodeTestResult = {
        success: false,
        error: errorMessage,
        executionTime: 0,
        logs: [`Error: ${errorMessage}`],
      };
      
      setTestResult(result);
      return result;
      
    } finally {
      setIsTestingNode(false);
    }
  }, []);

  const validateNode = useCallback((nodeConfig: NodeConfig): NodeValidationResult => {
    const validation = validateNodeConfig(nodeConfig);
    setValidationResult(validation);
    return validation;
  }, []);

  const clearTestResult = useCallback(() => {
    setTestResult(null);
    setTestError(null);
    setValidationResult(null);
  }, []);

  const batchTestNodes = useCallback(async (
    nodeConfigs: NodeConfig[], 
    testInput: Record<string, any>
  ): Promise<NodeTestResult[]> => {
    const results: NodeTestResult[] = [];
    
    for (const nodeConfig of nodeConfigs) {
      try {
        const result = await testNode(nodeConfig, testInput);
        results.push(result);
      } catch (error) {
        results.push({
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          executionTime: 0,
          logs: [`Batch test failed for node ${nodeConfig.id}`],
        });
      }
    }
    
    return results;
  }, [testNode]);

  const createTestCase = useCallback((
    nodeConfig: NodeConfig,
    input: Record<string, any>,
    expectedOutput?: Record<string, any>
  ): TestCase => {
    return {
      id: `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: `Test case for ${nodeConfig.name}`,
      description: `Test case for ${nodeConfig.type} node`,
      input,
      expectedOutput,
      nodeId: nodeConfig.id,
      createdAt: new Date().toISOString(),
    };
  }, []);

  const runTestCase = useCallback(async (
    nodeConfig: NodeConfig,
    testCase: TestCase
  ): Promise<NodeTestResult> => {
    const result = await testNode(nodeConfig, testCase.input);
    
    // If expected output is provided, compare it with actual output
    if (testCase.expectedOutput && result.output) {
      const outputMatches = JSON.stringify(result.output) === JSON.stringify(testCase.expectedOutput);
      
      if (!outputMatches) {
        return {
          ...result,
          success: false,
          error: 'Output does not match expected result',
          logs: [
            ...(result.logs || []),
            'Expected output comparison failed',
            `Expected: ${JSON.stringify(testCase.expectedOutput)}`,
            `Actual: ${JSON.stringify(result.output)}`,
          ],
        };
      }
    }
    
    return result;
  }, [testNode]);

  return {
    // State
    isTestingNode,
    testResult,
    testError,
    validationResult,
    
    // Functions
    testNode,
    validateNode,
    clearTestResult,
    batchTestNodes,
    createTestCase,
    runTestCase,
  };
};

// Helper function to generate common test inputs for different node types
export const generateTestInput = (nodeType: string): Record<string, any> => {
  switch (nodeType) {
    case 'llm':
      return {
        user_message: 'Hello, how are you?',
        context: {},
      };
    
    case 'dspyModule':
      return {
        input: 'Sample input for DSPy module',
      };
    
    case 'memory':
      return {
        operation: 'search',
        query: 'test query',
        content: 'test content to store',
      };
    
    case 'tool':
      return {
        action: 'test',
        parameters: {},
      };
    
    case 'conditional':
      return {
        condition_input: 'test condition',
        context: {},
      };
    
    case 'rag':
      return {
        query: 'What is the meaning of life?',
        context: {},
      };
    
    case 'pythonFunction':
      return {
        args: [],
        kwargs: {},
      };
    
    default:
      return {
        message: 'Test input',
        context: {},
      };
  }
};

// Helper function to create comprehensive test suites
export const createNodeTestSuite = (nodeConfig: NodeConfig) => {
  const baseInput = generateTestInput(nodeConfig.type);
  
  return {
    id: `suite_${nodeConfig.id}_${Date.now()}`,
    name: `Test Suite for ${nodeConfig.name}`,
    description: `Comprehensive test suite for ${nodeConfig.type} node`,
    nodeId: nodeConfig.id,
    testCases: [
      {
        name: 'Basic functionality test',
        input: baseInput,
        description: 'Tests basic node functionality',
      },
      {
        name: 'Empty input test',
        input: {},
        description: 'Tests node behavior with empty input',
      },
      {
        name: 'Invalid input test',
        input: { invalid: 'data' },
        description: 'Tests node error handling with invalid input',
      },
    ],
    createdAt: new Date().toISOString(),
  };
};
