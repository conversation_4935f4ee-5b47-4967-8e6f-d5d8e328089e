// frontend/src/types/index.ts

import { Workflow, NodeConfig, SpeechConfig, Connections } from './workflow'; // Import consolidated types

export interface Agent {
  id: number;
  name: string;
  description?: string;
  workflow?: Workflow;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface ConversationConfig {
  max_turns?: number;
  timeout_seconds?: number;
  enable_interruptions?: boolean;
  conversation_memory?: 'short' | 'medium' | 'long';
  context_window?: number;
}

export interface Customer {
  id?: number; // Made optional
  name: string;
  phone: string; // This seems to be 'phone' in some places and 'phone_number' in others. I'll use 'phone' for consistency in the frontend.
  phone_number: string | undefined; // Keep this for now if backend uses it
  email: string | undefined;
  address: string | undefined;
  city?: string | undefined; // Optional, as not all Customer interfaces have it
  state?: string | undefined; // Optional
  zip_code?: string | undefined; // Optional
  domain?: string | undefined; // Optional
  created_at?: string; // Made optional
  updated_at?: string; // Made optional
}

export interface Product {
  id: number;
  name: string;
  description: string | undefined;
  price: number;
  created_at: string;
  updated_at: string;
}

export interface OrderItem {
  id: number;
  order_id: number;
  product_id: number;
  quantity: number;
  price: number;
  created_at: string;
  updated_at: string;
  product: Product;
}

export interface Order {
  id: number;
  customer_id: number;
  status: string;
  total_amount: number;
  created_at: string;
  updated_at: string;
  items: OrderItem[];
}

export interface Insight {
  // Define insight properties based on actual data structure
  // For now, assuming it's a string as per LivePerformanceChart.tsx
  // If it's an object, its properties should be defined here.
  content: string; // Assuming insights are just strings for now
}

export interface Action {
  // Define action properties based on actual data structure
  // For now, assuming it's a string as per LivePerformanceChart.tsx
  // If it's an object, its properties should be defined here.
  content: string; // Assuming actions are just strings for now
}

export interface ConversationLogEventData {
  content?: string;
  tool?: string;
  input?: string;
  output?: string | object;
  messages?: unknown[];
  response?: string | object;
  is_final?: boolean;
}

export interface ConversationLogEntry {
  event_type: string;
  source: string;
  data: ConversationLogEventData;
  timestamp: string;
}

export interface ServerMessage {
  type: 'conversation_log' | 'transcript' | 'insight' | 'action' | 'audio';
  log?: ConversationLogEntry;
  transcript?: string;
  is_final?: boolean;
  sender?: 'user' | 'agent';
  insight?: Insight;
  action?: Action;
  audio?: string;
  data?: unknown; // For other generic data in ServerMessage
}

export interface TestPrompt {
  id: number | string;
  name: string;
  phone_number?: string | undefined; // Make it nullable
  system_prompt: string;
  first_message: string;
}

export interface PhoneNumber {
  id: number;
  phone_number: string;
  friendly_name: string | undefined;
  provider: string;
  account_sid: string;
  customer_id: number;
  capabilities: string[];
  created_at: string;
  updated_at: string;
}

// Legacy alias for backward compatibility
export interface TwilioNumber extends PhoneNumber {}

export interface Job {
  id?: number;
  customer_id: number;
  service_type: string;
  scheduled_time: string;
  status: string;
  notes: string | undefined;
  created_at?: string;
  updated_at?: string;
}

export interface InitiateCallPayload {
  to: string;
  from: string;
  customer_id?: number;
  custom_prompt?: string;
  custom_first_message?: string;
  phone_number?: string; // Added phone_number
}

export interface InitiateWebCallPayload {
  customer_id?: number;
  custom_prompt?: string;
  custom_first_message?: string;
}

export interface InitiateTextChatPayload {
  customer_id?: number;
  custom_prompt: string;
  custom_first_message: string;
}

export interface CallHistoryUpdatePayload {
  call_duration?: number;
  status?: string;
}

export interface PerformanceMetric {
  call_history_id: number;
  timestamp: string;
  service: string;
  action: string;
  latency: number;
}

export interface NodeData {
  label: string;
  llm?: string;
  tool?: string;
  name: string;
  type: string;
  functionPath: string;
  parameters: Record<string, unknown>;
  dependencies: string[];
}