// frontend/src/types/workflow.ts

// ============================================================================
// CORE ENUMS - Synchronized with backend
// ============================================================================

export enum NodeType {
  PYTHON_FUNCTION = 'pythonFunction',
  LLM = 'llm',
  TOOL = 'tool',
  CONDITIONAL = 'conditional',
  MEMORY = 'memory',
  DSPY_MODULE = 'dspyModule',
  RAG = 'rag',
  INPUT = 'input',
  OUTPUT = 'output',
}

export enum MemoryType {
  SHORT_TERM = 'short_term',
  LONG_TERM = 'long_term',
  EPISODIC = 'episodic',
  SEMANTIC = 'semantic',
  WORKING = 'working',
}

export enum DSPyModuleType {
  PREDICT = 'predict',
  CHAIN_OF_THOUGHT = 'chain_of_thought',
  REACT = 'react',
  RETRIEVE = 'retrieve',
  GENERATE = 'generate',
  CLASSIFY = 'classify',
  SUMMARIZE = 'summarize',
}

export enum ToolParameterType {
  STRING = 'string',
  INTEGER = 'integer',
  FLOAT = 'float',
  BOOLEAN = 'boolean',
  ARRAY = 'array',
  OBJECT = 'object',
  FILE = 'file',
}

export enum AgentType {
  SINGLE_NODE = 'single_node',
  MULTI_NODE = 'multi_node',
  HYBRID = 'hybrid',
}

export enum AgentCategory {
  CUSTOMER_SERVICE = 'customer_service',
  SALES = 'sales',
  SUPPORT = 'support',
  ANALYSIS = 'analysis',
  CREATIVE = 'creative',
  RESEARCH = 'research',
  AUTOMATION = 'automation',
  CUSTOM = 'custom',
}

// ============================================================================
// CONFIGURATION INTERFACES
// ============================================================================

export interface MemoryConfig {
  type: MemoryType;
  max_entries?: number;
  ttl_seconds?: number;
  embedding_model?: string;
  similarity_threshold?: number;
}

export interface DSPyConfig {
  module_type: DSPyModuleType;
  signature: string;
  examples: Record<string, any>[];
  optimizer?: string;
  max_bootstrapped_demos?: number;
  max_labeled_demos?: number;
}

export interface ToolParameter {
  name: string;
  type: ToolParameterType;
  description: string;
  required: boolean;
  default?: any;
  enum_values?: any[];
  min_value?: number;
  max_value?: number;
  pattern?: string;
  examples?: any[];
}

export interface ToolMetadata {
  name: string;
  description: string;
  parameters: ToolParameter[];
  category?: string;
  icon?: string;
}

// Enhanced NodeConfig with all required fields for full functionality
export interface NodeConfig {
  id: string;
  name: string;
  type: NodeType;
  functionPath?: string;
  parameters: Record<string, any>;
  dependencies: string[];
  position?: number[];

  // Enhanced configurations
  system_prompt?: string;
  memory_config?: MemoryConfig;
  dspy_config?: DSPyConfig;
  tools?: string[];

  // UI and metadata
  ui_config: Record<string, any>;
  description?: string;
  category?: string;

  // Validation and testing
  input_schema?: Record<string, any>;
  output_schema?: Record<string, any>;
  test_cases?: Array<{
    name: string;
    input: Record<string, any>;
    expected_output?: Record<string, any>;
  }>;
}

export interface ConnectionTarget {
  node: string;
  type: string;
  index: number;
}

export interface MainConnection {
  main: ConnectionTarget[];
}

export interface ConditionalConnection {
  type: string;
  conditionFunctionPath?: string;
  prompt?: string;
  paths: Record<string, ConnectionTarget[]>;
}

export type Connections = Record<string, MainConnection | { conditional: ConditionalConnection }>;

// ============================================================================
// CONNECTION AND EDGE TYPES - Enhanced for LangGraph support
// ============================================================================

// Enhanced connection types for React Flow integration
export interface ReactFlowConnection {
  id: string;
  source: string;
  target: string;
  sourceHandle?: string;
  targetHandle?: string;
  type?: 'default' | 'conditional' | 'custom';
  label?: string;
  animated?: boolean;
  style?: Record<string, any>;
  data?: Record<string, any>;
}

// ============================================================================
// TOOL AND CONFIGURATION TYPES
// ============================================================================

export type ToolConfig = {
  name: string;
  description: string;
  functionPath?: string;
  parameters?: Record<string, any>;
  category?: string;
  enabled?: boolean;
};

export interface SpeechConfig {
  stt_vad_threshold?: number;
  tts_voice_id?: string;
  tts_speaking_rate?: number;
}

export interface ConversationSettings {
  silence_timeout_seconds?: number;
  reprompt_messages?: string[];
  max_reprompts?: number;
  no_input_fallback_message?: string;
  no_match_fallback_message?: string;
}

export interface GlobalMemoryConfig {
  enabled: boolean;
  types: MemoryType[];
  max_total_entries?: number;
  cleanup_interval_hours?: number;
}

// ============================================================================
// WORKFLOW AND EXECUTION TYPES
// ============================================================================

export interface ExecutionMetrics {
  node_id: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  duration_ms?: number;
  tokens_used?: number;
  error_message?: string;
  input_data?: Record<string, any>;
  output_data?: Record<string, any>;
}

export interface AgentExecutionState {
  execution_id: string;
  status: 'idle' | 'running' | 'completed' | 'failed';
  currentNodeId?: string;
  nodeMetrics: ExecutionMetrics[];
  totalTokensUsed: number;
  totalExecutionTime: number;
  error?: string;
}

export interface Workflow {
  name: string;
  description?: string;
  active: boolean;
  id: string;
  nodes: NodeConfig[];
  connections: Connections;
  start_node_id: string;
  first_message?: string;
  speech_config?: SpeechConfig;
  conversation_settings?: ConversationSettings;
  tools?: ToolConfig[];
  agent_type: AgentType;
  category?: AgentCategory;
  tags: string[];
  version: string;
  global_memory_config?: GlobalMemoryConfig;
  max_execution_time_seconds?: number;
  max_iterations?: number;
  error_handling_strategy: 'stop' | 'continue' | 'retry';
  canvas_settings?: Record<string, any>;
}

// ============================================================================
// REACT FLOW INTEGRATION TYPES
// ============================================================================

export interface ReactFlowNodeData {
  label: string;
  type: NodeType;
  description?: string;
  status?: string;
  executionTime?: number;
  config: NodeConfig;
  isValid?: boolean;
  errors?: string[];
}

export interface ReactFlowEdgeData {
  condition?: string;
  isConditional?: boolean;
  sourceHandle?: string;
  targetHandle?: string;
}

// ============================================================================
// NODE TESTING AND VALIDATION TYPES
// ============================================================================

export interface NodeTestResult {
  success: boolean;
  output?: Record<string, any>;
  error?: string;
  executionTime?: number;
  tokensUsed?: number;
  logs?: string[];
}

export interface NodeValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// ============================================================================
// ENHANCED NODE TYPES FOR UI
// ============================================================================

export interface NodeTypeDefinition {
  type: NodeType;
  name: string;
  description: string;
  category: string;
  icon: string;
  parameters: ToolParameter[];
  requiredDependencies?: string[];
  supportedHandles?: {
    inputs: string[];
    outputs: string[];
  };
}
