// frontend/src/types/workflow.ts
export enum NodeType {
  PYTHON_FUNCTION = "pythonFunction",
  LLM = "llm",
  TOOL = "tool",
  CONDITIONAL = "conditional",
  MEMORY = "memory",
  DSPY_MODULE = "dspyModule",
  RAG = "rag",
  INPUT = "input",
  OUTPUT = "output",
}

export enum MemoryType {
  SHORT_TERM = "short_term",
  LONG_TERM = "long_term",
  EPISODIC = "episodic",
  SEMANTIC = "semantic",
  WORKING = "working",
}

export enum DSPyModuleType {
  PREDICT = "predict",
  CHAIN_OF_THOUGHT = "chain_of_thought",
  REACT = "react",
  RETRIEVE = "retrieve",
  GENERATE = "generate",
  CLASSIFY = "classify",
  SUMMARIZE = "summarize",
}

export interface MemoryConfig {
  type: MemoryType;
  max_entries?: number;
  ttl_seconds?: number;
  embedding_model?: string;
  similarity_threshold?: number;
}

export interface DSPyConfig {
  module_type: DSPyModuleType;
  signature: string;
  examples: Record<string, any>[];
  optimizer?: string;
  max_bootstrapped_demos?: number;
  max_labeled_demos?: number;
}

export interface NodeConfig {
  id: string;
  name: string;
  type: NodeType;
  functionPath?: string;
  parameters: Record<string, any>;
  dependencies: string[];
  position?: number[];
  system_prompt?: string;
  memory_config?: MemoryConfig;
  dspy_config?: DSPyConfig;
  tools?: string[];
  ui_config: Record<string, any>;
  description?: string;
  category?: string;
}

export interface ConnectionTarget {
  node: string;
  type: string;
  index: number;
}

export interface MainConnection {
  main: ConnectionTarget[];
}

export interface ConditionalConnection {
  type: string;
  conditionFunctionPath?: string;
  prompt?: string;
  paths: Record<string, ConnectionTarget[]>;
}

export type Connections = Record<string, MainConnection | { conditional: ConditionalConnection }>;

// --- Start of missing ToolMetadata and ToolParameterType definitions ---

export enum ToolParameterType {
  STRING = "string",
  INTEGER = "integer",
  FLOAT = "float",
  BOOLEAN = "boolean",
  ARRAY = "array",
  OBJECT = "object",
  FILE = "file",
}

export interface ToolParameter {
  name: string;
  type: ToolParameterType;
  description: string;
  required: boolean;
  default?: any;
  enum_values?: any[];
  min_value?: number;
  max_value?: number;
  pattern?: string;
  examples?: any[];
}

export interface ToolMetadata {
  name: string;
  description: string;
  parameters: ToolParameter[];
}

// --- End of missing ToolMetadata and ToolParameterType definitions ---

export type ToolConfig = any; // Using 'any' for now as the ToolConfig is complex and not immediately needed for this task.

export interface SpeechConfig {
  stt_vad_threshold?: number;
  tts_voice_id?: string;
  tts_speaking_rate?: number;
}

export interface ConversationSettings {
  silence_timeout_seconds?: number;
  reprompt_messages?: string[];
  max_reprompts?: number;
  no_input_fallback_message?: string;
  no_match_fallback_message?: string;
}

export enum AgentType {
  SINGLE_NODE = "single_node",
  MULTI_NODE = "multi_node",
  HYBRID = "hybrid",
}

export enum AgentCategory {
  CUSTOMER_SERVICE = "customer_service",
  SALES = "sales",
  SUPPORT = "support",
  ANALYSIS = "analysis",
  CREATIVE = "creative",
  RESEARCH = "research",
  AUTOMATION = "automation",
  CUSTOM = "custom",
}

export interface GlobalMemoryConfig {
  enabled: boolean;
  types: MemoryType[];
  max_total_entries?: number;
  cleanup_interval_hours?: number;
}

export interface Workflow {
  name: string;
  description?: string;
  active: boolean;
  id: string;
  nodes: NodeConfig[];
  connections: Connections;
  start_node_id: string;
  first_message?: string;
  speech_config?: SpeechConfig;
  conversation_settings?: ConversationSettings;
  tools?: ToolConfig[];
  agent_type: AgentType;
  category?: AgentCategory;
  tags: string[];
  version: string;
  global_memory_config?: GlobalMemoryConfig;
  max_execution_time_seconds?: number;
  max_iterations?: number;
  error_handling_strategy: "stop" | "continue" | "retry";
  canvas_settings?: Record<string, any>;
}