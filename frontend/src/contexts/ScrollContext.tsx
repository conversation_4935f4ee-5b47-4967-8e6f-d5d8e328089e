import React, { createContext, useContext, useRef } from 'react';
import type { ReactNode } from 'react';

interface ScrollContextType {
  saveScrollPosition: (key: string) => void;
  restoreScrollPosition: (key: string) => void;
  scrollToTop: () => void;
}

const ScrollContext = createContext<ScrollContextType | undefined>(undefined);

interface ScrollProviderProps {
  children: ReactNode;
}

export const ScrollProvider: React.FC<ScrollProviderProps> = ({ children }) => {
  const scrollPositions = useRef<Record<string, number>>({});

  const saveScrollPosition = (key: string) => {
    scrollPositions.current[key] = window.scrollY;
  };

  const restoreScrollPosition = (key: string) => {
    const savedPosition = scrollPositions.current[key];
    if (savedPosition !== undefined) {
      // Use setTimeout to ensure DOM is ready
      setTimeout(() => {
        window.scrollTo({
          top: savedPosition,
          behavior: 'auto', // Use 'auto' for instant scroll
        });
      }, 0);
    }
  };

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'auto', // Use 'auto' for instant scroll
    });
  };

  return (
    <ScrollContext.Provider value={{ saveScrollPosition, restoreScrollPosition, scrollToTop }}>
      {children}
    </ScrollContext.Provider>
  );
};

export const useScroll = () => {
  const context = useContext(ScrollContext);
  if (context === undefined) {
    throw new Error('useScroll must be used within a ScrollProvider');
  }
  return context;
};
