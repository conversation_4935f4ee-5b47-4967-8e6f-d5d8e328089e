'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface CustomerNodeConfig {
  id: string;
  name: string;
  type: string;
  description?: string;
  // Add other properties relevant to a customer node
}

interface CustomerWorkflow {
  id: string;
  name: string;
  description: string;
  nodes: CustomerNodeConfig[];
  category: string;
  version: string;
}

const CustomerBuilderPage = () => {
  const router = useRouter();

  const [currentCustomerWorkflow, setCurrentCustomerWorkflow] = useState<CustomerWorkflow>({
    id: `customer_workflow_${Date.now()}`,
    name: 'New Customer Workflow',
    description: 'A new workflow for managing customer interactions',
    nodes: [],
    category: 'general',
    version: '1.0.0',
  });

  const [selectedCustomerNode, setSelectedCustomerNode] = useState<CustomerNodeConfig | null>(null);

  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <div className="border-b bg-white px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" onClick={() => router.back()}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-2xl font-bold">Customer Builder</h1>
              <p className="text-sm text-gray-600">Design and manage customer workflows</p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Area (simplified for this request) */}
      <div className="flex-1 flex">
        {/* Placeholder for main content/canvas */}
        <div className="flex-1 p-4">
          <Card className="h-full flex items-center justify-center">
            <CardContent>
              <p className="text-gray-500">Main content area for customer workflow canvas or other tools.</p>
              <Button onClick={() => setSelectedCustomerNode({ id: 'node_123', name: 'Example Node', type: 'data_input' })} className="mt-4">
                Select Example Node
              </Button>
              <Button onClick={() => setSelectedCustomerNode(null)} className="mt-4 ml-2">
                Deselect Node
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Properties Panel */}
        <div className="w-96 border-l bg-white">
          {selectedCustomerNode ? (
            <div className="h-full flex flex-col">
              <div className="p-4 border-b">
                <h3 className="font-medium">Customer Node Properties</h3>
                <p className="text-sm text-gray-600">{selectedCustomerNode.name}</p>
              </div>
              <ScrollArea className="flex-1 p-4">
                <div className="space-y-4">
                  <div>
                    <Label>Node ID</Label>
                    <Input value={selectedCustomerNode.id} disabled />
                  </div>
                  <div>
                    <Label>Type</Label>
                    <Badge variant="outline">{selectedCustomerNode.type}</Badge>
                  </div>
                  <div>
                    <Label>Description</Label>
                    <Textarea value={selectedCustomerNode.description || ''} rows={2} disabled />
                  </div>
                  {/* Add more node-specific properties here */}
                </div>
              </ScrollArea>
            </div>
          ) : (
            <div className="h-full flex flex-col">
              <div className="p-4 border-b">
                <h3 className="font-medium">Customer Workflow Properties</h3>
                <p className="text-sm text-gray-600">Configure the overall customer workflow</p>
              </div>
              <ScrollArea className="flex-1 p-4">
                <div className="space-y-4">
                  <div>
                    <Label>Workflow ID</Label>
                    <Input value={currentCustomerWorkflow.id} disabled />
                  </div>
                  <div>
                    <Label>Name</Label>
                    <Input
                      value={currentCustomerWorkflow.name}
                      onChange={(e) =>
                        setCurrentCustomerWorkflow((prev) => ({ ...prev, name: e.target.value }))
                      }
                    />
                  </div>
                  <div>
                    <Label>Description</Label>
                    <Textarea
                      value={currentCustomerWorkflow.description}
                      onChange={(e) =>
                        setCurrentCustomerWorkflow((prev) => ({ ...prev, description: e.target.value }))
                      }
                      rows={3}
                    />
                  </div>
                  <div>
                    <Label>Category</Label>
                    <Select
                      value={currentCustomerWorkflow.category}
                      onValueChange={(value) =>
                        setCurrentCustomerWorkflow((prev) => ({ ...prev, category: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="general">General</SelectItem>
                        <SelectItem value="onboarding">Onboarding</SelectItem>
                        <SelectItem value="support">Support</SelectItem>
                        <SelectItem value="sales">Sales</SelectItem>
                        <SelectItem value="feedback">Feedback</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label>Version</Label>
                    <Input value={currentCustomerWorkflow.version} disabled />
                  </div>
                </div>
              </ScrollArea>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CustomerBuilderPage;
