'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { JsonViewer } from '@/components/JsonViewer';
import {
  ArrowLeft,
  Search,
  Filter,
  Star,
  Download,
  Eye,
  Sparkles,
  Bot,
  MessageSquare,
  Phone,
  Mic,
  ShoppingCart,
  Headphones,
  BookOpen,
  Briefcase,
  Heart,
  Zap,
} from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import { agentService, AgentTemplate } from '@/services/agents/agentService';

const templateCategories = [
  { id: 'all', name: 'All Templates', icon: Sparkles },
  { id: 'customer-service', name: 'Customer Service', icon: Headphones },
  { id: 'sales', name: 'Sales & Marketing', icon: ShoppingCart },
  { id: 'education', name: 'Education', icon: BookOpen },
  { id: 'business', name: 'Business', icon: Briefcase },
  { id: 'healthcare', name: 'Healthcare', icon: Heart },
  { id: 'utilities', name: 'Utilities', icon: Zap },
];

export default function AgentTemplatesPage() {
  const [templates, setTemplates] = useState<AgentTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [categories, setCategories] = useState<string[]>(['all']);
  const [selectedTemplate, setSelectedTemplate] = useState<AgentTemplate | null>(null);
  const [isViewerOpen, setIsViewerOpen] = useState(false);
  const [isCreateAgentModalOpen, setIsCreateAgentModalOpen] = useState(false);
  const [selectedTemplateForCreation, setSelectedTemplateForCreation] =
    useState<AgentTemplate | null>(null);
  const [newAgentName, setNewAgentName] = useState('');
  const [newAgentDescription, setNewAgentDescription] = useState('');
  const [isCreatingAgent, setIsCreatingAgent] = useState(false);
  const router = useRouter();

  useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = async () => {
    try {
      setLoading(true);

      const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/templates`);
      if (!response.ok) {
        throw new Error('Failed to fetch templates');
      }

      const data = await response.json();
      setTemplates(data.templates || []);
      const uniqueCategories = [
        'all',
        ...new Set(
          data.templates.map((template: AgentTemplate) => template.category).filter(Boolean),
        ),
      ];
      setCategories(uniqueCategories);
    } catch (error) {
      console.error('Failed to load templates:', error);
      toast.error('Failed to load templates');
    } finally {
      setLoading(false);
    }
  };

  const filteredTemplates = templates
    .filter((template) => {
      const matchesSearch =
        template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (template.description || '').toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
      return matchesSearch && matchesCategory;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'category':
          return (a.category || '').localeCompare(b.category || '');
        case 'date':
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        default:
          return 0;
      }
    });

  const handleUseTemplate = (template: AgentTemplate) => {
    setSelectedTemplateForCreation(template);
    setNewAgentName(`${template.name} Copy`);
    setNewAgentDescription(template.description || '');
    setIsCreateAgentModalOpen(true);
  };

  const handleCreateAgent = async () => {
    if (!selectedTemplateForCreation || !newAgentName.trim()) {
      toast.error('Agent name is required.');
      return;
    }

    try {
      setIsCreatingAgent(true);
      const agentData = {
        name: newAgentName,
        description: newAgentDescription,
        workflow: selectedTemplateForCreation.workflow,
      };

      const newAgent = await agentService.create(agentData);

      toast.success('Agent created successfully!');
      router.push(`/agents/${newAgent.id}`);
      setIsCreateAgentModalOpen(false);
    } catch (error) {
      console.error('Failed to create agent:', error);
      toast.error('Failed to create agent.');
    } finally {
      setIsCreatingAgent(false);
    }
  };

  const handlePreviewTemplate = (template: AgentTemplate) => {
    setSelectedTemplate(template);
    setIsViewerOpen(true);
  };

  const getCategoryIcon = (categoryId: string) => {
    const category = templateCategories.find((cat) => cat.id === categoryId);
    return category?.icon || Bot;
  };

  const getSupportedPlatforms = (template: AgentTemplate) => {
    const platforms = ['text']; // All templates support text

    if (template.workflow?.speech_config?.stt_vad_threshold) {
      platforms.push('voice');
    }
    if (template.workflow?.speech_config?.tts_voice_id) {
      platforms.push('web');
    }

    return platforms;
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'text':
        return <MessageSquare className="w-3 h-3" />;
      case 'voice':
        return <Phone className="w-3 h-3" />;
      case 'web':
        return <Mic className="w-3 h-3" />;
      default:
        return null;
    }
  };

  return (
    <div className="container mx-auto p-6">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Link href="/agents">
          <Button variant="outline" size="sm">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Agents
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold">Agent Templates</h1>
          <p className="text-gray-600 mt-2">
            Choose from pre-built agent templates to get started quickly
          </p>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col lg:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search templates..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
          <SelectTrigger className="w-full lg:w-48">
            <SelectValue placeholder="Category" />
          </SelectTrigger>
          <SelectContent>
            {categories.map((categoryId) => {
              const categoryData = templateCategories.find((cat) => cat.id === categoryId);
              const Icon = categoryData?.icon || Bot;
              const name =
                categoryData?.name || categoryId.charAt(0).toUpperCase() + categoryId.slice(1);

              return (
                <SelectItem key={categoryId} value={categoryId}>
                  <div className="flex items-center gap-2">
                    <Icon className="w-4 h-4" />
                    {name}
                  </div>
                </SelectItem>
              );
            })}
          </SelectContent>
        </Select>

        <Select value={sortBy} onValueChange={setSortBy}>
          <SelectTrigger className="w-full lg:w-40">
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="name">Name</SelectItem>
            <SelectItem value="category">Category</SelectItem>
            <SelectItem value="date">Date</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Templates Grid */}
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : filteredTemplates.length === 0 ? (
        <Card className="text-center py-12">
          <CardContent>
            <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <Search className="w-12 h-12 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold mb-2">No templates found</h3>
            <p className="text-gray-600 mb-4">Try adjusting your search or filter criteria</p>
            <Button
              variant="outline"
              onClick={() => {
                setSearchTerm('');
                setSelectedCategory('all');
              }}
            >
              Clear Filters
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTemplates.map((template) => {
            const CategoryIcon = getCategoryIcon(template.category || '');
            const supportedPlatforms = getSupportedPlatforms(template);

            return (
              <Card key={template.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-2">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <CategoryIcon className="w-5 h-5 text-blue-600" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{template.name}</CardTitle>
                        <Badge variant="outline" className="mt-1">
                          {templateCategories.find((cat) => cat.id === template.category)?.name ||
                            'General'}
                        </Badge>
                      </div>
                    </div>
                    {template.is_public && (
                      <Badge variant="secondary">
                        <Star className="w-3 h-3 mr-1" />
                        Public
                      </Badge>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <CardDescription className="text-sm">{template.description}</CardDescription>

                    <div className="flex items-center gap-2">
                      <span className="text-xs text-gray-500">Supports:</span>
                      {supportedPlatforms.map((platform) => (
                        <Badge key={platform} variant="outline" className="text-xs">
                          {getPlatformIcon(platform)}
                          <span className="ml-1 capitalize">{platform}</span>
                        </Badge>
                      ))}
                    </div>

                    <div className="flex gap-2 pt-2">
                      <Button className="flex-1" onClick={() => handleUseTemplate(template)}>
                        <Download className="w-4 h-4 mr-2" />
                        Use Template
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePreviewTemplate(template)}
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      {selectedTemplate && (
        <JsonViewer
          jsonData={selectedTemplate}
          isOpen={isViewerOpen}
          onClose={() => setIsViewerOpen(false)}
          title={selectedTemplate.name}
        />
      )}

      {selectedTemplateForCreation && (
        <Dialog open={isCreateAgentModalOpen} onOpenChange={setIsCreateAgentModalOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Create New Agent</DialogTitle>
              <DialogDescription>
                Enter basic information to create your agent from the &apos;
                {selectedTemplateForCreation.name}&apos; template.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="agent-name" className="text-right">
                  Agent Name
                </Label>
                <Input
                  id="agent-name"
                  value={newAgentName}
                  onChange={(e) => setNewAgentName(e.target.value)}
                  className="col-span-3"
                  placeholder="Enter agent name"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="agent-description" className="text-right">
                  Description
                </Label>
                <Textarea
                  id="agent-description"
                  value={newAgentDescription}
                  onChange={(e) => setNewAgentDescription(e.target.value)}
                  className="col-span-3"
                  placeholder="Describe what your agent does"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsCreateAgentModalOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateAgent} disabled={isCreatingAgent}>
                {isCreatingAgent ? 'Creating...' : 'Create Agent'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Stats */}
      <div className="mt-8 text-center text-sm text-gray-500">
        Showing {filteredTemplates.length} of {templates.length} templates
      </div>
    </div>
  );
}
