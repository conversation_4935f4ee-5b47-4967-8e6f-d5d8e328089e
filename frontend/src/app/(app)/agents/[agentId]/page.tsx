// frontend/src/app/(app)/agents/[agentId]/page.tsx
'use client';

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import { agentService } from '@/services/agents/agentService';
import { Workflow as ApiWorkflow, NodeConfig, NodeType, ToolMetadata } from '@/types/workflow';
import { agentBuilderService } from '@/services/agent-builder/agentBuilderService';
import { templateService } from '@/services/templates/templateService';

// Import all extracted components
import AgentHeader from '@/components/features/AgentBuilder/AgentHeader';
import AgentBuilderLayout from '@/components/features/AgentBuilder/AgentBuilderLayout';
import AgentJsonViewers from '@/components/features/AgentBuilder/AgentJsonViewers';
import NodeEditor from '@/components/features/AgentBuilder/NodeEditor';
import TemplateSelectionDialog from '@/components/features/AgentBuilder/TemplateSelectionDialog';


interface BuilderWorkflow {
  id: string;
  name: string;
  description: string;
  nodes: NodeConfig[];
  connections: Record<string, any>;
  agent_type: 'single_node' | 'multi_node' | 'hybrid';
  category: string;
  tags: string[];
  version: string;
  created_at?: string;
  updated_at?: string;
}

interface AgentTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  workflow: BuilderWorkflow;
  preview_image?: string;
  author?: string;
  version: string;
  usage_count: number;
  rating?: number;
}

const initialWorkflowState: BuilderWorkflow = {
  id: `workflow_${Date.now()}`,
  name: 'New Agent Workflow',
  description: 'A new multi-node agent workflow',
  nodes: [],
  connections: {},
  agent_type: 'multi_node',
  category: 'custom',
  tags: [],
  version: '1.0.0',
};

const AgentBuilderPage = () => {
  const router = useRouter();
  const params = useParams();
  const agentId = params.agentId as string;

  // Workflow state
  const [currentWorkflow, setCurrentWorkflow] = useState<BuilderWorkflow>(initialWorkflowState);

  // UI state
  const [selectedNode, setSelectedNode] = useState<NodeConfig | null>(null);
  const [showNodeEditor, setShowNodeEditor] = useState(false);
  const [showTemplateDialog, setShowTemplateDialog] = useState(false);
  const [templateSearchTerm, setTemplateSearchTerm] = useState('');
  const [selectedTemplateCategory, setSelectedTemplateCategory] = useState('all');
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionState, setExecutionState] = useState<any>(null);
  const [activeTab, setActiveTab] = useState('canvas');
  const [showAgentJsonDialog, setShowAgentJsonDialog] = useState(false);
  const [showNodeJsonDialog, setShowNodeJsonDialog] = useState(false);

  // Available node types and tools (fetched from API)
  const [availableNodeTypes, setAvailableNodeTypes] = useState<NodeType[]>([]);
  const [availableToolsMetadata, setAvailableToolsMetadata] = useState<ToolMetadata[]>([]);

  // Templates (fetched from API)
  const [templates, setTemplates] = useState<AgentTemplate[]>([]);

  // Effect for fetching initial agent data, node types, and tool metadata
  useEffect(() => {
    const fetchAgentAndNodeData = async () => {
      try {
        // Fetch agent configuration
        if (agentId && agentId !== 'new') {
          toast.loading('Loading agent configuration...');
          const agent = await agentService.getById(parseInt(agentId, 10));
          const workflowConfigFromApi = agent.workflow_config as ApiWorkflow;

          setCurrentWorkflow({
            ...initialWorkflowState,
            id: agent.id.toString(),
            name: agent.name,
            description: agent.description || workflowConfigFromApi?.description || '',
            nodes: workflowConfigFromApi?.nodes || [],
            connections: workflowConfigFromApi?.connections || {},
          });

          toast.dismiss();
          toast.success('Loaded agent configuration.');
        } else {
          setCurrentWorkflow(initialWorkflowState);
        }

        // Fetch available node types
        const fetchedNodeTypes = await agentBuilderService.getNodeTypes();
        setAvailableNodeTypes(fetchedNodeTypes);

        // Fetch available tools metadata
        const fetchedToolsMetadata = await agentService.getToolMetadata();
        setAvailableToolsMetadata(fetchedToolsMetadata);

      } catch (error) {
        console.error('Failed to fetch initial agent/node data:', error);
        toast.dismiss();
        toast.error('Failed to load agent configuration or node types/tools.');
      }
    };

    fetchAgentAndNodeData();
  }, [agentId]);

  // Effect for fetching templates (separated for clearer dependencies)
  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        const fetchedTemplates = await templateService.getAll({
          search: templateSearchTerm || undefined,
          category: selectedTemplateCategory !== 'all' ? selectedTemplateCategory : undefined,
        });
        setTemplates(fetchedTemplates as AgentTemplate[]);
      } catch (error) {
        console.error('Failed to fetch templates:', error);
        // Optionally show a toast error here if template loading is critical
      }
    };

    fetchTemplates();
  }, [templateSearchTerm, selectedTemplateCategory]);

  // Template categories (can be fetched from backend or hardcoded if static)
  const templateCategories = [
    { id: 'all', name: 'All Templates', icon: 'grid' },
    { id: 'customer-service', name: 'Customer Service', icon: 'headphones' },
    { id: 'sales', name: 'Sales', icon: 'trending-up' },
    { id: 'support', name: 'Support', icon: 'help-circle' },
    { id: 'analysis', name: 'Analysis', icon: 'bar-chart' },
    { id: 'custom', name: 'Custom', icon: 'settings' },
  ];

  const handleWorkflowChange = (workflow: {
    nodes: NodeConfig[];
    connections: Record<string, any>;
  }) => {
    setCurrentWorkflow((prev) => ({
      ...prev,
      nodes: workflow.nodes,
      connections: workflow.connections,
    }));
  };

  const handleNodeSelect = (node: NodeConfig | null) => {
    setSelectedNode(node);
  };

  const handleNodeEdit = (node: NodeConfig) => {
    setSelectedNode(node);
    setShowNodeEditor(true);
  };

  const handleNodeSave = (updatedNode: NodeConfig) => {
    setCurrentWorkflow((prev) => ({
      ...prev,
      nodes: prev.nodes.map((node) => (node.id === updatedNode.id ? updatedNode : node)),
    }));
    setShowNodeEditor(false);
    setSelectedNode(null);
  };

  const handleNodeDelete = (nodeId: string) => {
    setCurrentWorkflow((prev) => ({
      ...prev,
      nodes: prev.nodes.filter((node) => node.id !== nodeId),
      connections: Object.fromEntries(
        Object.entries(prev.connections).filter(([key]) => key !== nodeId),
      ),
    }));
    setSelectedNode(null);
    setShowNodeEditor(false);
  };

  const handleSaveWorkflow = async () => {
    try {
      const apiWorkflow: ApiWorkflow = {
        id: currentWorkflow.id,
        name: currentWorkflow.name,
        description: currentWorkflow.description,
        nodes: currentWorkflow.nodes,
        connections: currentWorkflow.connections,
        active: true, // default value
        start_node_id: currentWorkflow.nodes.length > 0 ? currentWorkflow.nodes[0].id : '', // sensible default
      };

      const agentData = {
        name: currentWorkflow.name,
        description: currentWorkflow.description,
        workflow_config: apiWorkflow,
      };

      if (agentId && agentId !== 'new') {
        await agentService.update(parseInt(agentId, 10), agentData);
        toast.success('Workflow saved successfully!');
      } else {
        const newAgent = await agentService.create(agentData);
        toast.success('Agent created successfully!');
        router.push(`/agents/${newAgent.id}`);
      }
    } catch (error) {
      console.error('Failed to save workflow:', error);
      toast.error('Failed to save workflow');
    }
  };

  const handleTestWorkflow = async () => {
    try {
      setIsExecuting(true);
      // Here you would execute the workflow
      console.log('Testing workflow:', currentWorkflow);

      // Simulate execution
      setTimeout(() => {
        setIsExecuting(false);
        toast.success('Workflow test completed!');
      }, 3000);
    } catch (error) {
      console.error('Failed to test workflow:', error);
      toast.error('Failed to test workflow');
      setIsExecuting(false);
    }
  };

  const handleLoadTemplate = (template: AgentTemplate) => {
    setCurrentWorkflow(template.workflow as BuilderWorkflow);
    setShowTemplateDialog(false);
    setTemplateSearchTerm('');
    setSelectedTemplateCategory('all');
    toast.success(`Loaded template: ${template.name}`);
  };

  // Filter templates based on search and category
  const filteredTemplates = (templates || []).filter((template) => {
    const matchesSearch =
      template.name.toLowerCase().includes(templateSearchTerm.toLowerCase()) ||
      template.description.toLowerCase().includes(templateSearchTerm.toLowerCase());
    const matchesCategory =
      selectedTemplateCategory === 'all' || template.category === selectedTemplateCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <AgentHeader
        agentName={currentWorkflow.name}
        agentDescription={currentWorkflow.description}
        onShowTemplateDialog={() => setShowTemplateDialog(true)}
        onSaveWorkflow={handleSaveWorkflow}
        onTestWorkflow={handleTestWorkflow}
        isExecuting={isExecuting}
        onShowAgentJsonDialog={() => setShowAgentJsonDialog(true)}
      />

      {/* Main Content Layout */}
      <AgentBuilderLayout
        currentWorkflow={currentWorkflow}
        setCurrentWorkflow={setCurrentWorkflow}
        selectedNode={selectedNode}
        setSelectedNode={setSelectedNode}
        showNodeEditor={showNodeEditor}
        setShowNodeEditor={setShowNodeEditor}
        templateSearchTerm={templateSearchTerm}
        selectedTemplateCategory={selectedTemplateCategory}
        isExecuting={isExecuting}
        executionState={executionState}
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        availableNodeTypes={availableNodeTypes}
        availableToolsMetadata={availableToolsMetadata}
        templates={templates}
        handleWorkflowChange={handleWorkflowChange}
        handleNodeSelect={handleNodeSelect}
        handleNodeEdit={handleNodeEdit}
        handleNodeSave={handleNodeSave}
        handleNodeDelete={handleNodeDelete}
        handleLoadTemplate={handleLoadTemplate}
        handleSaveAsTemplate={(templateData) => {
          toast.success('Template saved successfully!');
          // You might want to re-fetch templates here or update state
        }}
      />

      {/* Node Editor Dialog */}
      <Dialog open={showNodeEditor} onOpenChange={setShowNodeEditor}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Node</DialogTitle>
          </DialogHeader>
          {selectedNode && (
            <NodeEditor
              node={selectedNode}
              onSave={handleNodeSave}
              onDelete={handleNodeDelete}
              onClose={() => setShowNodeEditor(false)}
              availableNodeTypes={availableNodeTypes}
              availableToolsMetadata={availableToolsMetadata}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Template Selection Dialog */}
      <TemplateSelectionDialog
        showTemplateDialog={showTemplateDialog}
        setShowTemplateDialog={setShowTemplateDialog}
        templateSearchTerm={templateSearchTerm}
        setTemplateSearchTerm={setTemplateSearchTerm}
        selectedTemplateCategory={selectedTemplateCategory}
        setSelectedTemplateCategory={setSelectedTemplateCategory}
        templateCategories={templateCategories}
        filteredTemplates={filteredTemplates}
        handleLoadTemplate={handleLoadTemplate}
      />

      {/* Agent and Node JSON Viewers */}
      <AgentJsonViewers
        currentWorkflow={currentWorkflow}
        selectedNode={selectedNode}
        showAgentJsonDialog={showAgentJsonDialog}
        setShowAgentJsonDialog={setShowAgentJsonDialog}
        showNodeJsonDialog={showNodeJsonDialog}
        setShowNodeJsonDialog={setShowNodeJsonDialog}
      />
    </div>
  );
};

export default AgentBuilderPage;
