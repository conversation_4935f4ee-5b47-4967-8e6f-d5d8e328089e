'use client';

import React, { useCallback, useState, useEffect, useRef } from 'react';
import {
  applyNodeChanges,
  applyEdgeChanges,
  addEdge,
  Node,
  Edge,
  OnNodesChange,
  OnEdgesChange,
  OnConnect,
  ReactFlowProvider,
  ReactFlowInstance,
  NodeChange,
  NodeSelectionChange,
} from 'reactflow';
import AgentEditorFlow from '@/components/features/agent-editor/AgentEditorFlow';
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '@/components/ui/resizable';
import { transformWorkflowToReactFlow, transformReactFlowToWorkflow } from '@/lib/agent-utils';
import { Workflow } from '@/types'; // Import Workflow directly from @/types
import PropertiesPanel from '@/components/features/agent-editor/PropertiesPanel';
import JsonViewPanel from '@/components/features/agent-editor/JsonViewPanel';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { agentService, agentTemplateService } from '@/services/agents/agentService';
import NodesSidebar from '@/components/features/agent-editor/NodesSidebar';
import 'reactflow/dist/style.css';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { ArrowLeft, Save, Play, Code } from 'lucide-react';
import { toast } from 'react-hot-toast';

let id = 0;
const getId = () => `dndnode_${id++}`;

// Placeholder for components that are not available
const PlaceholderComponent = ({ name }: { name: string }) => (
  <div className="flex items-center justify-center h-full">
    <div className="text-center text-gray-500">
      <p className="text-lg font-semibold">{name}</p>
      <p>This component is not yet implemented.</p>
    </div>
  </div>
);

const ChatInterface = () => <PlaceholderComponent name="Chat Interface" />;
const TemplateManager = () => <PlaceholderComponent name="Template Manager" />;
const IntegrationTesting = () => <PlaceholderComponent name="Integration Testing" />;
const AgentAnalytics = () => <PlaceholderComponent name="Agent Analytics" />;
const AgentDeployment = () => <PlaceholderComponent name="Agent Deployment" />;

const AgentEditorPage = () => {
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const [nodes, setNodes] = useState<Node[]>([]);
  const [edges, setEdges] = useState<Edge[]>([]);
  const [selectedNode, setSelectedNode] = useState<Node | undefined>(undefined);
  const [workflow, setWorkflow] = useState<Workflow | undefined>(undefined);
  const [isJsonViewOpen, setIsJsonViewOpen] = useState(false);
  const [reactFlowInstance, setReactFlowInstance] = useState<ReactFlowInstance | undefined>(
    undefined,
  );
  const [activeTab, setActiveTab] = useState('canvas');

  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const agentId = params.agentId as string;
  const templateId = searchParams.get('template');

  useEffect(() => {
    const fetchAgent = async () => {
      if (agentId && agentId !== 'new') {
        try {
          const agent = await agentService.getById(parseInt(agentId, 10));
          const workflowData = (agent.workflow_config as Workflow) || {
            id: agent.id.toString(),
            name: agent.name,
            description: agent.description || '',
            nodes: [],
            connections: {},
            start_node_id: '',
            active: agent.is_active,
          };
          workflowData.name = agent.name;
          workflowData.description = agent.description || '';
          const { nodes: loadedNodes, edges: loadedEdges } =
            transformWorkflowToReactFlow(workflowData);
          setNodes(loadedNodes);
          setEdges(loadedEdges);
          setWorkflow(workflowData);
        } catch (error) {
          console.error('Failed to fetch agent:', error);
        }
      } else if (templateId) {
        try {
          const template = await agentTemplateService.getById(parseInt(templateId, 10));
          const workflowData = (template.template_config as Workflow) || {
            id: 'new',
            name: `New Agent from ${template.name}`,
            description: template.description || '',
            nodes: [],
            connections: {},
            start_node_id: '',
            active: false,
          };
          workflowData.name = `New Agent from ${template.name}`;
          workflowData.description = template.description || '';
          const { nodes: loadedNodes, edges: loadedEdges } =
            transformWorkflowToReactFlow(workflowData);
          setNodes(loadedNodes);
          setEdges(loadedEdges);
          setWorkflow(workflowData);
        } catch (error) {
          console.error('Failed to fetch agent template:', error);
        }
      } else {
        const emptyWorkflow: Workflow = {
          id: 'new',
          name: 'New Agent',
          description: 'A new agent',
          nodes: [],
          connections: {},
          start_node_id: '',
          active: false,
        };
        const { nodes: initialNodes, edges: initialEdges } =
          transformWorkflowToReactFlow(emptyWorkflow);
        setNodes(initialNodes);
        setEdges(initialEdges);
        setWorkflow(emptyWorkflow);
      }
    };

    fetchAgent();
  }, [agentId, templateId]);

  const onWorkflowChange = (newData: Partial<Workflow>) => {
    if (workflow) {
      setWorkflow((prev: Workflow | undefined) => (prev ? { ...prev, ...newData } : undefined));
    }
  };

  const onNodesChange: OnNodesChange = useCallback(
    (changes: NodeChange[]) => {
      const nextNodes = applyNodeChanges(changes, nodes);
      const selectedNodeChange = changes.find(
        (change): change is NodeSelectionChange => change.type === 'select' && change.selected,
      );
      if (selectedNodeChange) {
        const nodeId = selectedNodeChange.id;
        setSelectedNode(nextNodes.find((n) => n.id === nodeId) || undefined);
      } else if (changes.some((c) => c.type === 'select' && !c.selected)) {
        setSelectedNode(undefined);
      }
      setNodes(nextNodes);
    },
    [nodes],
  );

  const onEdgesChange: OnEdgesChange = useCallback(
    (changes) => setEdges((eds) => applyEdgeChanges(changes, eds)),
    [setEdges],
  );

  const onConnect: OnConnect = useCallback(
    (connection) => setEdges((eds) => addEdge(connection, eds)),
    [setEdges],
  );

  const onNodeDataChange = (nodeId: string, data: Record<string, unknown>) => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === nodeId) {
          const newData = { ...node.data, ...data };
          if (data.name) {
            newData.label = data.name as string;
          }
          return { ...node, data: newData };
        }
        return node;
      }),
    );
  };

  const getFullWorkflowJson = () => {
    if (!workflow) return {};
    return {
      workflow: transformReactFlowToWorkflow(nodes, edges, workflow),
    };
  };

  const onSave = async () => {
    if (!workflow) return;

    const updatedWorkflow = getFullWorkflowJson().workflow;

    if (!updatedWorkflow) {
      console.error('Workflow is undefined');
      return;
    }

    const agentData = {
      name: workflow.name,
      description: workflow.description || undefined,
      workflow_config: updatedWorkflow,
    };

    try {
      if (agentId === 'new') {
        const newAgent = await agentService.create(agentData);
        toast.success('Agent created successfully!');
        router.push(`/agents/${newAgent.id}`);
      } else {
        await agentService.update(parseInt(agentId, 10), agentData);
        toast.success('Agent saved successfully!');
      }
    } catch (error) {
      console.error('Failed to save agent:', error);
      toast.error('Failed to save agent.');
    }
  };

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();

      if (!reactFlowInstance || !reactFlowWrapper.current) {
        return;
      }

      const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();
      const type = event.dataTransfer.getData('application/reactflow');

      if (typeof type === 'undefined' || !type) {
        return;
      }

      const position = reactFlowInstance.project({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      });
      const newNode: Node = {
        id: getId(),
        type,
        position,
        data: { label: `${type} node` },
      };

      setNodes((nds) => nds.concat(newNode));
    },
    [reactFlowInstance],
  );

  if (!workflow) {
    return <div>Loading...</div>;
  }

  return (
    <div className="h-full w-full flex flex-col">
      <div className="border-b bg-white px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" onClick={() => router.push('/agents')}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-2xl font-bold">{workflow.name}</h1>
              <p className="text-sm text-gray-600">{workflow.description}</p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={onSave}>
              <Save className="w-4 h-4 mr-2" />
              Save
            </Button>
            <Button onClick={() => toast.info('Test functionality not implemented yet.')}>
              <Play className="w-4 h-4 mr-2" />
              Test
            </Button>
            <Button variant="outline" onClick={() => setIsJsonViewOpen(true)}>
              <Code className="w-4 h-4 mr-2" />
              View JSON
            </Button>
          </div>
        </div>
      </div>

      <div className="flex-1 overflow-hidden">
        <ResizablePanelGroup direction="horizontal" className="h-full">
          <ResizablePanel
            defaultSize={15}
            minSize={10}
            className="border-r bg-gray-50 overflow-hidden"
          >
            <NodesSidebar />
          </ResizablePanel>
          <ResizableHandle />
          <ResizablePanel defaultSize={65} minSize={40} className="flex flex-col overflow-hidden">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
              <div className="border-b px-4">
                <TabsList>
                  <TabsTrigger value="canvas">Canvas</TabsTrigger>
                  <TabsTrigger value="config">Configuration</TabsTrigger>
                  <TabsTrigger value="chat">Chat Test</TabsTrigger>
                  <TabsTrigger value="templates">Templates</TabsTrigger>
                  <TabsTrigger value="testing">Test</TabsTrigger>
                  <TabsTrigger value="debug">Debug</TabsTrigger>
                  <TabsTrigger value="analytics">Analytics</TabsTrigger>
                  <TabsTrigger value="deployment">Deployment</TabsTrigger>
                </TabsList>
              </div>

              <TabsContent value="canvas" className="flex-1 m-0">
                <div className="h-full w-full" ref={reactFlowWrapper}>
                  <AgentEditorFlow
                    nodes={nodes}
                    edges={edges}
                    onNodesChange={onNodesChange}
                    onEdgesChange={onEdgesChange}
                    onConnect={onConnect}
                    onInit={setReactFlowInstance}
                    onDrop={onDrop}
                    onDragOver={onDragOver}
                  />
                </div>
              </TabsContent>

              <TabsContent value="config" className="flex-1 p-4 overflow-y-auto">
                <div className="max-w-4xl mx-auto space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Workflow Configuration</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <Label>Name</Label>
                        <Input
                          value={workflow.name}
                          onChange={(e) => onWorkflowChange({ name: e.target.value })}
                        />
                      </div>
                      <div>
                        <Label>Description</Label>
                        <Textarea
                          value={workflow.description}
                          onChange={(e) => onWorkflowChange({ description: e.target.value })}
                          rows={3}
                        />
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="chat" className="flex-1 m-0">
                <ChatInterface />
              </TabsContent>
              <TabsContent value="templates" className="flex-1 m-0 p-4">
                <TemplateManager />
              </TabsContent>
              <TabsContent value="testing" className="flex-1 m-0 p-4">
                <IntegrationTesting />
              </TabsContent>
              <TabsContent value="debug" className="flex-1 m-0 p-4">
                <PlaceholderComponent name="Debug Console" />
              </TabsContent>
              <TabsContent value="analytics" className="flex-1 m-0 p-4">
                <AgentAnalytics />
              </TabsContent>
              <TabsContent value="deployment" className="flex-1 m-0 p-4">
                <AgentDeployment />
              </TabsContent>
            </Tabs>
          </ResizablePanel>
          <ResizableHandle />
          <ResizablePanel
            defaultSize={20}
            minSize={15}
            className="border-l bg-white overflow-hidden"
          >
            <PropertiesPanel
              selectedNode={selectedNode}
              onNodeDataChange={onNodeDataChange}
              workflow={workflow}
              onWorkflowChange={onWorkflowChange}
            />
          </ResizablePanel>
        </ResizablePanelGroup>
      </div>

      <Dialog open={isJsonViewOpen} onOpenChange={setIsJsonViewOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Agent Configuration JSON</DialogTitle>
          </DialogHeader>
          <JsonViewPanel
            jsonData={getFullWorkflowJson()}
            onClose={() => setIsJsonViewOpen(false)}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};

const AgentEditorPageWrapper = () => (
  <ReactFlowProvider>
    <AgentEditorPage />
  </ReactFlowProvider>
);

export default AgentEditorPageWrapper;
