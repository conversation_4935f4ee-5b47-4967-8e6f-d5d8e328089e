'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import {
  ArrowLeft,
  Save,
  Sparkles,
  Bot,
  Wand2,
  Settings,
  MessageSquare,
  Mic,
  Phone,
} from 'lucide-react';
import Link from 'next/link';
import { toast } from 'react-hot-toast';
import { agentService, agentTemplateService, AgentCreate } from '@/services/agents/agentService';

export default function NewAgentPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [creationMethod, setCreationMethod] = useState<'scratch' | 'template'>('scratch');

  const [agentData, setAgentData] = useState<AgentCreate>({
    name: '',
    description: '',
    system_prompt: '',
    first_message: '',
    is_active: false,
    speech_config: {
      stt_provider: 'openai',
      tts_provider: 'openai',
      language: 'en-US',
      voice_settings: {
        speed: 1.0,
        pitch: 1.0,
        stability: 0.5,
        similarity_boost: 0.5,
      },
    },
    conversation_config: {
      max_turns: 50,
      timeout_seconds: 300,
      enable_interruptions: true,
      conversation_memory: 'medium',
      context_window: 4000,
    },
    global_tools: [],
  });

  const handleInputChange = (field: keyof AgentCreate, value: any) => {
    setAgentData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSpeechConfigChange = (field: string, value: any) => {
    setAgentData((prev) => ({
      ...prev,
      speech_config: {
        ...prev.speech_config,
        [field]: value,
      },
    }));
  };

  const handleConversationConfigChange = (field: string, value: any) => {
    setAgentData((prev) => ({
      ...prev,
      conversation_config: {
        ...prev.conversation_config,
        [field]: value,
      },
    }));
  };

  const handleCreateAgent = async () => {
    if (!agentData.name.trim()) {
      toast.error('Agent name is required');
      return;
    }

    try {
      setLoading(true);
      // Create a simple default agent with minimal configuration
      const defaultAgent = {
        name: agentData.name,
        description: agentData.description || 'A new agent created with the agent builder',
        is_active: false,
      };

      const newAgent = await agentService.create(defaultAgent);
      toast.success('Agent created successfully! Opening agent builder...');
      // Redirect to the agent builder instead of the agent detail page
      router.push(`/agents/builder?id=${newAgent.id}`);
    } catch (error) {
      console.error('Failed to create agent:', error);
      toast.error('Failed to create agent');
    } finally {
      setLoading(false);
    }
  };

  const handleUseTemplate = () => {
    router.push('/agents/templates');
  };

  const generateWithAI = async () => {
    if (!agentData.name.trim()) {
      toast.error('Please enter an agent name first');
      return;
    }

    // Mock AI generation - in real implementation, this would call an AI service
    const suggestions = {
      description: `An AI assistant specialized in ${agentData.name.toLowerCase()} tasks, designed to provide helpful and accurate information.`,
      system_prompt: `You are ${agentData.name}, a helpful and knowledgeable AI assistant. You should be professional, friendly, and provide accurate information. Always aim to be helpful while maintaining appropriate boundaries.`,
      first_message: `Hello! I'm ${agentData.name}, your AI assistant. How can I help you today?`,
    };

    setAgentData((prev) => ({
      ...prev,
      ...suggestions,
    }));

    toast.success('AI suggestions applied!');
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Link href="/agents">
          <Button variant="outline" size="sm">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Agents
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold">Create New Agent</h1>
          <p className="text-gray-600 mt-2">Build a custom AI agent for your specific needs</p>
        </div>
      </div>

      {/* Creation Method Selection */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>How would you like to create your agent?</CardTitle>
          <CardDescription>Choose your preferred method to get started</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card
              className={`cursor-pointer transition-all ${creationMethod === 'scratch' ? 'ring-2 ring-blue-500 bg-blue-50' : 'hover:shadow-md'}`}
              onClick={() => setCreationMethod('scratch')}
            >
              <CardContent className="p-6 text-center">
                <Bot className="w-12 h-12 mx-auto mb-4 text-blue-600" />
                <h3 className="font-semibold mb-2">Start from Scratch</h3>
                <p className="text-sm text-gray-600">
                  Build your agent from the ground up with full customization
                </p>
              </CardContent>
            </Card>

            <Card
              className={`cursor-pointer transition-all ${creationMethod === 'template' ? 'ring-2 ring-blue-500 bg-blue-50' : 'hover:shadow-md'}`}
              onClick={() => setCreationMethod('template')}
            >
              <CardContent className="p-6 text-center">
                <Sparkles className="w-12 h-12 mx-auto mb-4 text-purple-600" />
                <h3 className="font-semibold mb-2">Use a Template</h3>
                <p className="text-sm text-gray-600">
                  Start with a pre-built template and customize as needed
                </p>
              </CardContent>
            </Card>
          </div>

          {creationMethod === 'template' && (
            <div className="mt-4 text-center">
              <Button onClick={handleUseTemplate}>
                <Sparkles className="w-4 h-4 mr-2" />
                Browse Templates
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Simple Agent Creation */}
      {creationMethod === 'scratch' && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bot className="w-5 h-5" />
              Create New Agent
            </CardTitle>
            <CardDescription>Enter basic information to create your agent</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Agent Name *</Label>
              <Input
                id="name"
                value={agentData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Enter agent name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={agentData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Describe what your agent does"
                rows={3}
              />
            </div>

            <div className="flex justify-end pt-4">
              <Button onClick={handleCreateAgent} disabled={loading || !agentData.name.trim()}>
                {loading ? 'Creating...' : 'Create Agent'}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
