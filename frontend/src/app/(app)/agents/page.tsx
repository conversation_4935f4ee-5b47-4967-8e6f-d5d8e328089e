'use client';

import React, { useEffect, useState } from 'react';
import { agentService, Agent } from '@/services/agents/agentService';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Plus, Settings, Play, Trash2, Copy, MoreVertical } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import Link from 'next/link';
import { toast } from 'react-hot-toast';
import { useRouter } from 'next/navigation';

const AgentsPage = () => {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    loadAgents();
  }, []);

  const loadAgents = async () => {
    try {
      setLoading(true);
      const fetchedAgents = await agentService.getAll();
      setAgents(fetchedAgents);
    } catch (error) {
      console.error('Failed to fetch agents:', error);
      toast.error('Failed to load agents');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateAgent = () => {
    router.push('/agents/new');
  };

  const handleEditAgent = (agentId: number) => {
    router.push(`/agents/${agentId}`);
  };

  const handleDeleteAgent = async (agentId: number) => {
    if (!confirm('Are you sure you want to delete this agent?')) return;

    try {
      await agentService.delete(agentId);
      setAgents(agents.filter((agent) => agent.id !== agentId));
      toast.success('Agent deleted successfully');
    } catch (error) {
      console.error('Failed to delete agent:', error);
      toast.error('Failed to delete agent');
    }
  };

  const handleDuplicateAgent = async (agent: Agent) => {
    try {
      const duplicatedAgent = await agentService.create({
        name: `${agent.name} (Copy)`,
        description: agent.description,
        system_prompt: agent.system_prompt,
        first_message: agent.first_message,
        workflow_config: agent.workflow_config,
        speech_config: agent.speech_config,
        conversation_config: agent.conversation_config,
        global_tools: agent.global_tools,
        is_active: false,
      });
      setAgents([...agents, duplicatedAgent]);
      toast.success('Agent duplicated successfully');
    } catch (error) {
      console.error('Failed to duplicate agent:', error);
      toast.error('Failed to duplicate agent');
    }
  };

  const handleTestAgent = (agentId: number) => {
    window.open(`/node?agent=${agentId}`, '_blank');
  };

  const toggleAgentStatus = async (agent: Agent) => {
    try {
      const updatedAgent = await agentService.update(agent.id, {
        is_active: !agent.is_active,
      });
      setAgents(agents.map((a) => (a.id === agent.id ? updatedAgent : a)));
      toast.success(`Agent ${updatedAgent.is_active ? 'activated' : 'deactivated'}`);
    } catch (error) {
      console.error('Failed to update agent status:', error);
      toast.error('Failed to update agent status');
    }
  };

  const getStatusColor = (isActive: boolean) => {
    return isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800';
  };

  const getStatusText = (isActive: boolean) => {
    return isActive ? 'Active' : 'Inactive';
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Agents</h1>
          <p className="text-gray-600 mt-2">Manage your AI agents and their configurations</p>
        </div>
        <div className="flex gap-2">
          <Link href="/agents/templates">
            <Button variant="outline">Browse Templates</Button>
          </Link>
          <Button onClick={handleCreateAgent}>
            <Plus className="w-4 h-4 mr-2" />
            Create Agent
          </Button>
        </div>
      </div>

      {agents.length === 0 ? (
        <Card className="text-center py-12">
          <CardContent>
            <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <Settings className="w-12 h-12 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold mb-2">No agents yet</h3>
            <p className="text-gray-600 mb-4">Create your first AI agent to get started</p>
            <Button onClick={handleCreateAgent}>
              <Plus className="w-4 h-4 mr-2" />
              Create Your First Agent
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {agents.map((agent) => (
            <Card key={agent.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <CardTitle className="text-lg">{agent.name}</CardTitle>
                    <CardDescription className="mt-1">
                      {agent.description || 'No description provided'}
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={getStatusColor(agent.is_active)}>
                      {getStatusText(agent.is_active)}
                    </Badge>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreVertical className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleDuplicateAgent(agent)}>
                          <Copy className="w-4 h-4 mr-2" />
                          Duplicate
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => toggleAgentStatus(agent)}>
                          {agent.is_active ? 'Deactivate' : 'Activate'}
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleDeleteAgent(agent.id)}
                          className="text-red-600"
                        >
                          <Trash2 className="w-4 h-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between text-sm text-gray-600">
                    <span>Created:</span>
                    <span>{new Date(agent.created_at).toLocaleDateString()}</span>
                  </div>
                  <div className="flex justify-between text-sm text-gray-600">
                    <span>Last modified:</span>
                    <span>{new Date(agent.updated_at).toLocaleDateString()}</span>
                  </div>
                  <div className="flex gap-2 pt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1"
                      onClick={() => handleEditAgent(agent.id)}
                    >
                      <Settings className="w-4 h-4 mr-2" />
                      Configure
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => handleTestAgent(agent.id)}>
                      <Play className="w-4 h-4 mr-2" />
                      Test
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default AgentsPage;
