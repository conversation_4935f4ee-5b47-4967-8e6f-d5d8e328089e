'use client';

import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Plus, Edit, Trash2 } from 'lucide-react';
import { phoneAPI } from '@/lib/api';
import { formatDateTime } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';

interface PhoneNumber {
  id: number;
  phone_number: string;
  friendly_name: string | undefined;
  provider: string;
  account_sid: string;
  customer_id: number;
  capabilities: string[];
  created_at: string;
  updated_at: string;
}

interface NumberFormProps {
  number?: PhoneNumber;
  onSave: () => void;
}

const NumberForm = ({ number, onSave }: NumberFormProps) => {
  const [formData, setFormData] = useState({
    phone_number: number?.phone_number || '',
    friendly_name: number?.friendly_name || '',
    provider: number?.provider || 'twilio',
    account_sid: number?.account_sid || '',
    customer_id: number?.customer_id || 1, // Default to 1 for now
  });

  const queryClient = useQueryClient();

  const mutation = useMutation<PhoneNumber, Error, PhoneNumber | Partial<PhoneNumber>>({
    mutationFn: (data: PhoneNumber | Partial<PhoneNumber>) =>
      (number ? phoneAPI.updateNumber(number.id, data) : phoneAPI.createNumber(data)).then(
        (res) => res.data,
      ),
    onSuccess: () => {
      toast.success(`Number ${number ? 'updated' : 'added'} successfully.`);
      queryClient.invalidateQueries({ queryKey: ['phoneNumbers'] });
      onSave();
    },
    onError: (error: Error) => {
      console.error('Failed to save number:', error);
      toast.error('Failed to save number.');
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    mutation.mutate(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="phone_number">Phone Number</Label>
        <Input
          id="phone_number"
          value={formData.phone_number}
          onChange={(e) => setFormData({ ...formData, phone_number: e.target.value })}
          required
        />
      </div>
      <div>
        <Label htmlFor="friendly_name">Friendly Name</Label>
        <Input
          id="friendly_name"
          value={formData.friendly_name}
          onChange={(e) => setFormData({ ...formData, friendly_name: e.target.value })}
        />
      </div>
      <div>
        <Label htmlFor="provider">Provider</Label>
        <Input
          id="provider"
          value={formData.provider}
          onChange={(e) => setFormData({ ...formData, provider: e.target.value })}
          required
        />
      </div>
      <div>
        <Label htmlFor="account_sid">Account SID</Label>
        <Input
          id="account_sid"
          value={formData.account_sid}
          onChange={(e) => setFormData({ ...formData, account_sid: e.target.value })}
          required
        />
      </div>
      <Button type="submit" disabled={mutation.isPending}>
        {mutation.isPending ? 'Saving...' : 'Save'}
      </Button>
    </form>
  );
};

const NumbersPage = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedNumber, setSelectedNumber] = useState<PhoneNumber | undefined>(undefined);
  const queryClient = useQueryClient();

  const { data: numbers, isLoading } = useQuery<PhoneNumber[]>({
    queryKey: ['phoneNumbers'],
    queryFn: () => phoneAPI.getNumbers().then((res) => res.data),
  });

  const deleteMutation = useMutation<void, Error, number>({
    mutationFn: async (id: number) => {
      await phoneAPI.deleteNumber(id);
    },
    onSuccess: () => {
      toast.success('Number deleted successfully.');
      queryClient.invalidateQueries({ queryKey: ['phoneNumbers'] });
    },
    onError: () => {
      toast.error('Failed to delete number.');
    },
  });

  const handleEdit = (number: PhoneNumber) => {
    setSelectedNumber(number);
    setIsModalOpen(true);
  };

  const handleAddNew = () => {
    setSelectedNumber(undefined);
    setIsModalOpen(true);
  };

  return (
    <div className="p-4 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Phone Numbers</h1>
        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
          <DialogTrigger asChild>
            <Button onClick={handleAddNew}>
              <Plus className="mr-2 h-4 w-4" /> Add New
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{selectedNumber ? 'Edit Number' : 'Add New Number'}</DialogTitle>
            </DialogHeader>
            <NumberForm
              number={selectedNumber as PhoneNumber}
              onSave={() => setIsModalOpen(false)}
            />
          </DialogContent>
        </Dialog>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Your Phone Numbers</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <p>Loading...</p>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Phone Number</TableHead>
                  <TableHead>Friendly Name</TableHead>
                  <TableHead>Added On</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {numbers?.map((number: PhoneNumber) => (
                  <TableRow key={number.id}>
                    <TableCell>{number.phone_number}</TableCell>
                    <TableCell>{number.friendly_name}</TableCell>
                    <TableCell>{formatDateTime(number.created_at)}</TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="icon" onClick={() => handleEdit(number)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="destructive"
                          size="icon"
                          onClick={() => deleteMutation.mutate(number.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default NumbersPage;
