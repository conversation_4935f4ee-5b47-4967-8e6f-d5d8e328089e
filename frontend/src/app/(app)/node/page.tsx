'use client';

import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import {
  Settings,
  MessageSquare,
  BarChart3,
  Wrench,
  Brain,
  Database,
  Zap,
  Play,
  Square,
  RefreshCw,
  Code,
  Eye,
  Phone,
  Mic,
  PhoneOff,
  Plus,
  Copy,
  Download,
  Upload,
  Globe,
} from 'lucide-react';

import { testCenterAPI, callAPI } from '@/lib/api';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import JsonViewerModal from '@/components/ui/JsonViewerModal';
import NodeTester, { NodeConfig } from '@/components/features/NodeTester/NodeTester';

export default function TestCenterPage() {
  const queryClient = useQueryClient();

  // Test center state
  const [activeTab, setActiveTab] = useState('agent-config');
  const [showJsonModal, setShowJsonModal] = useState(false);
  const [jsonContent, setJsonContent] = useState('');
  const [isTestingAgent, setIsTestingAgent] = useState(false);

  // Call state
  const [isCallActive, setIsCallActive] = useState(false);
  const [callId, setCallId] = useState<string | null>(null);
  const [callStatus, setCallStatus] = useState<'idle' | 'connecting' | 'connected' | 'ended'>(
    'idle',
  );
  const [transcript, setTranscript] = useState<string>('');
  const [conversationHistory, setConversationHistory] = useState<any[]>([]);

  // Workflow state
  const [currentWorkflow, setCurrentWorkflow] = useState<any>({
    name: 'Test Agent',
    description: 'Advanced agent for comprehensive testing and experimentation',
    active: true,
    id: 'test_agent_001',
    nodes: [
      {
        id: 'main_llm',
        name: 'Main LLM Node',
        type: 'llm',
        dependencies: ['llm_service'],
        parameters: {
          model: 'gpt-4',
          temperature: 0.7,
          max_tokens: 1000,
        },
        system_prompt:
          'You are a helpful AI assistant. Provide helpful, accurate, and engaging responses to user queries. Be conversational yet informative.',
        description: 'Primary language model for intelligent conversation',
      },
      {
        id: 'memory_node',
        name: 'Memory Manager',
        type: 'memory',
        dependencies: ['memory_service'],
        parameters: {},
        memory_config: {
          type: 'short_term',
          max_entries: 50,
        },
        description: 'Manages conversation context and memory',
      },
    ],
    connections: {
      main_llm: {
        main: [{ node: 'memory_node', type: 'main', index: 0 }],
      },
      memory_node: {
        main: [{ node: 'END', type: 'main', index: 0 }],
      },
    },
    start_node_id: 'main_llm',
    first_message:
      "Hello! I'm your AI assistant. I'm here to help you with any questions or tasks you might have. How can I assist you today?",
    conversation_settings: {
      silence_timeout_seconds: 30,
      max_turns: 100,
      enable_interruption: true,
    },
  });

  // Tool configuration state
  const [tools, setTools] = useState<any[]>([
    {
      id: 'web_search_001',
      name: 'Web Search',
      description: 'Search the web for current information',
      category: 'web',
      enabled: true,
      parameters: [
        { name: 'query', type: 'string', description: 'Search query', required: true },
        {
          name: 'num_results',
          type: 'number',
          description: 'Number of results',
          required: false,
          default: 5,
        },
      ],
      endpoint: 'https://api.example.com/search',
      method: 'POST',
    },
    {
      id: 'calculator_001',
      name: 'Calculator',
      description: 'Perform mathematical calculations',
      category: 'utility',
      enabled: true,
      parameters: [
        {
          name: 'expression',
          type: 'string',
          description: 'Mathematical expression to evaluate',
          required: true,
        },
      ],
    },
  ]);

  // Handlers
  const handleWorkflowChange = (workflow: any) => {
    setCurrentWorkflow(workflow);
    toast.success('Agent configuration updated');
  };

  const handleToolsChange = (newTools: any[]) => {
    setTools(newTools);
    toast.success('Tool configuration updated');
  };

  const handleViewJson = (json: string) => {
    setJsonContent(json);
    setShowJsonModal(true);
  };

  const handleTestAgent = async () => {
    setIsTestingAgent(true);
    try {
      // Validate agent configuration
      if (!currentWorkflow.nodes.length) {
        throw new Error('Agent must have at least one node');
      }
      if (!currentWorkflow.start_node_id) {
        throw new Error('Agent must have a start node');
      }

      // Simulate agent testing with API call
      await new Promise((resolve) => setTimeout(resolve, 2000));

      toast.success('Agent configuration validated successfully!');
      setActiveTab('chat-test');
    } catch (error: any) {
      toast.error(`Agent test failed: ${error.message}`);
    } finally {
      setIsTestingAgent(false);
    }
  };

  const handleStartCall = async (type: 'phone' | 'web' | 'text') => {
    try {
      setCallStatus('connecting');
      setIsCallActive(true);

      // Simulate call initiation
      const mockCallId = `call_${Date.now()}`;
      setCallId(mockCallId);

      // Simulate connection delay
      await new Promise((resolve) => setTimeout(resolve, 1500));

      setCallStatus('connected');
      toast.success(`${type} call connected successfully!`);

      // Add initial message to conversation
      setConversationHistory([
        {
          id: '1',
          type: 'agent',
          content: currentWorkflow.first_message,
          timestamp: new Date().toISOString(),
        },
      ]);
    } catch (error) {
      toast.error('Failed to start call');
      setCallStatus('idle');
      setIsCallActive(false);
    }
  };

  const handleEndCall = () => {
    setCallStatus('ended');
    setIsCallActive(false);
    setCallId(null);
    toast.success('Call ended');
  };

  const handleSendMessage = (message: string) => {
    if (!message.trim()) return;

    // Add user message
    const userMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: message,
      timestamp: new Date().toISOString(),
    };

    setConversationHistory((prev) => [...prev, userMessage]);

    // Simulate agent response
    setTimeout(() => {
      const agentMessage = {
        id: (Date.now() + 1).toString(),
        type: 'agent',
        content: `I understand you said: "${message}". This is a simulated response from your configured agent. In a real implementation, this would be processed through your agent workflow.`,
        timestamp: new Date().toISOString(),
      };
      setConversationHistory((prev) => [...prev, agentMessage]);
    }, 1000);
  };

  return (
    <div className="space-y-6">
      {/* JSON Viewer Modal */}
      <JsonViewerModal
        isOpen={showJsonModal}
        onClose={() => setShowJsonModal(false)}
        jsonString={jsonContent}
      />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Test Center</h1>
          <p className="text-gray-600">
            Configure, test, and refine your AI agents with advanced tools
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-sm">
            <Brain className="w-3 h-3 mr-1" />
            {currentWorkflow.nodes.length} Nodes
          </Badge>
          <Badge variant="outline" className="text-sm">
            <Tool className="w-3 h-3 mr-1" />
            {tools.filter((t) => t.enabled).length} Tools
          </Badge>
          <Badge variant={currentWorkflow.active ? 'default' : 'secondary'} className="text-sm">
            {currentWorkflow.active ? 'Active' : 'Inactive'}
          </Badge>
        </div>
      </div>

      {/* Tabs Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="agent-config" className="flex items-center gap-2">
            <Settings className="w-4 h-4" />
            Agent Configuration
          </TabsTrigger>
          <TabsTrigger value="tools" className="flex items-center gap-2">
            <Tool className="w-4 h-4" />
            Tools ({tools.length})
          </TabsTrigger>
          <TabsTrigger value="chat-test" className="flex items-center gap-2">
            <MessageSquare className="w-4 h-4" />
            Live Testing
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            Analytics
          </TabsTrigger>
        </TabsList>

        {/* Agent Configuration Tab */}
        <TabsContent value="agent-config" className="space-y-4">
          <AgentConfig
            workflow={currentWorkflow}
            onWorkflowChange={handleWorkflowChange}
            onViewJson={handleViewJson}
            onTestAgent={handleTestAgent}
            isTestingAgent={isTestingAgent}
          />
        </TabsContent>

        {/* Tools Configuration Tab */}
        <TabsContent value="tools" className="space-y-4">
          <ToolManager
            tools={tools}
            onToolsChange={handleToolsChange}
            onViewJson={handleViewJson}
          />
        </TabsContent>

        {/* Live Testing Tab */}
        <TabsContent value="chat-test" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Test Controls */}
            <div className="lg:col-span-1 space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MessageSquare className="w-5 h-5" />
                    Test Controls
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Button
                      onClick={() => handleStartCall('text')}
                      disabled={isCallActive}
                      className="w-full"
                    >
                      {isCallActive ? (
                        <>
                          <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                          Connected
                        </>
                      ) : (
                        <>
                          <MessageSquare className="w-4 h-4 mr-2" />
                          Start Text Chat
                        </>
                      )}
                    </Button>

                    <Button
                      onClick={() => handleStartCall('web')}
                      disabled={isCallActive}
                      variant="outline"
                      className="w-full"
                    >
                      <Mic className="w-4 h-4 mr-2" />
                      Start Web Call
                    </Button>

                    <Button
                      onClick={() => handleStartCall('phone')}
                      disabled={isCallActive}
                      variant="outline"
                      className="w-full"
                    >
                      <Phone className="w-4 h-4 mr-2" />
                      Start Phone Call
                    </Button>
                  </div>

                  {isCallActive && (
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Status:</span>
                        <Badge variant={callStatus === 'connected' ? 'default' : 'secondary'}>
                          {callStatus}
                        </Badge>
                      </div>
                      <Button
                        onClick={handleEndCall}
                        variant="destructive"
                        size="sm"
                        className="w-full"
                      >
                        <PhoneOff className="w-4 h-4 mr-2" />
                        End Call
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Agent Status</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Agent:</span>
                      <span className="font-medium">{currentWorkflow.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Nodes:</span>
                      <span>{currentWorkflow.nodes.length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Tools:</span>
                      <span>{tools.filter((t) => t.enabled).length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Status:</span>
                      <Badge
                        variant={currentWorkflow.active ? 'default' : 'secondary'}
                        className="text-xs"
                      >
                        {currentWorkflow.active ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Chat Interface */}
            <div className="lg:col-span-2">
              <Card className="h-[600px] flex flex-col">
                <CardHeader>
                  <CardTitle>Live Chat Interface</CardTitle>
                </CardHeader>
                <CardContent className="flex-1 flex flex-col">
                  <div className="flex-1 border rounded-lg p-4 mb-4 overflow-y-auto bg-gray-50">
                    {conversationHistory.length === 0 ? (
                      <div className="text-center text-gray-500 mt-8">
                        <MessageSquare className="w-12 h-12 mx-auto mb-2 opacity-50" />
                        <p>Start a conversation to see messages here</p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {conversationHistory.map((message) => (
                          <div
                            key={message.id}
                            className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                          >
                            <div
                              className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                                message.type === 'user'
                                  ? 'bg-blue-500 text-white'
                                  : 'bg-white border'
                              }`}
                            >
                              <p className="text-sm">{message.content}</p>
                              <p
                                className={`text-xs mt-1 ${
                                  message.type === 'user' ? 'text-blue-100' : 'text-gray-500'
                                }`}
                              >
                                {new Date(message.timestamp).toLocaleTimeString()}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  <div className="flex gap-2">
                    <input
                      type="text"
                      placeholder="Type your message..."
                      className="flex-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      disabled={!isCallActive}
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          handleSendMessage(e.currentTarget.value);
                          e.currentTarget.value = '';
                        }
                      }}
                    />
                    <Button
                      onClick={() => {
                        const input = document.querySelector(
                          'input[type="text"]',
                        ) as HTMLInputElement;
                        if (input) {
                          handleSendMessage(input.value);
                          input.value = '';
                        }
                      }}
                      disabled={!isCallActive}
                    >
                      Send
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Performance Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Response Time:</span>
                    <span className="font-medium">1.2s avg</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Success Rate:</span>
                    <span className="font-medium">98.5%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Total Tests:</span>
                    <span className="font-medium">247</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Usage Statistics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Text Chats:</span>
                    <span className="font-medium">156</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Web Calls:</span>
                    <span className="font-medium">67</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Phone Calls:</span>
                    <span className="font-medium">24</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Test completed successfully</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span>Agent configuration updated</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                    <span>New tool added</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
