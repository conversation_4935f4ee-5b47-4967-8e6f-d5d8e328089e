import Link from 'next/link';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Frown } from 'lucide-react';

export default function NotFound() {
  return (
    <div className="flex flex-col items-center justify-center min-h-[calc(100vh-64px)] bg-background text-foreground p-4">
      <Frown className="w-24 h-24 text-primary mb-6" />
      <h1 className="text-6xl font-extrabold text-primary mb-4 animate-fade-in">404</h1>
      <h2 className="text-2xl md:text-3xl font-semibold text-foreground mb-4 text-center animate-slide-up">
        Oops! Page Not Found
      </h2>
      <p className="text-muted-foreground text-lg text-center max-w-md mb-8 animate-slide-up delay-100">
        We can&apos;t seem to find the page you&apos;re looking for. It might have been moved or
        deleted.
      </p>
      <Link href="/" passHref>
        <Button className="bg-primary text-primary-foreground hover:bg-primary/90 transition-colors duration-200 animate-bounce-subtle">
          Go to Homepage
        </Button>
      </Link>
    </div>
  );
}
