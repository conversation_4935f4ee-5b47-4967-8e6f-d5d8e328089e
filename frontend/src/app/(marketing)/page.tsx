'use client';

import React from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { Store, ArrowRight, Sparkles, Zap, Shield } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const Home: React.FC = () => {
  const { user } = useAuth();

  return (
    <div className="max-w-7xl mx-auto">
      {/* Hero Section */}
      <div className="text-center py-20 px-4 animate-fade-in">
        <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-500 rounded-3xl mb-8 shadow-lg animate-bounce">
          <Sparkles className="w-10 h-10 text-white" />
        </div>

        <h1 className="text-6xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-primary via-primary to-accent bg-clip-text text-transparent leading-tight">
          <PERSON><PERSON>
        </h1>

        <p className="text-xl md:text-2xl text-foreground mb-12 max-w-4xl mx-auto leading-relaxed">
          Revolutionize your home services business with our cutting-edge Voice AI. Automate
          appointment booking, qualify leads, and provide 24/7 customer support, ensuring you never
          miss a booking.
        </p>

        {!user ? (
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Link href="/register">
              <Button size="lg" className="text-lg px-10 py-4 group">
                Get Started Free
                <ArrowRight
                  className="ml-3 group-hover:translate-x-1 transition-transform"
                  size={24}
                />
              </Button>
            </Link>
            <Link href="/login">
              <Button variant="outline" size="lg" className="text-lg px-10 py-4">
                Sign In
              </Button>
            </Link>
          </div>
        ) : (
          <Link href="/dashboard">
            <Button size="lg" className="text-lg px-10 py-4 group">
              Go to Dashboard
              <ArrowRight
                className="ml-3 group-hover:translate-x-1 transition-transform"
                size={24}
              />
            </Button>
          </Link>
        )}
      </div>

      <div className="py-20 px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-4">Key Features</h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Empowering your home services business with intelligent automation
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          <Card className="text-center group">
            <CardHeader>
              <div className="inline-flex items-center justify-center w-16 h-16 bg-primary rounded-2xl mb-6 shadow-lg group-hover:scale-110 transition-transform duration-200">
                <Sparkles className="w-8 h-8 text-primary-foreground" />
              </div>
              <CardTitle className="text-xl font-bold mb-3 text-foreground">
                24/7 AI Assistant
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground leading-relaxed">
                Automate appointment booking and answer customer FAQs around the clock
              </p>
            </CardContent>
          </Card>

          <Card className="text-center group">
            <CardHeader>
              <div className="inline-flex items-center justify-center w-16 h-16 bg-accent rounded-2xl mb-6 shadow-lg group-hover:scale-110 transition-transform duration-200">
                <Zap className="w-8 h-8 text-accent-foreground" />
              </div>
              <CardTitle className="text-xl font-bold mb-3 text-foreground">
                Intelligent Lead Qualification
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground leading-relaxed">
                AI-driven lead scoring and routing to ensure high-quality appointments
              </p>
            </CardContent>
          </Card>

          <Card className="text-center group">
            <CardHeader>
              <div className="inline-flex items-center justify-center w-16 h-16 bg-secondary rounded-2xl mb-6 shadow-lg group-hover:scale-110 transition-transform duration-200">
                <Store className="w-8 h-8 text-secondary-foreground" />
              </div>
              <CardTitle className="text-xl font-bold mb-3 text-foreground">
                Multi-Service Support
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground leading-relaxed">
                Seamlessly manage appointments for plumbing, HVAC, electrical, and more
              </p>
            </CardContent>
          </Card>

          <Card className="text-center group">
            <CardHeader>
              <div className="inline-flex items-center justify-center w-16 h-16 bg-destructive rounded-2xl mb-6 shadow-lg group-hover:scale-110 transition-transform duration-200">
                <Shield className="w-8 h-8 text-destructive-foreground" />
              </div>
              <CardTitle className="text-xl font-bold mb-3 text-foreground">
                CRM Integration
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground leading-relaxed">
                Connect with your existing CRM to sync customer data and appointment details
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      <div className="py-20 px-4">
        <div className="grid lg:grid-cols-3 gap-8">
          <Card className="text-center">
            <CardHeader>
              <div className="inline-flex items-center justify-center w-16 h-16 bg-primary rounded-2xl mb-6 shadow-lg">
                <Zap className="w-8 h-8 text-primary-foreground" />
              </div>
              <CardTitle className="text-2xl font-bold mb-4 text-foreground">
                Lightning Fast
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground text-lg leading-relaxed">
                Built for speed with modern technology stack ensuring quick response times
              </p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <div className="inline-flex items-center justify-center w-16 h-16 bg-accent rounded-2xl mb-6 shadow-lg">
                <Shield className="w-8 h-8 text-accent-foreground" />
              </div>
              <CardTitle className="text-2xl font-bold mb-4 text-foreground">
                Secure & Reliable
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground text-lg leading-relaxed">
                Enterprise-grade security with 99.9% uptime guarantee for your business
              </p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <div className="inline-flex items-center justify-center w-16 h-16 bg-secondary rounded-2xl mb-6 shadow-lg">
                <Sparkles className="w-8 h-8 text-secondary-foreground" />
              </div>
              <CardTitle className="text-2xl font-bold mb-4 text-foreground">Easy to Use</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground text-lg leading-relaxed">
                Intuitive interface designed for both beginners and home service experts
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Home;
