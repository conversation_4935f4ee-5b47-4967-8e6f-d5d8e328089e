'use client';

import React from 'react';
import Header from '@/components/common/Header';

export default function MarketingLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-accent-50 relative overflow-hidden">
      <div className="absolute inset-0 opacity-40">
        <div className="absolute inset-0 bg-gradient-to-r from-primary-100 to-accent-100 opacity-20"></div>
        <div
          className="absolute inset-0"
          style={{
            background:
              'radial-gradient(circle at 50% 50%, rgba(59,130,246,0.05), transparent 50%)',
          }}
        ></div>
      </div>

      <div className="relative z-10">
        <Header />
        <main className="mx-auto space-y-8">{children}</main>
      </div>
    </div>
  );
}
