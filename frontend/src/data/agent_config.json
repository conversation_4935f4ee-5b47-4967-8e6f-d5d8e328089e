{"workflow": {"name": "Customer Support Agent Workflow", "description": "Orchestrates the customer support agent's conversational flow.", "active": true, "id": "agent-workflow-1", "nodes": [{"id": "input_gate_node", "name": "Input Gate", "type": "pythonFunction", "functionPath": "orchestrator.nodes.input_gate", "parameters": {}, "position": [0, 0]}, {"id": "state_load_node", "name": "State Load", "type": "pythonFunction", "functionPath": "orchestrator.nodes.state_load", "parameters": {}, "position": [0, 1]}, {"id": "intent_detect_node", "name": "Intent Detection", "type": "pythonFunction", "functionPath": "orchestrator.nodes.intent_detect", "parameters": {"system_prompt": "Given the following conversation, what is the user's intent? Possible intents: book_appointment, rag_query, handoff_human. Return only the intent name."}, "dependencies": ["llm_service"], "position": [0, 2]}, {"id": "rag_fetch_node", "name": "RAG Fetch", "type": "pythonFunction", "functionPath": "orchestrator.nodes.rag_fetch", "parameters": {}, "dependencies": ["llm_service", "embedding_service", "db", "settings"], "position": [1, 3]}, {"id": "book_appointment_node", "name": "Book Appointment", "type": "pythonFunction", "functionPath": "orchestrator.nodes.book_appointment", "parameters": {"tool_calls": [{"tool_name": "schedule_job", "args": {"service_type": "plumbing", "duration": 60, "time": "tomorrow", "address": "123 Main St", "customer_info": {"name": "<PERSON>", "phone": "555-1234"}}}]}, "dependencies": ["db"], "position": [0, 3]}, {"id": "response_planner_node", "name": "Response Planner", "type": "pythonFunction", "functionPath": "orchestrator.nodes.response_planner", "parameters": {"system_prompt": "You are an AI assistant for a home services company. Based on the following conversation and retrieved information, formulate a concise and helpful response."}, "dependencies": ["llm_service"], "position": [0, 4]}, {"id": "tts_node", "name": "Text-to-Speech", "type": "pythonFunction", "functionPath": "orchestrator.nodes.tts", "parameters": {}, "position": [0, 5]}, {"id": "state_persist_node", "name": "State Persist", "type": "pythonFunction", "functionPath": "orchestrator.nodes.state_persist", "parameters": {}, "position": [0, 6]}], "connections": {"input_gate_node": {"main": [{"node": "state_load_node", "type": "main", "index": 0}]}, "state_load_node": {"main": [{"node": "intent_detect_node", "type": "main", "index": 0}]}, "intent_detect_node": {"conditional": {"type": "aiBased", "prompt": "Does the user's intent indicate they want to book an appointment? Respond with 'True' or 'False'.", "paths": {"True": [{"node": "book_appointment_node", "type": "main", "index": 0}], "False": [{"node": "rag_fetch_node", "type": "main", "index": 0}]}}}, "rag_fetch_node": {"main": [{"node": "response_planner_node", "type": "main", "index": 0}]}, "book_appointment_node": {"main": [{"node": "response_planner_node", "type": "main", "index": 0}]}, "response_planner_node": {"main": [{"node": "tts_node", "type": "main", "index": 0}]}, "tts_node": {"main": [{"node": "state_persist_node", "type": "main", "index": 0}]}, "state_persist_node": {"main": [{"node": "END", "type": "main", "index": 0}]}}, "start_node_id": "input_gate_node", "first_message": "Hello! How can I assist you today?", "speech_config": {"stt_vad_threshold": 0.7, "tts_voice_id": "default", "tts_speaking_rate": 1.0}, "conversation_settings": {"silence_timeout_seconds": 10, "reprompt_messages": ["Are you still there?", "Did you have any other questions?"], "max_reprompts": 2, "no_input_fallback_message": "I didn't hear anything. Can you please repeat that?", "no_match_fallback_message": "I'm sorry, I didn't understand that. Could you rephrase your request?"}, "global_tools": [{"name": "get_customer_details", "functionPath": "orchestrator.tools.langchain_tools.get_customer_details"}, {"name": "schedule_job", "functionPath": "orchestrator.tools.langchain_tools.schedule_job"}, {"name": "get_customer_jobs", "functionPath": "orchestrator.tools.langchain_tools.get_customer_jobs"}]}}