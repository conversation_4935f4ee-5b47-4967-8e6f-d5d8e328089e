{"workflow": {"name": "Electrical Inspection Agent", "description": "An AI assistant for scheduling electrical inspections for new clients.", "active": true, "id": "electrical-agent-1", "nodes": [{"id": "get_user_input_node", "name": "Get User Input", "type": "pythonFunction", "functionPath": "orchestrator.services.websocket.io.get_user_input", "dependencies": ["db"]}, {"id": "llm_conversation_node", "name": "LLM Conversation", "type": "pythonFunction", "functionPath": "orchestrator.nodes.llm.generate_response", "dependencies": ["llm_service", "db"]}], "connections": {"get_user_input_node": {"conditional": {"type": "pythonFunction", "conditionFunctionPath": "orchestrator.graph.should_continue", "paths": {"True": [{"node": "llm_conversation_node", "type": "main", "index": 0}], "False": [{"node": "END", "type": "main", "index": 0}]}}}, "llm_conversation_node": {"main": [{"node": "get_user_input_node", "type": "main", "index": 0}]}}, "start_node_id": "get_user_input_node", "first_message": "Hi, this is from our electrical services department. We're calling to help you schedule your complimentary electrical inspection. When would be a good time for us to visit?", "system_prompt": "You are an AI assistant for an electrical services company. Your task is to schedule electrical inspections for new clients. Explain the inspection process and find a suitable time slot. Be professional and informative.", "tools": [{"type": "webhook", "name": "book_appointment", "description": "book a 30-minute electrical inspection appointment", "api_schema": {"url": "https://api.example.com/electrical/bookings", "method": "POST", "request_body_schema": {"id": "body", "type": "object", "properties": [{"id": "time", "type": "string", "value_type": "llm_prompt", "description": "The start time of the booking in ISO 8601 format.", "required": true}, {"id": "service_type", "type": "string", "value_type": "constant", "constant_value": "electrical", "required": true}]}}}, {"type": "python", "name": "get_available_slots", "description": "Get available appointment slots for electrical inspections.", "functionPath": "your.module.get_electrical_slots"}]}}