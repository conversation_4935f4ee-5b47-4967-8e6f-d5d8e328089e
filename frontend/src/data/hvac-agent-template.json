{"workflow": {"name": "HVAC Service Agent", "description": "An AI assistant for an HVAC company to gather information, provide troubleshooting, and schedule appointments.", "active": true, "id": "hvac-agent-1", "nodes": [{"id": "get_user_input_node", "name": "Get User Input", "type": "pythonFunction", "functionPath": "orchestrator.services.websocket.io.get_user_input", "dependencies": ["db"]}, {"id": "llm_conversation_node", "name": "LLM Conversation", "type": "pythonFunction", "functionPath": "orchestrator.nodes.llm.generate_response", "dependencies": ["llm_service", "db"]}], "connections": {"get_user_input_node": {"conditional": {"type": "pythonFunction", "conditionFunctionPath": "orchestrator.graph.should_continue", "paths": {"True": [{"node": "llm_conversation_node", "type": "main", "index": 0}], "False": [{"node": "END", "type": "main", "index": 0}]}}}, "llm_conversation_node": {"main": [{"node": "get_user_input_node", "type": "main", "index": 0}]}}, "start_node_id": "get_user_input_node", "first_message": "Hello! Thank you for calling our HVAC service. How can I help you with your heating or cooling needs today?", "system_prompt": "You are an AI assistant for an HVAC company. Your goal is to gather information about the customer's HVAC issue, provide basic troubleshooting tips, and schedule a service appointment if necessary. Be empathetic and clear.", "tools": [{"type": "webhook", "name": "book_appointment", "description": "book a 30-minute HVAC service appointment", "api_schema": {"url": "https://api.example.com/hvac/bookings", "method": "POST", "request_body_schema": {"id": "body", "type": "object", "properties": [{"id": "time", "type": "string", "value_type": "llm_prompt", "description": "The start time of the booking in ISO 8601 format.", "required": true}, {"id": "service_type", "type": "string", "value_type": "constant", "constant_value": "hvac", "required": true}]}}}, {"type": "python", "name": "get_available_slots", "description": "Get available appointment slots for HVAC services.", "functionPath": "your.module.get_hvac_slots"}]}}