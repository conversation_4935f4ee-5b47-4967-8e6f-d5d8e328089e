{"workflow": {"name": "Plumbing Emergency Agent", "description": "An AI dispatcher for a 24/7 plumbing service to assess emergencies and dispatch plumbers.", "active": true, "id": "plumbing-agent-1", "nodes": [{"id": "get_user_input_node", "name": "Get User Input", "type": "pythonFunction", "functionPath": "orchestrator.services.websocket.io.get_user_input", "dependencies": ["db"]}, {"id": "llm_conversation_node", "name": "LLM Conversation", "type": "pythonFunction", "functionPath": "orchestrator.nodes.llm.generate_response", "dependencies": ["llm_service", "db"]}], "connections": {"get_user_input_node": {"conditional": {"type": "pythonFunction", "conditionFunctionPath": "orchestrator.graph.should_continue", "paths": {"True": [{"node": "llm_conversation_node", "type": "main", "index": 0}], "False": [{"node": "END", "type": "main", "index": 0}]}}}, "llm_conversation_node": {"main": [{"node": "get_user_input_node", "type": "main", "index": 0}]}}, "start_node_id": "get_user_input_node", "first_message": "Thank you for calling our emergency plumbing line. Please describe your plumbing issue, and I'll connect you with a technician immediately.", "system_prompt": "You are an AI dispatcher for a 24/7 plumbing service. Your priority is to quickly assess the emergency, get the customer's location, and dispatch the nearest available plumber. Stay calm and reassuring.", "tools": [{"type": "webhook", "name": "book_appointment", "description": "book a 30-minute plumbing service appointment", "api_schema": {"url": "https://api.example.com/plumbing/bookings", "method": "POST", "request_body_schema": {"id": "body", "type": "object", "properties": [{"id": "time", "type": "string", "value_type": "llm_prompt", "description": "The start time of the booking in ISO 8601 format.", "required": true}, {"id": "service_type", "type": "string", "value_type": "constant", "constant_value": "plumbing", "required": true}]}}}, {"type": "python", "name": "get_available_slots", "description": "Get available appointment slots for plumbing services.", "functionPath": "your.module.get_plumbing_slots"}]}}