{"workflow": {"name": "Simple Conversational Agent", "description": "A simple agent that gets user input and responds with an LLM.", "active": true, "id": "simple-agent-1", "nodes": [{"id": "get_user_input_node", "name": "Get User Input", "type": "pythonFunction", "functionPath": "orchestrator.services.websocket.io.get_user_input", "dependencies": ["db"]}, {"id": "llm_conversation_node", "name": "LLM Conversation", "type": "pythonFunction", "functionPath": "orchestrator.nodes.llm.generate_response", "dependencies": ["llm_service", "db"]}], "connections": {"get_user_input_node": {"conditional": {"type": "pythonFunction", "conditionFunctionPath": "orchestrator.graph.should_continue", "paths": {"True": [{"node": "llm_conversation_node", "type": "main", "index": 0}], "False": [{"node": "END", "type": "main", "index": 0}]}}}, "llm_conversation_node": {"main": [{"node": "get_user_input_node", "type": "main", "index": 0}]}}, "start_node_id": "get_user_input_node", "first_message": "Hello! I am a simple assistant. How can I help?", "conversation_settings": {"silence_timeout_seconds": 30}}}