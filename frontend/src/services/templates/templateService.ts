// frontend/src/services/templates/templateService.ts
import { api } from '@/lib/api';
import {
  Workflow,
  NodeConfig,
  MemoryConfig,
  DSPyConfig,
  NodeType,
  AgentType,
  AgentCategory,
  ToolConfig,
} from '../agent-builder/agentBuilderService'; // Import from agent-builder/agentBuilderService

export interface TemplateCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  count: number;
}

export interface TemplateFilter {
  category?: string;
  tags?: string[];
  rating_min?: number;
  search?: string;
  sort_by?: 'name' | 'rating' | 'usage_count' | 'created_at';
  sort_order?: 'asc' | 'desc';
}

export interface TemplateStats {
  total_templates: number;
  categories: TemplateCategory[];
  popular_tags: Array<{ tag: string; count: number }>;
  average_rating: number;
}

// Template Service
export const templateService = {
  // Template Discovery
  getAll: async (filter?: TemplateFilter): Promise<AgentTemplate[]> => {
    try {
      const params = new URLSearchParams();
      if (filter?.category) params.append('category', filter.category);
      if (filter?.tags?.length) params.append('tags', filter.tags.join(','));
      if (filter?.rating_min) params.append('rating_min', filter.rating_min.toString());
      if (filter?.search) params.append('search', filter.search);
      if (filter?.sort_by) params.append('sort_by', filter.sort_by);
      if (filter?.sort_order) params.append('sort_order', filter.sort_order);

      const response = await api.get(`/templates?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Failed to fetch templates:', error);
      return [];
    }
  },

  getById: async (id: string): Promise<AgentTemplate | null> => {
    try {
      const response = await api.get(`/templates/${id}`);
      return response.data;
    } catch (error) {
      console.error('Failed to fetch template:', error);
      return null;
    }
  },

  getFeatured: async (limit: number = 6): Promise<AgentTemplate[]> => {
    try {
      const response = await api.get(`/templates/featured?limit=${limit}`);
      return response.data;
    } catch (error) {
      console.error('Failed to fetch featured templates:', error);
      return [];
    }
  },

  getPopular: async (limit: number = 10): Promise<AgentTemplate[]> => {
    try {
      const response = await api.get(`/templates/popular?limit=${limit}`);
      return response.data;
    } catch (error) {
      console.error('Failed to fetch popular templates:', error);
      return [];
    }
  },

  getRecent: async (limit: number = 10): Promise<AgentTemplate[]> => {
    try {
      const response = await api.get(`/templates/recent?limit=${limit}`);
      return response.data;
    } catch (error) {
      console.error('Failed to fetch recent templates:', error);
      return [];
    }
  },

  // Template Statistics
  getStats: async (): Promise<TemplateStats> => {
    try {
      const response = await api.get('/templates/stats');
      return response.data;
    } catch (error) {
      console.error('Failed to fetch template stats:', error);
      return {
        total_templates: 0,
        categories: [],
        popular_tags: [],
        average_rating: 0
      };
    }
  },

  getCategories: async (): Promise<TemplateCategory[]> => {
    try {
      const response = await api.get('/templates/categories');
      return response.data;
    } catch (error) {
      console.error('Failed to fetch categories:', error);
      return [];
    }
  },

  // Template Usage
  useTemplate: async (templateId: string, customizations?: Partial<Workflow>): Promise<Workflow> => {
    try {
      const response = await api.post(`/templates/${templateId}/use`, {
        customizations
      });
      
      // Track usage
      await templateService.trackUsage(templateId);
      
      return response.data;
    } catch (error) {
      console.error('Failed to use template:', error);
      throw error;
    }
  },

  trackUsage: async (templateId: string): Promise<void> => {
    try {
      await api.post(`/templates/${templateId}/track-usage`);
    } catch (error) {
      console.error('Failed to track template usage:', error);
    }
  },

  // Template Rating
  rateTemplate: async (templateId: string, rating: number, review?: string): Promise<void> => {
    try {
      await api.post(`/templates/${templateId}/rate`, {
        rating,
        review
      });
    } catch (error) {
      console.error('Failed to rate template:', error);
      throw error;
    }
  },

  getReviews: async (templateId: string): Promise<any[]> => {
    try {
      const response = await api.get(`/templates/${templateId}/reviews`);
      return response.data;
    } catch (error) {
      console.error('Failed to fetch reviews:', error);
      return [];
    }
  },

  // Template Creation (for admin/power users)
  create: async (template: Omit<AgentTemplate, 'id' | 'usage_count' | 'created_at' | 'updated_at'>): Promise<AgentTemplate> => {
    try {
      const response = await api.post('/templates', template);
      return response.data;
    } catch (error) {
      console.error('Failed to create template:', error);
      throw error;
    }
  },

  update: async (id: string, template: Partial<AgentTemplate>): Promise<AgentTemplate> => {
    try {
      const response = await api.put(`/templates/${id}`, template);
      return response.data;
    } catch (error) {
      console.error('Failed to update template:', error);
      throw error;
    }
  },

  delete: async (id: string): Promise<void> => {
    try {
      await api.delete(`/templates/${id}`);
    } catch (error) {
      console.error('Failed to delete template:', error);
      throw error;
    }
  },

  // Template Sharing
  share: async (templateId: string, shareOptions: {
    public?: boolean;
    users?: string[];
    organizations?: string[];
  }): Promise<{ share_url: string }> => {
    try {
      const response = await api.post(`/templates/${templateId}/share`, shareOptions);
      return response.data;
    } catch (error) {
      console.error('Failed to share template:', error);
      throw error;
    }
  },

  // Template Import/Export
  export: async (templateId: string): Promise<Blob> => {
    try {
      const response = await api.get(`/templates/${templateId}/export`, {
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Failed to export template:', error);
      throw error;
    }
  },

  import: async (file: File): Promise<AgentTemplate> => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      const response = await api.post('/templates/import', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to import template:', error);
      throw error;
    }
  }
};

// Template utilities
export const templateUtils = {
  // Get template complexity score
  getComplexityScore: (template: AgentTemplate): number => {
    const nodeCount = template.workflow.nodes.length;
    const connectionCount = Object.keys(template.workflow.connections).length;
    const hasMemory = template.workflow.nodes.some(n => n.memory_config);
    const hasDSPy = template.workflow.nodes.some(n => n.dspy_config);
    
    let score = nodeCount * 2 + connectionCount;
    if (hasMemory) score += 5;
    if (hasDSPy) score += 10;
    
    return Math.min(score, 100);
  },

  // Get estimated execution time
  getEstimatedExecutionTime: (template: AgentTemplate): string => {
    const complexity = templateUtils.getComplexityScore(template);
    
    if (complexity < 20) return '< 1 second';
    if (complexity < 40) return '1-3 seconds';
    if (complexity < 60) return '3-10 seconds';
    return '10+ seconds';
  },

  // Get required resources
  getRequiredResources: (template: AgentTemplate): string[] => {
    const resources: string[] = [];
    
    const hasLLM = template.workflow.nodes.some(n => n.type === NodeType.LLM);
    const hasMemory = template.workflow.nodes.some(n => n.memory_config);
    const hasDSPy = template.workflow.nodes.some(n => n.dspy_config);
    const hasRAG = template.workflow.nodes.some(n => n.type === NodeType.RAG);
    
    if (hasLLM) resources.push('LLM Service');
    if (hasMemory) resources.push('Memory Storage');
    if (hasDSPy) resources.push('DSPy Framework');
    if (hasRAG) resources.push('Vector Database');
    
    return resources;
  },

  // Validate template
  validateTemplate: (template: AgentTemplate): { valid: boolean; errors: string[] } => {
    const errors: string[] = [];
    
    if (!template.name?.trim()) {
      errors.push('Template name is required');
    }
    
    if (!template.description?.trim()) {
      errors.push('Template description is required');
    }
    
    if (!template.workflow) {
      errors.push('Template workflow is required');
    } else {
      if (!template.workflow.nodes || template.workflow.nodes.length === 0) {
        errors.push('Template must have at least one node');
      }
    }
    
    if (!template.category) {
      errors.push('Template category is required');
    }
    
    return { valid: errors.length === 0, errors };
  },

  // Generate template preview
  generatePreview: (template: AgentTemplate): string => {
    const nodeTypes = [...new Set(template.workflow.nodes.map(n => n.type))];
    const nodeCount = template.workflow.nodes.length;
    const complexity = templateUtils.getComplexityScore(template);
    
    return `${nodeCount} nodes (${nodeTypes.join(', ')}) • Complexity: ${complexity}/100`;
  },

  // Search templates
  searchTemplates: (templates: AgentTemplate[], query: string): AgentTemplate[] => {
    const searchTerm = query.toLowerCase().trim();
    if (!searchTerm) return templates;
    
    return templates.filter(template => 
      template.name.toLowerCase().includes(searchTerm) ||
      template.description.toLowerCase().includes(searchTerm) ||
      template.tags.some(tag => tag.toLowerCase().includes(searchTerm)) ||
      template.category.toLowerCase().includes(searchTerm)
    );
  },

  // Sort templates
  sortTemplates: (templates: AgentTemplate[], sortBy: string, order: 'asc' | 'desc' = 'desc'): AgentTemplate[] => {
    return [...templates].sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'rating':
          aValue = a.rating || 0;
          bValue = b.rating || 0;
          break;
        case 'usage_count':
          aValue = a.usage_count;
          bValue = b.usage_count;
          break;
        case 'created_at':
          aValue = new Date(a.workflow.created_at || 0);
          bValue = new Date(b.workflow.created_at || 0);
          break;
        default:
          return 0;
      }
      
      if (order === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });
  }
};

export default templateService;