import { customerAPI } from '@/lib/api';
import { Customer } from '@/types';

export const customersService = {
  async getAllCustomers(): Promise<Customer[]> {
    const response = await customerAPI.getAll();
    return response.data;
  },

  async getCustomerById(id: number): Promise<Customer> {
    const response = await customerAPI.getById(id);
    return response.data;
  },

  async createCustomer(
    customerData: Omit<Customer, 'id' | 'created_at' | 'updated_at'>,
  ): Promise<Customer> {
    const response = await customerAPI.create(customerData);
    return response.data;
  },

  async updateCustomer(
    id: number,
    customerData: Partial<Omit<Customer, 'id' | 'created_at' | 'updated_at'>>,
  ): Promise<Customer> {
    const response = await customerAPI.update(id, customerData);
    return response.data;
  },

  async deleteCustomer(id: number): Promise<void> {
    await customerAPI.delete(id);
  },
};
