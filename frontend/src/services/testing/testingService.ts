// frontend/src/services/testing/testingService.ts
import { apiClient } from '../api/apiClient';
import { NodeConfig, Workflow } from '../agent-builder/agentBuilderService'; // Import from agent-builder/agentBuilderService
import { errorHandler } from '../error/errorHandler';

export interface TestCase {
  id: string;
  name: string;
  description: string;
  input: Record<string, any>;
  expected_output?: any;
  expected_behavior?: 'success' | 'failure' | 'timeout';
  timeout_ms?: number;
  tags: string[];
  created_at: string;
}

export interface TestResult {
  test_case_id: string;
  success: boolean;
  actual_output?: any;
  error_message?: string;
  execution_time_ms: number;
  node_results?: Array<{
    node_id: string;
    success: boolean;
    duration_ms: number;
    output?: any;
    error?: string;
  }>;
  timestamp: string;
}

export interface TestSuite {
  id: string;
  name: string;
  description: string;
  test_cases: TestCase[];
  agent_id?: string;
  workflow_id?: string;
  created_at: string;
  updated_at: string;
}

export interface TestExecution {
  id: string;
  test_suite_id: string;
  results: TestResult[];
  summary: {
    total: number;
    passed: number;
    failed: number;
    success_rate: number;
    total_duration_ms: number;
    avg_duration_ms: number;
  };
  started_at: string;
  completed_at?: string;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
}

class TestingService {
  private testSuites: Map<string, TestSuite> = new Map();
  private testExecutions: Map<string, TestExecution> = new Map();

  // Test Case Management
  createTestCase(
    name: string,
    description: string,
    input: Record<string, any>,
    expectedOutput?: any,
    expectedBehavior: 'success' | 'failure' | 'timeout' = 'success',
    timeoutMs: number = 30000,
    tags: string[] = []
  ): TestCase {
    return {
      id: this.generateId('test'),
      name,
      description,
      input,
      expected_output: expectedOutput,
      expected_behavior: expectedBehavior,
      timeout_ms: timeoutMs,
      tags,
      created_at: new Date().toISOString()
    };
  }

  // Test Suite Management
  createTestSuite(
    name: string,
    description: string,
    testCases: TestCase[] = [],
    agentId?: string,
    workflowId?: string
  ): TestSuite {
    const testSuite: TestSuite = {
      id: this.generateId('suite'),
      name,
      description,
      test_cases: testCases,
      agent_id: agentId,
      workflow_id: workflowId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    this.testSuites.set(testSuite.id, testSuite);
    return testSuite;
  }

  getTestSuite(id: string): TestSuite | undefined {
    return this.testSuites.get(id);
  }

  getAllTestSuites(): TestSuite[] {
    return Array.from(this.testSuites.values());
  }

  updateTestSuite(id: string, updates: Partial<TestSuite>): TestSuite | null {
    const testSuite = this.testSuites.get(id);
    if (!testSuite) return null;

    const updated = {
      ...testSuite,
      ...updates,
      updated_at: new Date().toISOString()
    };

    this.testSuites.set(id, updated);
    return updated;
  }

  deleteTestSuite(id: string): boolean {
    return this.testSuites.delete(id);
  }

  // Node Testing
  async testNode(nodeConfig: NodeConfig, testInput: Record<string, any>): Promise<TestResult> {
    const startTime = Date.now();
    const testCaseId = this.generateId('node_test');

    try {
      const result = await apiClient.testNode(nodeConfig, testInput);
      const executionTime = Date.now() - startTime;

      return {
        test_case_id: testCaseId,
        success: result.success || false,
        actual_output: result.response || result.result,
        execution_time_ms: executionTime,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      const executionTime = Date.now() - startTime;
      const appError = errorHandler.handleNodeTestError(error, nodeConfig.id);

      return {
        test_case_id: testCaseId,
        success: false,
        error_message: appError.message,
        execution_time_ms: executionTime,
        timestamp: new Date().toISOString()
      };
    }
  }

  // Workflow Testing
  async testWorkflow(workflow: Workflow, testInput: Record<string, any>): Promise<TestResult> {
    const startTime = Date.now();
    const testCaseId = this.generateId('workflow_test');

    try {
      const executionResult = await apiClient.executeAgent({ workflow }, testInput);
      const executionTime = Date.now() - startTime;

      // Get execution status for detailed results
      let nodeResults: TestResult['node_results'] = [];
      if (executionResult.execution_id) {
        try {
          const status = await apiClient.getExecutionStatus(executionResult.execution_id);
          nodeResults = status.node_metrics.map(metric => ({
            node_id: metric.node_id,
            success: metric.status === 'completed',
            duration_ms: metric.duration_ms || 0,
            output: metric.output_data,
            error: metric.error_message
          }));
        } catch (statusError) {
          console.warn('Failed to get execution status:', statusError);
        }
      }

      return {
        test_case_id: testCaseId,
        success: executionResult.status === 'completed',
        actual_output: executionResult,
        execution_time_ms: executionTime,
        node_results: nodeResults,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      const executionTime = Date.now() - startTime;
      const appError = errorHandler.handleAgentExecutionError(error, workflow.id);

      return {
        test_case_id: testCaseId,
        success: false,
        error_message: appError.message,
        execution_time_ms: executionTime,
        timestamp: new Date().toISOString()
      };
    }
  }

  // Test Suite Execution
  async executeTestSuite(testSuiteId: string, workflow?: Workflow): Promise<TestExecution> {
    const testSuite = this.testSuites.get(testSuiteId);
    if (!testSuite) {
      throw new Error(`Test suite not found: ${testSuiteId}`);
    }

    const execution: TestExecution = {
      id: this.generateId('execution'),
      test_suite_id: testSuiteId,
      results: [],
      summary: {
        total: testSuite.test_cases.length,
        passed: 0,
        failed: 0,
        success_rate: 0,
        total_duration_ms: 0,
        avg_duration_ms: 0
      },
      started_at: new Date().toISOString(),
      status: 'running'
    };

    this.testExecutions.set(execution.id, execution);

    try {
      // Execute test cases
      for (const testCase of testSuite.test_cases) {
        let result: TestResult;

        if (workflow) {
          result = await this.testWorkflow(workflow, testCase.input);
        } else {
          // If no workflow provided, this might be a node test
          throw new Error('Workflow is required for test suite execution');
        }

        result.test_case_id = testCase.id;
        execution.results.push(result);

        // Update summary
        if (result.success) {
          execution.summary.passed++;
        } else {
          execution.summary.failed++;
        }
        execution.summary.total_duration_ms += result.execution_time_ms;
      }

      // Finalize execution
      execution.summary.success_rate = (execution.summary.passed / execution.summary.total) * 100;
      execution.summary.avg_duration_ms = execution.summary.total_duration_ms / execution.summary.total;
      execution.completed_at = new Date().toISOString();
      execution.status = 'completed';

    } catch (error) {
      execution.status = 'failed';
      execution.completed_at = new Date().toISOString();
      console.error('Test suite execution failed:', error);
    }

    this.testExecutions.set(execution.id, execution);
    return execution;
  }

  // Batch Testing
  async batchTestNodes(nodeConfigs: NodeConfig[], testInput: Record<string, any>): Promise<TestResult[]> {
    const results = await Promise.allSettled(
      nodeConfigs.map(config => this.testNode(config, testInput))
    );

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          test_case_id: this.generateId('batch_test'),
          success: false,
          error_message: result.reason?.message || 'Unknown error',
          execution_time_ms: 0,
          timestamp: new Date().toISOString()
        };
      }
    });
  }

  // Test Data Generation
  generateTestCasesFromLogs(conversationLogs: Array<{
    user_message: string;
    agent_response: string;
    quality_score?: number;
  }>): TestCase[] {
    return conversationLogs.map((log, index) => ({
      id: this.generateId('generated_test'),
      name: `Generated Test Case ${index + 1}`,
      description: `Test case generated from conversation log`,
      input: { user_message: log.user_message },
      expected_output: log.agent_response,
      expected_behavior: 'success' as const,
      timeout_ms: 30000,
      tags: ['generated', 'conversation'],
      created_at: new Date().toISOString()
    }));
  }

  // Performance Testing
  async performanceTest(
    workflow: Workflow,
    testInput: Record<string, any>,
    iterations: number = 10,
    concurrency: number = 1
  ): Promise<{
    results: TestResult[];
    performance_metrics: {
      avg_duration_ms: number;
      min_duration_ms: number;
      max_duration_ms: number;
      success_rate: number;
      throughput_per_second: number;
    };
  }> {
    const results: TestResult[] = [];
    const startTime = Date.now();

    // Run tests in batches based on concurrency
    for (let i = 0; i < iterations; i += concurrency) {
      const batch = Array(Math.min(concurrency, iterations - i))
        .fill(null)
        .map(() => this.testWorkflow(workflow, testInput));

      const batchResults = await Promise.allSettled(batch);
      
      batchResults.forEach((result) => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          results.push({
            test_case_id: this.generateId('perf_test'),
            success: false,
            error_message: result.reason?.message || 'Unknown error',
            execution_time_ms: 0,
            timestamp: new Date().toISOString()
          });
        }
      });
    }

    const totalTime = Date.now() - startTime;
    const durations = results.map(r => r.execution_time_ms);
    const successCount = results.filter(r => r.success).length;

    return {
      results,
      performance_metrics: {
        avg_duration_ms: durations.reduce((a, b) => a + b, 0) / durations.length,
        min_duration_ms: Math.min(...durations),
        max_duration_ms: Math.max(...durations),
        success_rate: (successCount / results.length) * 100,
        throughput_per_second: (results.length / totalTime) * 1000
      }
    };
  }

  // Utilities
  private generateId(prefix: string): string {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  getTestExecution(id: string): TestExecution | undefined {
    return this.testExecutions.get(id);
  }

  getAllTestExecutions(): TestExecution[] {
    return Array.from(this.testExecutions.values());
  }

  // Export/Import
  exportTestSuite(testSuiteId: string): string {
    const testSuite = this.testSuites.get(testSuiteId);
    if (!testSuite) {
      throw new Error(`Test suite not found: ${testSuiteId}`);
    }
    return JSON.stringify(testSuite, null, 2);
  }

  importTestSuite(testSuiteJson: string): TestSuite {
    const testSuite = JSON.parse(testSuiteJson);
    testSuite.id = this.generateId('imported_suite');
    testSuite.created_at = new Date().toISOString();
    testSuite.updated_at = new Date().toISOString();
    
    this.testSuites.set(testSuite.id, testSuite);
    return testSuite;
  }
}

// Create singleton instance
export const testingService = new TestingService();

// React hook for testing
export const useTesting = () => {
  return {
    createTestCase: testingService.createTestCase.bind(testingService),
    createTestSuite: testingService.createTestSuite.bind(testingService),
    getTestSuite: testingService.getTestSuite.bind(testingService),
    getAllTestSuites: testingService.getAllTestSuites.bind(testingService),
    updateTestSuite: testingService.updateTestSuite.bind(testingService),
    deleteTestSuite: testingService.deleteTestSuite.bind(testingService),
    testNode: testingService.testNode.bind(testingService),
    testWorkflow: testingService.testWorkflow.bind(testingService),
    executeTestSuite: testingService.executeTestSuite.bind(testingService),
    batchTestNodes: testingService.batchTestNodes.bind(testingService),
    generateTestCasesFromLogs: testingService.generateTestCasesFromLogs.bind(testingService),
    performanceTest: testingService.performanceTest.bind(testingService),
    getTestExecution: testingService.getTestExecution.bind(testingService),
    getAllTestExecutions: testingService.getAllTestExecutions.bind(testingService),
    exportTestSuite: testingService.exportTestSuite.bind(testingService),
    importTestSuite: testingService.importTestSuite.bind(testingService)
  };
};

export default testingService;