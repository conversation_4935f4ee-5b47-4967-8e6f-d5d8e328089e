import { api } from '@/lib/api';
import { Workflow } from '@/types/workflow';

// Types for Agent Management
export interface Agent {
  id: number;
  name: string;
  description?: string;
  workflow?: Workflow;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface AgentCreate {
  name: string;
  description?: string;
  workflow?: Workflow;
  is_active?: boolean;
}

export interface AgentUpdate {
  name?: string;
  description?: string;
  workflow?: Workflow;
  is_active?: boolean;
}

export interface AgentTemplate {
  id: string;
  name: string;
  description?: string;
  category?: string;
  workflow: Workflow;
  is_public: boolean;
  created_at: string;
  tags?: string[];
  is_featured?: boolean;
  created_by?: string;
  updated_at?: string;
  preview_image?: string;
  author?: string;
  version?: string;
  usage_count?: number;
  rating?: number | null;
}

export interface AgentRun {
  agent_id: number;
  input_data: any;
  context?: any;
}

// Main Agent Service
import { ToolMetadata } from '@/types/workflow';

export const agentService = {
  // CRUD Operations
  create: async (agent: AgentCreate): Promise<Agent> => {
    const response = await api.post('/api/agents/', agent);
    return response.data;
  },

  getAll: async (skip: number = 0, limit: number = 100): Promise<Agent[]> => {
    const response = await api.get(`/api/agents/?skip=${skip}&limit=${limit}`);
    return response.data;
  },

  getById: async (id: number): Promise<Agent> => {
    const response = await api.get(`/api/agents/${id}`);
    return response.data;
  },

  update: async (id: number, agent: AgentUpdate): Promise<Agent> => {
    const response = await api.put(`/api/agents/${id}`, agent);
    return response.data;
  },

  delete: async (id: number): Promise<Agent> => {
    const response = await api.delete(`/api/agents/${id}`);
    return response.data;
  },

  // Agent Execution
  run: async (agentRun: AgentRun): Promise<any> => {
    const response = await api.post('/api/agents/run', agentRun);
    return response.data;
  },

  // Agent Configuration Helpers
  validateConfig: async (config: any): Promise<{ valid: boolean; errors?: string[] }> => {
    try {
      const response = await api.post('/api/agents/validate-config', { config });
      return response.data;
    } catch (error: any) {
      return {
        valid: false,
        errors: [error.response?.data?.detail || 'Configuration validation failed'],
      };
    }
  },

  // Agent Deployment
  deploy: async (id: number): Promise<{ success: boolean; deployment_url?: string }> => {
    const response = await api.post(`/api/agents/${id}/deploy`);
    return response.data;
  },

  undeploy: async (id: number): Promise<{ success: boolean }> => {
    const response = await api.post(`/api/agents/${id}/undeploy`);
    return response.data;
  },

  // Agent Testing
  test: async (id: number, testInput: any): Promise<any> => {
    const response = await api.post(`/api/agents/${id}/test`, { input: testInput });
    return response.data;
  },

  // Agent Analytics
  getAnalytics: async (id: number, timeRange?: string): Promise<any> => {
    const params = timeRange ? `?time_range=${timeRange}` : '';
    const response = await api.get(`/api/agents/${id}/analytics${params}`);
    return response.data;
  },

  // Agent Conversation History
  getConversations: async (id: number, skip: number = 0, limit: number = 50): Promise<any[]> => {
    const response = await api.get(`/api/agents/${id}/conversations?skip=${skip}&limit=${limit}`);
    return response.data;
  },

  // Tool Metadata
  getToolMetadata: async (): Promise<ToolMetadata[]> => {
    const response = await api.get('/api/nodes/tools/metadata');
    return response.data;
  },
};

// Agent Template Services
export const agentTemplateService = {
  getAll: async (skip: number = 0, limit: number = 100): Promise<AgentTemplate[]> => {
    const response = await api.get(`/api/agents/templates/?skip=${skip}&limit=${limit}`);
    return response.data;
  },

  getById: async (id: number): Promise<AgentTemplate> => {
    const response = await api.get(`/api/agents/templates/${id}`);
    return response.data;
  },

  createFromTemplate: async (
    templateId: string,
    customizations: { name: string },
  ): Promise<Agent> => {
    const response = await api.post(`/api/templates/${templateId}/use`, {
      customizations,
    });
    return response.data;
  },
};
