// Main services export file for the Agent Builder application

// Agent Builder Services
export {
  agentBuilderService,
  type NodeConfig,
  type MemoryConfig,
  type DSPyConfig,
  type Workflow,
  type AgentTemplate,
  type ExecutionMetrics,
  type AgentExecutionState,
  type NodeType,
  workflowUtils
} from './agent-builder/agentBuilderService';

// Template Services
export {
  templateService,
  type TemplateCategory,
  type TemplateFilter,
  type TemplateStats,
  templateUtils
} from './templates/templateService';

// API Client
export {
  apiClient,
  type ApiResponse,
  type ApiError,
  type ExecutionState,
  type NodeTestResult,
  type ValidationResult,
  apiUtils
} from './api/apiClient';

// Error Handling
export {
  errorHandler,
  handleError,
  handleApiError,
  createError,
  useErrorHandler,
  ErrorType,
  type AppError,
  type ErrorLog
} from './error/errorHandler';

// WebSocket Services
export {
  websocketService,
  useWebSocket,
  type WebSocketMessage,
  type ExecutionUpdate,
  type WebSocketConfig,
  type WebSocketEventHandler,
  type ExecutionEventHandler
} from './websocket/websocketService';

// Testing Services
export {
  testingService,
  useTesting,
  type TestCase,
  type TestResult,
  type TestSuite,
  type TestExecution
} from './testing/testingService';

// Configuration Services
export {
  configService,
  useConfig,
  type AppConfig,
  type UserPreferences
} from './config/configService';

// Service initialization and setup
export const initializeServices = async () => {
  try {
    // Initialize configuration service
    const config = configService.getConfig();
    
    // Set up error reporting if enabled
    if (config.development.enableErrorReporting) {
      errorHandler.enableReporting();
    } else {
      errorHandler.disableReporting();
    }
    
    // Initialize API client with config
    // This would typically set up interceptors, base URLs, etc.
    
    console.log('Services initialized successfully');
    return true;
  } catch (error) {
    console.error('Failed to initialize services:', error);
    return false;
  }
};

// Service health check
export const checkServiceHealth = async () => {
  const health = {
    api: false,
    websocket: false,
    config: true,
    error_handler: true,
    testing: true,
    templates: true
  };

  try {
    // Check API health
    await apiClient.healthCheck();
    health.api = true;
  } catch (error) {
    console.warn('API health check failed:', error);
  }

  try {
    // Check WebSocket connection status
    const wsStatus = websocketService.getConnectionStatus();
    health.websocket = wsStatus.connected;
  } catch (error) {
    console.warn('WebSocket health check failed:', error);
  }

  return health;
};

// Service cleanup
export const cleanupServices = () => {
  try {
    // Disconnect WebSocket
    websocketService.disconnect();
    
    // Clear error logs if needed
    // errorHandler.clearErrorLogs();
    
    console.log('Services cleaned up successfully');
  } catch (error) {
    console.error('Error during service cleanup:', error);
  }
};

// Utility functions for service management
export const serviceUtils = {
  // Get all service instances
  getServices: () => ({
    agentBuilder: agentBuilderService,
    template: templateService,
    api: apiClient,
    error: errorHandler,
    websocket: websocketService,
    testing: testingService,
    config: configService
  }),

  // Check if all critical services are available
  areCriticalServicesAvailable: () => {
    try {
      return !!(
        agentBuilderService &&
        templateService &&
        apiClient &&
        errorHandler &&
        configService
      );
    } catch (error) {
      return false;
    }
  },

  // Get service versions (if available)
  getServiceVersions: () => ({
    agentBuilder: '1.0.0',
    template: '1.0.0',
    api: '1.0.0',
    error: '1.0.0',
    websocket: '1.0.0',
    testing: '1.0.0',
    config: '1.0.0'
  }),

  // Service feature flags
  getServiceFeatures: () => {
    const config = configService.getConfig();
    return {
      templateBrowser: config.features.templateBrowser,
      agentAnalytics: config.features.agentAnalytics,
      realTimeChat: config.features.realTimeChat,
      batchTesting: config.features.batchTesting,
      workflowExport: config.features.workflowExport,
      collaborativeEditing: config.features.collaborativeEditing
    };
  }
};

// Export default service manager
export default {
  initialize: initializeServices,
  checkHealth: checkServiceHealth,
  cleanup: cleanupServices,
  utils: serviceUtils
};