// frontend/src/services/agent-builder/agentBuilderService.ts
import { api } from '@/lib/api';
import {
  NodeConfig,
  MemoryConfig,
  DSPyConfig,
  Workflow,
  NodeType,
  AgentType,
  AgentCategory,
  ToolConfig,
} from '@/types/workflow'; // Consolidated imports

// Enhanced types for Agent Builder
export {
  NodeConfig,
  MemoryConfig,
  DSPyConfig,
  Workflow,
  NodeType,
  AgentType,
  AgentCategory,
  ToolConfig,
};

export interface AgentTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  workflow: Workflow;
  preview_image?: string;
  author?: string;
  version: string;
  usage_count: number;
  rating?: number;
}

export interface ExecutionMetrics {
  node_id: string;
  start_time?: string;
  end_time?: string;
  duration_ms?: number;
  status: 'pending' | 'running' | 'completed' | 'failed';
  error_message?: string;
  input_data?: Record<string, any>;
  output_data?: Record<string, any>;
  memory_usage?: number;
  token_usage?: Record<string, number>;
}

export interface AgentExecutionState {
  execution_id: string;
  agent_id: string;
  current_node_id?: string;
  execution_path: string[];
  node_metrics: ExecutionMetrics[];
  global_state: Record<string, any>;
  created_at?: string;
  updated_at?: string;
}

// Agent Builder Service
export const agentBuilderService = {
  // Template Management
  getTemplates: async (): Promise<AgentTemplate[]> => {
    try {
      const response = await api.get('/agent-builder/templates');
      return response.data;
    } catch (error) {
      console.error('Failed to fetch templates:', error);
      return [];
    }
  },

  // Agent Configuration
  createAgent: async (workflow: Workflow): Promise<{ success: boolean; agent_id?: string }> => {
    try {
      const response = await api.post('/agent-builder/agents', { workflow });
      return { success: true, agent_id: response.data.id };
    } catch (error) {
      console.error('Failed to create agent:', error);
      return { success: false };
    }
  },

  updateAgent: async (agentId: string, workflow: Workflow): Promise<{ success: boolean }> => {
    try {
      await api.put(`/agent-builder/agents/${agentId}`, { workflow });
      return { success: true };
    } catch (error) {
      console.error('Failed to update agent:', error);
      return { success: false };
    }
  },

  // Agent Execution
  executeAgent: async (
    agentId: string, 
    inputData: Record<string, any>
  ): Promise<{ execution_id: string; status: string }> => {
    try {
      const response = await api.post('/agent-builder/agents/execute', {
        agent_config: { workflow: { id: agentId } },
        input_data: inputData
      });
      return response.data;
    } catch (error) {
      console.error('Failed to execute agent:', error);
      throw error;
    }
  },

  getExecutionStatus: async (executionId: string): Promise<AgentExecutionState> => {
    try {
      const response = await api.get(`/agent-builder/executions/${executionId}`);
      return response.data;
    } catch (error) {
      console.error('Failed to get execution status:', error);
      throw error;
    }
  },

  // Node Management
  getNodeTypes: async (): Promise<
    Array<{
      type: NodeType;
      name: string;
      description: string;
      category: string;
      icon: string;
      parameters: Array<{
        name: string;
        type: string;
        description: string;
        required: boolean;
        default?: any;
        enum_values?: any[];
        min_value?: number;
        max_value?: number;
      }>;
    }>
  > => {
    try {
      const response = await api.get('/nodes/types');
      return response.data;
    } catch (error) {
      console.error('Failed to fetch node types:', error);
      return [];
    }
  },

  testNode: async (nodeConfig: NodeConfig, testInput: Record<string, any>): Promise<any> => {
    try {
      const response = await api.post('/nodes/test', {
        node_config: nodeConfig,
        test_input: testInput
      });
      return response.data;
    } catch (error) {
      console.error('Failed to test node:', error);
      throw error;
    }
  },

  validateNodeConfig: async (nodeConfig: NodeConfig): Promise<{ valid: boolean; errors: string[] }> => {
    try {
      const response = await api.post('/nodes/validate', nodeConfig);
      return response.data;
    } catch (error) {
      console.error('Failed to validate node config:', error);
      return { valid: false, errors: ['Validation failed'] };
    }
  },

  // DSPy Integration
  getDSPyModules: async (): Promise<any[]> => {
    try {
      const response = await api.get('/agent-builder/dspy/modules');
      return response.data;
    } catch (error) {
      console.error('Failed to fetch DSPy modules:', error);
      return [];
    }
  },

  optimizePrompt: async (
    prompt: string, 
    examples: any[], 
    method: string = 'bootstrap'
  ): Promise<{ optimized_prompt: string; improvement_score: number }> => {
    try {
      const response = await api.post('/agent-builder/optimize-prompt', {
        original_prompt: prompt,
        examples,
        optimization_method: method
      });
      return response.data;
    } catch (error) {
      console.error('Failed to optimize prompt:', error);
      throw error;
    }
  },

  // Memory Management
  testMemory: async (
    memoryType: string, 
    operation: string, 
    data: any
  ): Promise<any> => {
    try {
      const response = await api.post('/agent-builder/test-memory', {
        memory_type: memoryType,
        operation,
        data
      });
      return response.data;
    } catch (error) {
      console.error('Failed to test memory:', error);
      throw error;
    }
  },

  // WebSocket for real-time execution monitoring
  connectExecutionMonitor: (executionId: string): WebSocket => {
    const wsUrl = `${process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000'}/agent-builder/ws/execution/${executionId}`;
    return new WebSocket(wsUrl);
  },

  // Workflow Import/Export
  exportWorkflow: async (workflow: Workflow): Promise<Blob> => {
    try {
      const response = await api.post('/agent-builder/export', { workflow }, {
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Failed to export workflow:', error);
      throw error;
    }
  },

  importWorkflow: async (file: File): Promise<Workflow> => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      const response = await api.post('/agent-builder/import', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to import workflow:', error);
      throw error;
    }
  },

  // Analytics and Monitoring
  getAgentAnalytics: async (agentId: string, timeRange?: string): Promise<any> => {
    try {
      const params = timeRange ? `?time_range=${timeRange}` : '';
      const response = await api.get(`/agent-builder/agents/${agentId}/analytics${params}`);
      return response.data;
    } catch (error) {
      console.error('Failed to get agent analytics:', error);
      return null;
    }
  },

  getExecutionLogs: async (executionId: string): Promise<any[]> => {
    try {
      const response = await api.get(`/agent-builder/executions/${executionId}/logs`);
      return response.data;
    } catch (error) {
      console.error('Failed to get execution logs:', error);
      return [];
    }
  }
};

// Utility functions
export const workflowUtils = {
  validateWorkflow: (workflow: Workflow): { valid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (!workflow.name?.trim()) {
      errors.push('Workflow name is required');
    }

    if (!workflow.nodes || workflow.nodes.length === 0) {
      errors.push('Workflow must have at least one node');
    }

    // Check for start node
    const startNodeExists = workflow.nodes.some(node => 
      Object.keys(workflow.connections).length === 0 || 
      !Object.values(workflow.connections).some(conn => 
        'main' in conn && conn.main?.some((target: any) => target.node === node.id)
      )
    );

    if (!startNodeExists && workflow.nodes.length > 1) {
      errors.push('Workflow must have a clear start node');
    }

    // Validate node connections
    for (const [nodeId, connections] of Object.entries(workflow.connections)) {
      if (!workflow.nodes.find(n => n.id === nodeId)) {
        errors.push(`Connection source node '${nodeId}' not found`);
      }

      if ('main' in connections && connections.main) {
        for (const target of connections.main) {
          if (target.node !== 'END' && !workflow.nodes.find(n => n.id === target.node)) {
            errors.push(`Connection target node '${target.node}' not found`);
          }
        }
      }
      if ('conditional' in connections && connections.conditional?.paths) {
        for (const pathKey in connections.conditional.paths) {
          for (const target of connections.conditional.paths[pathKey]) {
            if (target.node !== 'END' && !workflow.nodes.find(n => n.id === target.node)) {
              errors.push(`Conditional connection target node '${target.node}' not found`);
            }
          }
        }
      }
    }

    return { valid: errors.length === 0, errors };
  },

  generateNodeId: (type: string): string => {
    return `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  },

  cloneWorkflow: (workflow: Workflow): Workflow => {
    return JSON.parse(JSON.stringify(workflow));
  },

  getWorkflowComplexity: (workflow: Workflow): 'simple' | 'moderate' | 'complex' => {
    const nodeCount = workflow.nodes.length;
    const connectionCount = Object.keys(workflow.connections).length;
    
    const hasMemory = workflow.nodes.some(n => n.memory_config);
    const hasDSPy = workflow.nodes.some(n => n.dspy_config);
    
    let score = nodeCount * 2 + connectionCount;
    if (hasMemory) score += 5;
    if (hasDSPy) score += 10;
    
    return Math.min(score, 100) as 'simple' | 'moderate' | 'complex'; // Cast to specific literal types
  }
};

export default agentBuilderService;