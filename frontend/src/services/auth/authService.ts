import { api } from '@/lib/api';

interface User {
  email: string;
  full_name: string;
  is_active: boolean;
}

export const authService = {
  async login(email: string, password: string): Promise<User> {
    const form_data = new URLSearchParams();
    form_data.append('username', email);
    form_data.append('password', password);
    const response = await api.post('/api/auth/token', form_data, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
    return response.data;
  },

  async logout(): Promise<void> {
    await api.post('/api/auth/logout');
  },

  async register(email: string, password: string, full_name: string): Promise<User> {
    const response = await api.post('/api/auth/register', {
      email,
      password,
      full_name,
    });
    return response.data;
  },

  async getCurrentUser(): Promise<User> {
    const response = await api.get('/api/auth/me');
    return response.data;
  },

  async forgotPassword(email: string): Promise<void> {
    await api.post('/api/auth/forgot-password', { email });
  },

  async resetPassword(token: string, password: string): Promise<void> {
    await api.post('/api/auth/reset-password', { token, password });
  },
};
