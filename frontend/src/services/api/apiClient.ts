import { api } from '@/lib/api';
import { errorHandler, ErrorType } from '../error/errorHandler';

// Enhanced API client with comprehensive error handling and retry logic
class ApiClient {
  private baseURL: string;
  private retryAttempts: number = 3;
  private retryDelay: number = 1000;

  constructor(baseURL: string = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000') {
    this.baseURL = baseURL;
  }

  private async delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  private async retryRequest<T>(
    requestFn: () => Promise<T>,
    attempts: number = this.retryAttempts,
    context?: any,
  ): Promise<T> {
    try {
      return await requestFn();
    } catch (error: any) {
      if (attempts > 1 && this.shouldRetry(error)) {
        await this.delay(this.retryDelay);
        return this.retryRequest(requestFn, attempts - 1, context);
      }

      // Handle error with context
      const appError = errorHandler.handleApiError(error, context);
      throw appError;
    }
  }

  private shouldRetry(error: any): boolean {
    // Retry on network errors, 5xx errors, or timeout
    return (
      !error.response ||
      error.response.status >= 500 ||
      error.code === 'NETWORK_ERROR' ||
      error.code === 'TIMEOUT'
    );
  }

  // Agent Builder APIs
  async getAgentTemplates(filters?: {
    category?: string;
    tags?: string;
    rating_min?: number;
    search?: string;
    sort_by?: string;
    sort_order?: string;
  }) {
    return this.retryRequest(async () => {
      const params = new URLSearchParams();
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            params.append(key, value.toString());
          }
        });
      }
      const response = await api.get(`/templates?${params.toString()}`);
      return response.data;
    });
  }

  async getAgentTemplate(id: string) {
    return this.retryRequest(async () => {
      const response = await api.get(`/templates/${id}`);
      return response.data;
    });
  }

  async useTemplate(templateId: string, customizations?: any) {
    return this.retryRequest(async () => {
      const response = await api.post(`/templates/${templateId}/use`, {
        customizations,
      });
      return response.data;
    });
  }

  async createAgent(workflow: any) {
    return this.retryRequest(async () => {
      const response = await api.post('/agent-builder/agents', { workflow });
      return response.data;
    });
  }

  async executeAgent(agentConfig: any, inputData: any) {
    return this.retryRequest(async () => {
      const response = await api.post('/agent-builder/agents/execute', {
        agent_config: agentConfig,
        input_data: inputData,
      });
      return response.data;
    });
  }

  async getExecutionStatus(executionId: string) {
    return this.retryRequest(async () => {
      const response = await api.get(`/agent-builder/executions/${executionId}`);
      return response.data;
    });
  }

  // Node Management APIs
  async getNodeTypes() {
    return this.retryRequest(async () => {
      const response = await api.get('/nodes/types');
      return response.data;
    });
  }

  async testNode(nodeConfig: any, testInput: any) {
    return this.retryRequest(async () => {
      const response = await api.post('/nodes/test', {
        node_config: nodeConfig,
        test_input: testInput,
      });
      return response.data;
    });
  }

  async validateNodeConfig(nodeConfig: any) {
    return this.retryRequest(async () => {
      const response = await api.post('/nodes/validate', nodeConfig);
      return response.data;
    });
  }

  // DSPy APIs
  async getDSPyModules() {
    return this.retryRequest(async () => {
      const response = await api.get('/agent-builder/dspy/modules');
      return response.data;
    });
  }

  async optimizePrompt(prompt: string, examples: any[], method: string = 'bootstrap') {
    return this.retryRequest(async () => {
      const response = await api.post('/agent-builder/optimize-prompt', {
        original_prompt: prompt,
        examples,
        optimization_method: method,
      });
      return response.data;
    });
  }

  // Memory APIs
  async testMemory(memoryType: string, operation: string, data: any) {
    return this.retryRequest(async () => {
      const response = await api.post('/agent-builder/test-memory', {
        memory_type: memoryType,
        operation,
        data,
      });
      return response.data;
    });
  }

  // Template Management APIs
  async getFeaturedTemplates(limit: number = 6) {
    return this.retryRequest(async () => {
      const response = await api.get(`/templates/featured?limit=${limit}`);
      return response.data;
    });
  }

  async getPopularTemplates(limit: number = 10) {
    return this.retryRequest(async () => {
      const response = await api.get(`/templates/popular?limit=${limit}`);
      return response.data;
    });
  }

  async getTemplateCategories() {
    return this.retryRequest(async () => {
      const response = await api.get('/templates/categories');
      return response.data;
    });
  }

  async getTemplateStats() {
    return this.retryRequest(async () => {
      const response = await api.get('/templates/stats');
      return response.data;
    });
  }

  async rateTemplate(templateId: string, rating: number, review?: string) {
    return this.retryRequest(async () => {
      const response = await api.post(`/templates/${templateId}/rate`, {
        rating,
        review,
      });
      return response.data;
    });
  }

  // Analytics APIs
  async getAgentAnalytics(agentId: string, timeRange?: string) {
    return this.retryRequest(async () => {
      const params = timeRange ? `?time_range=${timeRange}` : '';
      const response = await api.get(`/agent-builder/agents/${agentId}/analytics${params}`);
      return response.data;
    });
  }

  async getExecutionLogs(executionId: string) {
    return this.retryRequest(async () => {
      const response = await api.get(`/agent-builder/executions/${executionId}/logs`);
      return response.data;
    });
  }

  // File Operations
  async exportWorkflow(workflow: any): Promise<Blob> {
    return this.retryRequest(async () => {
      const response = await api.post(
        '/agent-builder/export',
        { workflow },
        {
          responseType: 'blob',
        },
      );
      return response.data;
    });
  }

  async importWorkflow(file: File) {
    return this.retryRequest(async () => {
      const formData = new FormData();
      formData.append('file', file);
      const response = await api.post('/agent-builder/import', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    });
  }

  async exportTemplate(templateId: string): Promise<Blob> {
    return this.retryRequest(async () => {
      const response = await api.get(`/templates/${templateId}/export`, {
        responseType: 'blob',
      });
      return response.data;
    });
  }

  // WebSocket Connection
  createExecutionWebSocket(executionId: string): WebSocket {
    const wsUrl = `${this.baseURL.replace('http', 'ws')}/agent-builder/ws/execution/${executionId}`;
    return new WebSocket(wsUrl);
  }

  // Health Check
  async healthCheck() {
    return this.retryRequest(async () => {
      const response = await api.get('/health');
      return response.data;
    });
  }

  // Batch Operations
  async batchTestNodes(nodeConfigs: Array<{ config: any; testInput: any }>) {
    const results = await Promise.allSettled(
      nodeConfigs.map(({ config, testInput }) => this.testNode(config, testInput)),
    );

    return results.map((result, index) => ({
      nodeId: nodeConfigs[index].config.id,
      success: result.status === 'fulfilled',
      data: result.status === 'fulfilled' ? result.value : null,
      error: result.status === 'rejected' ? result.reason : null,
    }));
  }

  async batchValidateNodes(nodeConfigs: any[]) {
    const results = await Promise.allSettled(
      nodeConfigs.map((config) => this.validateNodeConfig(config)),
    );

    return results.map((result, index) => ({
      nodeId: nodeConfigs[index].id,
      success: result.status === 'fulfilled',
      validation: result.status === 'fulfilled' ? result.value : null,
      error: result.status === 'rejected' ? result.reason : null,
    }));
  }
}

// Create singleton instance
export const apiClient = new ApiClient();

// Export types for better TypeScript support
export interface ApiResponse<T = any> {
  data: T;
  success: boolean;
  message?: string;
}

export interface ApiError {
  message: string;
  code?: string;
  details?: any;
}

export interface ExecutionState {
  execution_id: string;
  agent_id: string;
  current_node_id?: string;
  execution_path: string[];
  node_metrics: Array<{
    node_id: string;
    status: 'pending' | 'running' | 'completed' | 'failed';
    duration_ms?: number;
    error_message?: string;
  }>;
  global_state: Record<string, any>;
}

export interface NodeTestResult {
  success: boolean;
  response?: any;
  result?: any;
  error?: string;
  node_id: string;
  execution_time_ms?: number;
}

export interface ValidationResult {
  valid: boolean;
  errors: string[];
  node_id: string;
}

// Utility functions
export const apiUtils = {
  // Format error messages for user display
  formatError: (error: any): string => {
    if (error.response?.data?.detail) {
      return error.response.data.detail;
    }
    if (error.message) {
      return error.message;
    }
    return 'An unexpected error occurred';
  },

  // Check if error is retryable
  isRetryableError: (error: any): boolean => {
    return (
      !error.response ||
      error.response.status >= 500 ||
      error.code === 'NETWORK_ERROR' ||
      error.code === 'TIMEOUT'
    );
  },

  // Generate unique request ID for tracking
  generateRequestId: (): string => {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  },

  // Parse WebSocket messages
  parseWebSocketMessage: (message: string): any => {
    try {
      return JSON.parse(message);
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error);
      return null;
    }
  },
};

export default apiClient;
