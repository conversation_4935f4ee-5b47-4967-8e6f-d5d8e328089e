import { toast } from 'react-hot-toast';

// Error types
export enum ErrorType {
  NETWORK = 'NETWORK',
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NOT_FOUND = 'NOT_FOUND',
  SERVER = 'SERVER',
  TIMEOUT = 'TIMEOUT',
  UNKNOWN = 'UNKNOWN'
}

export interface AppError {
  type: ErrorType;
  message: string;
  code?: string;
  details?: any;
  timestamp: string;
  context?: {
    component?: string;
    action?: string;
    userId?: string;
    agentId?: string;
    executionId?: string;
  };
}

export interface ErrorLog {
  id: string;
  error: AppError;
  userAgent: string;
  url: string;
  stackTrace?: string;
  resolved: boolean;
  reportedAt: string;
}

class ErrorHandler {
  private errorLogs: ErrorLog[] = [];
  private maxLogs: number = 1000;
  private reportingEnabled: boolean = true;

  constructor() {
    // Set up global error handlers
    this.setupGlobalHandlers();
  }

  private setupGlobalHandlers() {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError(
        this.createError(
          ErrorType.UNKNOWN,
          'Unhandled promise rejection',
          undefined,
          event.reason
        ),
        { component: 'global', action: 'unhandledrejection' }
      );
    });

    // Handle JavaScript errors
    window.addEventListener('error', (event) => {
      this.handleError(
        this.createError(
          ErrorType.UNKNOWN,
          event.message,
          undefined,
          {
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
            stack: event.error?.stack
          }
        ),
        { component: 'global', action: 'javascript_error' }
      );
    });
  }

  createError(
    type: ErrorType,
    message: string,
    code?: string,
    details?: any
  ): AppError {
    return {
      type,
      message,
      code,
      details,
      timestamp: new Date().toISOString()
    };
  }

  handleError(error: AppError, context?: AppError['context']): void {
    // Add context to error
    if (context) {
      error.context = { ...error.context, ...context };
    }

    // Log error
    this.logError(error);

    // Show user-friendly message
    this.showUserMessage(error);

    // Report error if enabled
    if (this.reportingEnabled) {
      this.reportError(error);
    }
  }

  private logError(error: AppError): void {
    const errorLog: ErrorLog = {
      id: this.generateId(),
      error,
      userAgent: navigator.userAgent,
      url: window.location.href,
      stackTrace: new Error().stack,
      resolved: false,
      reportedAt: new Date().toISOString()
    };

    // Add to logs
    this.errorLogs.unshift(errorLog);

    // Maintain max logs limit
    if (this.errorLogs.length > this.maxLogs) {
      this.errorLogs = this.errorLogs.slice(0, this.maxLogs);
    }

    // Console log for development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error logged:', errorLog);
    }
  }

  private showUserMessage(error: AppError): void {
    const userMessage = this.getUserFriendlyMessage(error);
    
    switch (error.type) {
      case ErrorType.NETWORK:
        toast.error(userMessage, { duration: 5000 });
        break;
      case ErrorType.VALIDATION:
        toast.error(userMessage, { duration: 4000 });
        break;
      case ErrorType.AUTHENTICATION:
        toast.error(userMessage, { duration: 6000 });
        break;
      case ErrorType.AUTHORIZATION:
        toast.error(userMessage, { duration: 4000 });
        break;
      case ErrorType.NOT_FOUND:
        toast.error(userMessage, { duration: 3000 });
        break;
      case ErrorType.SERVER:
        toast.error(userMessage, { duration: 5000 });
        break;
      case ErrorType.TIMEOUT:
        toast.error(userMessage, { duration: 4000 });
        break;
      default:
        toast.error(userMessage, { duration: 4000 });
    }
  }

  private getUserFriendlyMessage(error: AppError): string {
    switch (error.type) {
      case ErrorType.NETWORK:
        return 'Network connection issue. Please check your internet connection and try again.';
      case ErrorType.VALIDATION:
        return error.message || 'Please check your input and try again.';
      case ErrorType.AUTHENTICATION:
        return 'Authentication failed. Please log in again.';
      case ErrorType.AUTHORIZATION:
        return 'You don\'t have permission to perform this action.';
      case ErrorType.NOT_FOUND:
        return 'The requested resource was not found.';
      case ErrorType.SERVER:
        return 'Server error occurred. Please try again later.';
      case ErrorType.TIMEOUT:
        return 'Request timed out. Please try again.';
      default:
        return error.message || 'An unexpected error occurred. Please try again.';
    }
  }

  private async reportError(error: AppError): Promise<void> {
    try {
      // In a real application, this would send to an error reporting service
      // like Sentry, LogRocket, or a custom endpoint
      await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/errors`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          error,
          userAgent: navigator.userAgent,
          url: window.location.href,
          timestamp: new Date().toISOString()
        })
      });
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  }

  // API Error Handler
  handleApiError(error: any, context?: AppError['context']): AppError {
    let appError: AppError;

    if (error.response) {
      // Server responded with error status
      const status = error.response.status;
      const data = error.response.data;

      if (status === 400) {
        appError = this.createError(
          ErrorType.VALIDATION,
          data?.detail || data?.message || 'Invalid request',
          data?.code,
          data
        );
      } else if (status === 401) {
        appError = this.createError(
          ErrorType.AUTHENTICATION,
          'Authentication required',
          'UNAUTHORIZED',
          data
        );
      } else if (status === 403) {
        appError = this.createError(
          ErrorType.AUTHORIZATION,
          'Access denied',
          'FORBIDDEN',
          data
        );
      } else if (status === 404) {
        appError = this.createError(
          ErrorType.NOT_FOUND,
          'Resource not found',
          'NOT_FOUND',
          data
        );
      } else if (status >= 500) {
        appError = this.createError(
          ErrorType.SERVER,
          'Server error occurred',
          'SERVER_ERROR',
          data
        );
      } else {
        appError = this.createError(
          ErrorType.UNKNOWN,
          data?.detail || data?.message || 'Unknown error',
          data?.code,
          data
        );
      }
    } else if (error.request) {
      // Network error
      appError = this.createError(
        ErrorType.NETWORK,
        'Network error occurred',
        'NETWORK_ERROR',
        { request: error.request }
      );
    } else if (error.code === 'ECONNABORTED') {
      // Timeout error
      appError = this.createError(
        ErrorType.TIMEOUT,
        'Request timed out',
        'TIMEOUT',
        error
      );
    } else {
      // Unknown error
      appError = this.createError(
        ErrorType.UNKNOWN,
        error.message || 'Unknown error occurred',
        error.code,
        error
      );
    }

    this.handleError(appError, context);
    return appError;
  }

  // Utility methods
  getErrorLogs(): ErrorLog[] {
    return [...this.errorLogs];
  }

  clearErrorLogs(): void {
    this.errorLogs = [];
  }

  markErrorResolved(errorId: string): void {
    const errorLog = this.errorLogs.find(log => log.id === errorId);
    if (errorLog) {
      errorLog.resolved = true;
    }
  }

  getUnresolvedErrors(): ErrorLog[] {
    return this.errorLogs.filter(log => !log.resolved);
  }

  enableReporting(): void {
    this.reportingEnabled = true;
  }

  disableReporting(): void {
    this.reportingEnabled = false;
  }

  private generateId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Specific error handlers for common scenarios
  handleAgentExecutionError(error: any, agentId: string, executionId?: string): AppError {
    return this.handleApiError(error, {
      component: 'agent-execution',
      action: 'execute',
      agentId,
      executionId
    });
  }

  handleNodeTestError(error: any, nodeId: string): AppError {
    return this.handleApiError(error, {
      component: 'node-test',
      action: 'test',
      agentId: nodeId
    });
  }

  handleTemplateLoadError(error: any, templateId: string): AppError {
    return this.handleApiError(error, {
      component: 'template',
      action: 'load',
      agentId: templateId
    });
  }

  handleMemoryError(error: any, memoryType: string, operation: string): AppError {
    return this.handleApiError(error, {
      component: 'memory',
      action: operation,
      agentId: memoryType
    });
  }

  handleDSPyError(error: any, moduleType: string, operation: string): AppError {
    return this.handleApiError(error, {
      component: 'dspy',
      action: operation,
      agentId: moduleType
    });
  }
}

// Create singleton instance
export const errorHandler = new ErrorHandler();

// Convenience functions
export const handleError = (error: AppError, context?: AppError['context']) => {
  errorHandler.handleError(error, context);
};

export const handleApiError = (error: any, context?: AppError['context']) => {
  return errorHandler.handleApiError(error, context);
};

export const createError = (
  type: ErrorType,
  message: string,
  code?: string,
  details?: any
) => {
  return errorHandler.createError(type, message, code, details);
};

// React hook for error handling
export const useErrorHandler = () => {
  return {
    handleError: errorHandler.handleError.bind(errorHandler),
    handleApiError: errorHandler.handleApiError.bind(errorHandler),
    createError: errorHandler.createError.bind(errorHandler),
    getErrorLogs: errorHandler.getErrorLogs.bind(errorHandler),
    clearErrorLogs: errorHandler.clearErrorLogs.bind(errorHandler),
    markErrorResolved: errorHandler.markErrorResolved.bind(errorHandler),
    getUnresolvedErrors: errorHandler.getUnresolvedErrors.bind(errorHandler),
    
    // Specific handlers
    handleAgentExecutionError: errorHandler.handleAgentExecutionError.bind(errorHandler),
    handleNodeTestError: errorHandler.handleNodeTestError.bind(errorHandler),
    handleTemplateLoadError: errorHandler.handleTemplateLoadError.bind(errorHandler),
    handleMemoryError: errorHandler.handleMemoryError.bind(errorHandler),
    handleDSPyError: errorHandler.handleDSPyError.bind(errorHandler)
  };
};

export default errorHandler;
