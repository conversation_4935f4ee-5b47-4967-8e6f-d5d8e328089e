import { jobAP<PERSON> } from '@/lib/api';
import { Job } from '@/types'; // Import Job from @/types

export const jobsService = {
  async getAllJobs(): Promise<Job[]> {
    const response = await jobAPI.getAll();
    return response.data;
  },

  async getJobById(id: number): Promise<Job> {
    const response = await jobAPI.getById(id);
    return response.data;
  },

  async createJob(jobData: Omit<Job, 'id' | 'created_at' | 'updated_at'>): Promise<Job> {
    const response = await jobAPI.create(jobData);
    return response.data;
  },

  async updateJob(
    id: number,
    jobData: Partial<Omit<Job, 'id' | 'created_at' | 'updated_at'>>,
  ): Promise<Job> {
    const response = await jobAPI.update(id, jobData);
    return response.data;
  },

  async deleteJob(id: number): Promise<void> {
    await jobAPI.delete(id);
  },
};
