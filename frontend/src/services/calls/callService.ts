import { api } from '@/lib/api';

// Types for Call Management
export interface CallRequest {
  type: 'text' | 'voice' | 'web';
  phone_number?: string;
  custom_prompt?: string;
  custom_first_message?: string;
  agent_id?: number;
}

export interface CallResponse {
  success: boolean;
  call_history_id: number;
  call_sid?: string;
  message: string;
}

export interface CallHistory {
  id: number;
  customer_id?: number;
  call_sid: string;
  call_status: string;
  system_prompt?: string;
  first_message?: string;
  call_metadata?: any;
  created_at: string;
  updated_at: string;
}

export interface WebSocketMessage {
  type: string;
  data?: any;
  text?: string;
  audio?: string;
  transcript?: string;
  sender?: string;
  is_final?: boolean;
}

// Call Service
export const callService = {
  // Initiate different types of calls
  initiateTextChat: async (request: Omit<CallRequest, 'type'>): Promise<CallResponse> => {
    const response = await api.post('/call', { ...request, type: 'text' });
    return response.data;
  },

  initiateVoiceCall: async (request: CallRequest): Promise<CallResponse> => {
    if (!request.phone_number) {
      throw new Error('Phone number is required for voice calls');
    }
    const response = await api.post('/call', { ...request, type: 'voice' });
    return response.data;
  },

  initiateWebCall: async (request: Omit<CallRequest, 'type' | 'phone_number'>): Promise<CallResponse> => {
    const response = await api.post('/call', { ...request, type: 'web' });
    return response.data;
  },

  // Get call history
  getCallHistory: async (callHistoryId: number): Promise<CallHistory> => {
    const response = await api.get(`/call/history/${callHistoryId}`);
    return response.data;
  },

  // Get all call histories
  getAllCallHistories: async (skip: number = 0, limit: number = 50): Promise<CallHistory[]> => {
    const response = await api.get(`/call/history?skip=${skip}&limit=${limit}`);
    return response.data;
  },

  // WebSocket URL generators
  getTextChatWebSocketUrl: (): string => {
    const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const apiUrl = new URL(process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000');
    const wsHost = apiUrl.host;
    return `${wsProtocol}//${wsHost}/api/call/ws/text-chat`;
  },

  getWebCallWebSocketUrl: (): string => {
    const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const apiUrl = new URL(process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000');
    const wsHost = apiUrl.host;
    return `${wsProtocol}//${wsHost}/api/call/ws/web-call`;
  },

  getPhoneRelayWebSocketUrl: (callHistoryId: number): string => {
    const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const apiUrl = new URL(process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000');
    const wsHost = apiUrl.host;
    return `${wsProtocol}//${wsHost}/api/call/ws/phone-relay/${callHistoryId}`;
  },

  getCallUpdatesWebSocketUrl: (callHistoryId: number): string => {
    const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const apiUrl = new URL(process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000');
    const wsHost = apiUrl.host;
    return `${wsProtocol}//${wsHost}/api/call/ws/call-updates/${callHistoryId}`;
  },
};

// WebSocket Helper Class
export class CallWebSocket {
  private ws: WebSocket | null = null;
  private url: string;
  private onMessage: (message: WebSocketMessage) => void;
  private onConnect: () => void;
  private onDisconnect: () => void;
  private onError: (error: Event) => void;

  constructor(
    url: string,
    onMessage: (message: WebSocketMessage) => void,
    onConnect: () => void = () => {},
    onDisconnect: () => void = () => {},
    onError: (error: Event) => void = () => {}
  ) {
    this.url = url;
    this.onMessage = onMessage;
    this.onConnect = onConnect;
    this.onDisconnect = onDisconnect;
    this.onError = onError;
  }

  connect(): void {
    try {
      this.ws = new WebSocket(this.url);
      
      this.ws.onopen = () => {
        console.log('WebSocket connected');
        this.onConnect();
      };

      this.ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          this.onMessage(message);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      this.ws.onclose = () => {
        console.log('WebSocket disconnected');
        this.onDisconnect();
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        this.onError(error);
      };
    } catch (error) {
      console.error('Error creating WebSocket:', error);
      this.onError(error as Event);
    }
  }

  send(message: WebSocketMessage): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket is not connected');
    }
  }

  sendText(text: string): void {
    this.send({ type: 'user_message', text });
  }

  sendAudio(audioData: string): void {
    this.send({ type: 'audio_data', audio: audioData });
  }

  sendInit(callHistoryId: number, customPrompt?: string, customFirstMessage?: string): void {
    this.send({
      type: 'init',
      data: {
        call_history_id: callHistoryId,
        callHistoryId, // Support both formats
        custom_prompt: customPrompt,
        custom_first_message: customFirstMessage,
      },
    });
  }

  disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }
}

// Audio Processing Utilities
export const audioUtils = {
  // Convert audio blob to base64
  blobToBase64: (blob: Blob): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        const base64 = result.split(',')[1]; // Remove data:audio/wav;base64, prefix
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  },

  // Create WAV header for audio data
  createWavHeader: (dataLength: number, sampleRate: number = 16000, numChannels: number = 1, bitsPerSample: number = 16): ArrayBuffer => {
    const buffer = new ArrayBuffer(44);
    const view = new DataView(buffer);
    
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };

    const blockAlign = numChannels * (bitsPerSample / 8);
    const byteRate = sampleRate * blockAlign;

    writeString(0, 'RIFF');
    view.setUint32(4, 36 + dataLength, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, numChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, byteRate, true);
    view.setUint16(32, blockAlign, true);
    view.setUint16(34, bitsPerSample, true);
    writeString(36, 'data');
    view.setUint32(40, dataLength, true);

    return buffer;
  },

  // Get user media with proper constraints
  getUserMedia: async (constraints: MediaStreamConstraints = { audio: { sampleRate: 16000, channelCount: 1 } }): Promise<MediaStream> => {
    try {
      return await navigator.mediaDevices.getUserMedia(constraints);
    } catch (error) {
      console.error('Error accessing microphone:', error);
      throw new Error('Could not access microphone. Please check permissions.');
    }
  },
};
