// Configuration management service for the agent builder
export interface AppConfig {
  api: {
    baseUrl: string;
    timeout: number;
    retryAttempts: number;
    retryDelay: number;
  };
  websocket: {
    url: string;
    reconnectAttempts: number;
    reconnectDelay: number;
    heartbeatInterval: number;
  };
  ui: {
    theme: 'light' | 'dark' | 'auto';
    language: string;
    autoSave: boolean;
    autoSaveInterval: number;
    maxUndoSteps: number;
    gridSnap: boolean;
    showMinimap: boolean;
    showNodeLabels: boolean;
  };
  agent: {
    defaultTimeout: number;
    maxNodes: number;
    maxConnections: number;
    enableMemory: boolean;
    enableDSPy: boolean;
    enableAnalytics: boolean;
  };
  development: {
    enableDebugMode: boolean;
    enableErrorReporting: boolean;
    enablePerformanceMonitoring: boolean;
    logLevel: 'debug' | 'info' | 'warn' | 'error';
  };
  features: {
    templateBrowser: boolean;
    agentAnalytics: boolean;
    realTimeChat: boolean;
    batchTesting: boolean;
    workflowExport: boolean;
    collaborativeEditing: boolean;
  };
}

export interface UserPreferences {
  userId: string;
  preferences: {
    theme: string;
    language: string;
    notifications: {
      executionComplete: boolean;
      executionFailed: boolean;
      templateUpdates: boolean;
      systemMaintenance: boolean;
    };
    editor: {
      autoSave: boolean;
      gridSnap: boolean;
      showMinimap: boolean;
      nodeSpacing: number;
      connectionStyle: 'curved' | 'straight' | 'orthogonal';
    };
    dashboard: {
      defaultView: 'grid' | 'list';
      itemsPerPage: number;
      showPreview: boolean;
    };
  };
  lastUpdated: string;
}

class ConfigService {
  private config: AppConfig;
  private userPreferences: UserPreferences | null = null;
  private configListeners: Array<(config: AppConfig) => void> = [];
  private preferencesListeners: Array<(preferences: UserPreferences) => void> = [];

  constructor() {
    this.config = this.getDefaultConfig();
    this.loadConfig();
    this.loadUserPreferences();
  }

  private getDefaultConfig(): AppConfig {
    return {
      api: {
        baseUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',
        timeout: 30000,
        retryAttempts: 3,
        retryDelay: 1000
      },
      websocket: {
        url: process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000',
        reconnectAttempts: 5,
        reconnectDelay: 3000,
        heartbeatInterval: 30000
      },
      ui: {
        theme: 'auto',
        language: 'en',
        autoSave: true,
        autoSaveInterval: 30000,
        maxUndoSteps: 50,
        gridSnap: true,
        showMinimap: true,
        showNodeLabels: true
      },
      agent: {
        defaultTimeout: 30000,
        maxNodes: 50,
        maxConnections: 100,
        enableMemory: true,
        enableDSPy: true,
        enableAnalytics: true
      },
      development: {
        enableDebugMode: process.env.NODE_ENV === 'development',
        enableErrorReporting: process.env.NODE_ENV === 'production',
        enablePerformanceMonitoring: true,
        logLevel: process.env.NODE_ENV === 'development' ? 'debug' : 'info'
      },
      features: {
        templateBrowser: true,
        agentAnalytics: true,
        realTimeChat: true,
        batchTesting: true,
        workflowExport: true,
        collaborativeEditing: false // Future feature
      }
    };
  }

  private loadConfig(): void {
    try {
      const savedConfig = localStorage.getItem('agent_builder_config');
      if (savedConfig) {
        const parsed = JSON.parse(savedConfig);
        this.config = { ...this.config, ...parsed };
      }
    } catch (error) {
      console.warn('Failed to load saved config:', error);
    }
  }

  private saveConfig(): void {
    try {
      localStorage.setItem('agent_builder_config', JSON.stringify(this.config));
    } catch (error) {
      console.warn('Failed to save config:', error);
    }
  }

  private loadUserPreferences(): void {
    try {
      const savedPreferences = localStorage.getItem('agent_builder_preferences');
      if (savedPreferences) {
        this.userPreferences = JSON.parse(savedPreferences);
      }
    } catch (error) {
      console.warn('Failed to load user preferences:', error);
    }
  }

  private saveUserPreferences(): void {
    if (!this.userPreferences) return;

    try {
      localStorage.setItem('agent_builder_preferences', JSON.stringify(this.userPreferences));
    } catch (error) {
      console.warn('Failed to save user preferences:', error);
    }
  }

  // Config Management
  getConfig(): AppConfig {
    return { ...this.config };
  }

  updateConfig(updates: Partial<AppConfig>): void {
    this.config = this.mergeDeep(this.config, updates);
    this.saveConfig();
    this.notifyConfigListeners();
  }

  resetConfig(): void {
    this.config = this.getDefaultConfig();
    this.saveConfig();
    this.notifyConfigListeners();
  }

  // User Preferences Management
  getUserPreferences(): UserPreferences | null {
    return this.userPreferences ? { ...this.userPreferences } : null;
  }

  setUserPreferences(userId: string, preferences: UserPreferences['preferences']): void {
    this.userPreferences = {
      userId,
      preferences,
      lastUpdated: new Date().toISOString()
    };
    this.saveUserPreferences();
    this.notifyPreferencesListeners();
  }

  updateUserPreferences(updates: Partial<UserPreferences['preferences']>): void {
    if (!this.userPreferences) return;

    this.userPreferences.preferences = this.mergeDeep(this.userPreferences.preferences, updates);
    this.userPreferences.lastUpdated = new Date().toISOString();
    this.saveUserPreferences();
    this.notifyPreferencesListeners();
  }

  // Specific Config Getters
  getApiConfig() {
    return this.config.api;
  }

  getWebSocketConfig() {
    return this.config.websocket;
  }

  getUIConfig() {
    return this.config.ui;
  }

  getAgentConfig() {
    return this.config.agent;
  }

  getDevelopmentConfig() {
    return this.config.development;
  }

  getFeatureFlags() {
    return this.config.features;
  }

  // Feature Flag Helpers
  isFeatureEnabled(feature: keyof AppConfig['features']): boolean {
    return this.config.features[feature];
  }

  enableFeature(feature: keyof AppConfig['features']): void {
    this.updateConfig({
      features: {
        ...this.config.features,
        [feature]: true
      }
    });
  }

  disableFeature(feature: keyof AppConfig['features']): void {
    this.updateConfig({
      features: {
        ...this.config.features,
        [feature]: false
      }
    });
  }

  // Environment Helpers
  isDevelopment(): boolean {
    return process.env.NODE_ENV === 'development';
  }

  isProduction(): boolean {
    return process.env.NODE_ENV === 'production';
  }

  isDebugMode(): boolean {
    return this.config.development.enableDebugMode;
  }

  // Theme Management
  getTheme(): 'light' | 'dark' | 'auto' {
    return this.userPreferences?.preferences.theme as 'light' | 'dark' | 'auto' || this.config.ui.theme;
  }

  setTheme(theme: 'light' | 'dark' | 'auto'): void {
    if (this.userPreferences) {
      this.updateUserPreferences({ theme });
    } else {
      this.updateConfig({
        ui: {
          ...this.config.ui,
          theme
        }
      });
    }
  }

  // Language Management
  getLanguage(): string {
    return this.userPreferences?.preferences.language || this.config.ui.language;
  }

  setLanguage(language: string): void {
    if (this.userPreferences) {
      this.updateUserPreferences({ language });
    } else {
      this.updateConfig({
        ui: {
          ...this.config.ui,
          language
        }
      });
    }
  }

  // Event Listeners
  onConfigChange(listener: (config: AppConfig) => void): () => void {
    this.configListeners.push(listener);
    return () => {
      const index = this.configListeners.indexOf(listener);
      if (index > -1) {
        this.configListeners.splice(index, 1);
      }
    };
  }

  onPreferencesChange(listener: (preferences: UserPreferences) => void): () => void {
    this.preferencesListeners.push(listener);
    return () => {
      const index = this.preferencesListeners.indexOf(listener);
      if (index > -1) {
        this.preferencesListeners.splice(index, 1);
      }
    };
  }

  private notifyConfigListeners(): void {
    this.configListeners.forEach(listener => {
      try {
        listener(this.config);
      } catch (error) {
        console.error('Error in config listener:', error);
      }
    });
  }

  private notifyPreferencesListeners(): void {
    if (!this.userPreferences) return;

    this.preferencesListeners.forEach(listener => {
      try {
        listener(this.userPreferences!);
      } catch (error) {
        console.error('Error in preferences listener:', error);
      }
    });
  }

  // Utility Methods
  private mergeDeep(target: any, source: any): any {
    const result = { ...target };
    
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.mergeDeep(target[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
    
    return result;
  }

  // Export/Import Configuration
  exportConfig(): string {
    return JSON.stringify({
      config: this.config,
      preferences: this.userPreferences
    }, null, 2);
  }

  importConfig(configJson: string): void {
    try {
      const imported = JSON.parse(configJson);
      
      if (imported.config) {
        this.config = { ...this.getDefaultConfig(), ...imported.config };
        this.saveConfig();
        this.notifyConfigListeners();
      }
      
      if (imported.preferences) {
        this.userPreferences = imported.preferences;
        this.saveUserPreferences();
        this.notifyPreferencesListeners();
      }
    } catch (error) {
      throw new Error('Invalid configuration format');
    }
  }

  // Validation
  validateConfig(config: Partial<AppConfig>): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate API config
    if (config.api) {
      if (config.api.timeout && config.api.timeout < 1000) {
        errors.push('API timeout must be at least 1000ms');
      }
      if (config.api.retryAttempts && (config.api.retryAttempts < 0 || config.api.retryAttempts > 10)) {
        errors.push('API retry attempts must be between 0 and 10');
      }
    }

    // Validate agent config
    if (config.agent) {
      if (config.agent.maxNodes && (config.agent.maxNodes < 1 || config.agent.maxNodes > 100)) {
        errors.push('Max nodes must be between 1 and 100');
      }
      if (config.agent.maxConnections && (config.agent.maxConnections < 1 || config.agent.maxConnections > 500)) {
        errors.push('Max connections must be between 1 and 500');
      }
    }

    return { valid: errors.length === 0, errors };
  }
}

// Create singleton instance
export const configService = new ConfigService();

// React hook for configuration
export const useConfig = () => {
  return {
    getConfig: configService.getConfig.bind(configService),
    updateConfig: configService.updateConfig.bind(configService),
    resetConfig: configService.resetConfig.bind(configService),
    getUserPreferences: configService.getUserPreferences.bind(configService),
    setUserPreferences: configService.setUserPreferences.bind(configService),
    updateUserPreferences: configService.updateUserPreferences.bind(configService),
    getApiConfig: configService.getApiConfig.bind(configService),
    getWebSocketConfig: configService.getWebSocketConfig.bind(configService),
    getUIConfig: configService.getUIConfig.bind(configService),
    getAgentConfig: configService.getAgentConfig.bind(configService),
    getDevelopmentConfig: configService.getDevelopmentConfig.bind(configService),
    getFeatureFlags: configService.getFeatureFlags.bind(configService),
    isFeatureEnabled: configService.isFeatureEnabled.bind(configService),
    enableFeature: configService.enableFeature.bind(configService),
    disableFeature: configService.disableFeature.bind(configService),
    getTheme: configService.getTheme.bind(configService),
    setTheme: configService.setTheme.bind(configService),
    getLanguage: configService.getLanguage.bind(configService),
    setLanguage: configService.setLanguage.bind(configService),
    onConfigChange: configService.onConfigChange.bind(configService),
    onPreferencesChange: configService.onPreferencesChange.bind(configService),
    exportConfig: configService.exportConfig.bind(configService),
    importConfig: configService.importConfig.bind(configService),
    validateConfig: configService.validateConfig.bind(configService)
  };
};

export default configService;
