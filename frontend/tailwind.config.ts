import type { Config } from 'tailwindcss';

const config = {
  darkMode: 'class',
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
    './node_modules/@tremor/**/*.{js,ts,jsx,tsx}',
  ],
  prefix: '',
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px',
      },
    },
    extend: {
      colors: {
        // Base colors (Shadcn-like) mapped to CSS variables
        background: 'var(--background)',
        foreground: 'var(--foreground)',
        border: 'var(--border)',
        input: 'var(--input)',
        ring: 'var(--ring)',
        card: {
          DEFAULT: 'var(--card)',
          foreground: 'var(--card-foreground)',
        },
        popover: {
          DEFAULT: 'var(--popover)',
          foreground: 'var(--popover-foreground)',
        },
        primary: {
          DEFAULT: 'var(--primary)',
          foreground: 'var(--primary-foreground)',
        },
        secondary: {
          DEFAULT: 'var(--secondary)',
          foreground: 'var(--secondary-foreground)',
        },
        destructive: {
          DEFAULT: 'var(--destructive)',
          foreground: 'var(--destructive-foreground)',
        },
        success: {
          DEFAULT: 'var(--success)',
          foreground: 'var(--primary-foreground)', // Assuming white foreground for success
        },
        warning: {
          DEFAULT: 'var(--warning)',
          foreground: 'var(--foreground)', // Assuming dark foreground for warning
        },
        muted: {
          DEFAULT: 'var(--muted)',
          foreground: 'var(--muted-foreground)',
        },
        accent: {
          DEFAULT: 'var(--accent)',
          foreground: 'var(--accent-foreground)',
        },
        // Tremor default colors (optional, can be overridden by custom colors)
        tremor: {
          brand: {
            faint: 'var(--primary)',
            muted: 'var(--primary)',
            subtle: 'var(--primary)',
            DEFAULT: 'var(--primary)',
            emphasis: 'var(--primary)',
            inverted: 'var(--primary-foreground)',
          },
          background: {
            muted: 'var(--secondary)',
            subtle: 'var(--secondary)',
            DEFAULT: 'var(--secondary)',
            emphasis: 'var(--secondary)',
          },
          border: {
            DEFAULT: 'var(--border)',
          },
          ring: {
            DEFAULT: 'var(--ring)',
          },
          content: {
            subtle: 'var(--foreground)',
            DEFAULT: 'var(--foreground)',
            emphasis: 'var(--foreground)',
            strong: 'var(--foreground)',
            inverted: 'var(--background)',
          },
        },
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
        'fade-in': {
          from: { opacity: '0' },
          to: { opacity: '1' },
        },
        'slide-up': {
          from: { transform: 'translateY(20px)', opacity: '0' },
          to: { transform: 'translateY(0)', opacity: '1' },
        },
        'bounce-subtle': {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-5px)' },
        },
        gradient: {
          '0%, 100%': { 'background-size': '200% 200%', 'background-position': 'left center' },
          '50%': { 'background-size': '200% 200%', 'background-position': 'right center' },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
        'fade-in': 'fade-in 0.5s ease-in-out',
        'slide-up': 'slide-up 0.5s ease-out',
        'bounce-subtle': 'bounce-subtle 2s infinite',
        gradient: 'gradient 15s ease infinite',
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      boxShadow: {
        glass: 'var(--shadow-glass)',
        'glass-inset': 'var(--shadow-glass-inset)',
        elegant: 'var(--shadow-elegant)',
        'elegant-lg': 'var(--shadow-elegant-lg)',
      },
    },
  },
  plugins: [require('@tailwindcss/forms')],
} satisfies Config;

export default config;
