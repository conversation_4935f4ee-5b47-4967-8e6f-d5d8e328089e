repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.6.0
    hooks:
      - id: check-yaml
      - id: end-of-file-fixer
      - id: trailing-whitespace
  - repo: local
    hooks:
      - id: prettier
        name: prettier
        entry: npm run format
        language: system
        files: \.(js|jsx|ts|tsx|json|css|scss|md|html|yaml|yml)$
        pass_filenames: false
        always_run: true
      - id: eslint
        name: eslint
        entry: npm run lint
        language: system
        files: \.(js|jsx|ts|tsx)$
        pass_filenames: false
        always_run: true
      - id: typecheck
        name: typecheck
        entry: npm run typecheck
        language: system
        files: \.(ts|tsx)$
        pass_filenames: false
        always_run: true
