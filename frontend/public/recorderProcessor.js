// public/recorderProcessor.js
class RecorderProcessor extends AudioWorkletProcessor {
  // Helper function to convert float32 to int16
  _float32ToInt16(buffer) {
    let l = buffer.length;
    const output = new Int16Array(l);
    while (l--) {
      output[l] = Math.min(1, buffer[l]) * 0x7fff;
    }
    return output.buffer;
  }

  process(inputs, outputs, parameters) {
    const input = inputs[0];
    if (input.length > 0) {
      const data = input[0];
      // Send the data directly, converted to Int16
      this.port.postMessage(this._float32ToInt16(data));
    }
    return true; // Keep the processor alive
  }
}

registerProcessor('recorder-processor', RecorderProcessor);
