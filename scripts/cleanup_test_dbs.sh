#!/bin/bash

# This script drops all temporary test databases with names starting with "test_".
# It's useful for cleaning up after a test run, in case any test databases
# were not properly removed automatically.

set -e

DB_USER="user" # Default user from .env.test

echo "🧹 Searching for leftover test databases (named 'test_*')..."

DATABASES_TO_DROP=$(PGPASSWORD="password" psql -h localhost -p 5432 -U "$DB_USER" -d postgres -t -c "SELECT datname FROM pg_database WHERE datistemplate = false AND datname LIKE 'test_%';")

if [ -z "$DATABASES_TO_DROP" ]; then
    echo "✅ No leftover test databases found. All clean!"
    exit 0
fi

echo "🗑️ Found leftover test databases. Preparing to drop them."

echo "$DATABASES_TO_DROP" | while read -r dbname; do
    if [ -n "$dbname" ]; then
        echo "   -> Dropping database: $dbname"
        PGPASSWORD="password" psql -h localhost -p 5432 -U "$DB_USER" -d postgres -c "DROP DATABASE \"$dbname\" WITH (FORCE);"
    fi
done

echo "✅ Successfully cleaned up all leftover test databases."
