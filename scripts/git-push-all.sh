#!/bin/bash

# This script pushes changes for the current branch in all submodules and the parent repository.

set -e # Exit immediately if a command exits with a non-zero status.

# 1. Get the current branch name
BRANCH_NAME=$(git rev-parse --abbrev-ref HEAD)

if [ -z "$BRANCH_NAME" ]; then
  echo "❌ Error: Could not determine the current branch."
  exit 1
fi

echo "--- Pushing branch '$BRANCH_NAME' in submodules ---"

# 2. Push changes in each submodule
git submodule foreach '
  echo "Pushing in $name..."
  if git push origin "'"$BRANCH_NAME"'"; then
    echo "✅ Submodule $name pushed successfully."
  else
    echo "❌ Failed to push submodule: $name."
    exit 1 # Exit if submodule push fails
  fi
'

# 3. Push changes in the parent repository
echo ""
echo "--- Pushing branch '$BRANCH_NAME' in parent repository ---"
if git push origin "$BRANCH_NAME"; then
  echo "✅ All changes have been pushed."
else
  echo "❌ Failed to push parent repository."
  exit 1 # Exit if parent push fails
fi
