#!/bin/bash

# This script commits changes in all submodules and the parent repository.
# It uses an environment variable for the commit message and passes all additional arguments to git commit.

set -e # Exit immediately if a command exits with a non-zero status.

COMMIT_ARGS="$@"

# If --no-verify is passed, set GIT_SKIP_HOOKS to bypass all hooks
if [[ " ${COMMIT_ARGS} " =~ " --no-verify " ]]; then
  export GIT_SKIP_HOOKS=1
fi

export COMMIT_ARGS # Export COMMIT_ARGS for submodules

if [ -z "$COMMIT_MESSAGE" ]; then
  echo "❌ Error: COMMIT_MESSAGE environment variable is not set."
  echo "This script should be called from the Makefile."
  exit 1
fi

# 1. Go into each submodule, add all changes, and commit.
echo "--- Committing in submodules ---"
export COMMIT_MESSAGE
git submodule foreach --recursive '
  # Stage all changes before attempting commit
  git add .

  # Check if there are any changes to commit (including those staged by pre-commit hooks)
  if ! git diff-index --quiet HEAD --; then
    if git commit ${COMMIT_ARGS} -m "${COMMIT_MESSAGE}"; then
      echo "✅ Committed changes in $name"
    else
      echo "Pre-commit hooks might have modified files in $name. Re-staging and retrying commit..."
      git add . # Re-stage changes made by pre-commit hooks
      if git commit ${COMMIT_ARGS} -m "${COMMIT_MESSAGE}"; then
        echo "✅ Successfully re-committed changes in $name after pre-commit hook modifications."
      else
        echo "❌ Failed to commit changes in $name even after re-staging. Manual intervention might be needed."
        exit 1 # Exit if submodule commit fails
      fi
    fi
  else
    echo "No changes to commit in $name"
  fi
'

# 2. Add the updated submodule references in the parent repository.
echo ""
echo "--- Committing in parent repository ---"
git add .

# 3. Commit the changes in the parent repository.
if git commit ${COMMIT_ARGS} -m "${COMMIT_MESSAGE}"; then
  echo "✅ All changes have been committed."
else
  echo "❌ Failed to commit changes in the parent repository."
  exit 1 # Exit if parent commit fails
fi
