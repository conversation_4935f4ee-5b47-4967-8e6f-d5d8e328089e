#!/bin/bash

# This script pulls changes for the parent repository and all submodules,
# staying on the current branch.

set -e # Exit immediately if a command exits with a non-zero status.

echo "--- Pulling Parent Repository ---"
if git pull origin $(git rev-parse --abbrev-ref HEAD); then
  echo "✅ Parent repository pulled successfully."
else
  echo "❌ Failed to pull parent repository."
  exit 1
fi
echo ""

echo "--- Pulling Submodules ---"
git submodule foreach '
  echo ""
  echo "--> Pulling for submodule: $name"
  if git pull origin $(git rev-parse --abbrev-ref HEAD); then
    echo "✅ Submodule $name pulled successfully."
  else
    echo "❌ Failed to pull submodule: $name."
    exit 1 # Exit if submodule pull fails
  fi
'
