#!/bin/bash

# This script creates a timestamped zip archive of the current working directory,
# excluding git-ignored files, and places it in the 'drafts/' directory.
# An optional message can be provided as the first argument.

set -e # Exit immediately if a command exits with a non-zero status.

MESSAGE="$1"
TIMESTAMP=$(date +%Y%m%d-%H%M%S)

# Sanitize the message for use in a filename
MESSAGE_SLUG=$(echo "$MESSAGE" | tr -c 'a-zA-Z0-9_' '-' | sed -E 's/^-+|-+$//g')

mkdir -p drafts

if [ -n "$MESSAGE_SLUG" ]; then
  ZIP_FILE="drafts/wip-${TIMESTAMP}-${MESSAGE_SLUG}.zip"
else
  ZIP_FILE="drafts/wip-${TIMESTAMP}.zip"
fi

echo "--- Creating timestamped zip archive of current work in 'drafts/' ---"
echo "Creating ${ZIP_FILE}"

# Get files from the main repository, excluding submodules themselves
FILES=$(git ls-files -c -o --exclude-standard)

# Get files from submodules
SUBMODULE_PATHS=$(git config --file .gitmodules --get-regexp path | awk '{ print $2 }')

for SUBMODULE_PATH in $SUBMODULE_PATHS; do
  if [ -d "$SUBMODULE_PATH" ]; then
    echo "Including files from submodule: $SUBMODULE_PATH"
    (cd "$SUBMODULE_PATH" && git ls-files -c -o --exclude-standard) | sed "s|^|${SUBMODULE_PATH}/|" >> /tmp/submodule_files.txt
  fi
done

if [ -f "/tmp/submodule_files.txt" ]; then
  FILES="${FILES}
$(cat /tmp/submodule_files.txt)"
  rm /tmp/submodule_files.txt
fi

echo -e "$FILES" | zip "${ZIP_FILE}" -@

echo "✅ Created ${ZIP_FILE}"
