#!/bin/bash
set -e

export POSTGRES_USER='user' 

TIMESTAMP=$(date +%Y%m%d-%H%M%S)
# Use the first argument as the backup directory, or default to /var/lib/postgresql/backups
BACKUP_DIR="${1:-/var/lib/postgresql/backups}"

# Create the backup directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

# Wait for PostgreSQL to be ready
until pg_isready -h localhost -p 5432 -U "$POSTGRES_USER"; do
  echo "Waiting for postgres..."
  sleep 1
done

# Dump homeservice database
echo "Dumping homeservice database..."
PGPASSWORD="password" pg_dump -Fp -h localhost -U "$POSTGRES_USER" -d homeservice > "$BACKUP_DIR/homeservice_dump_$TIMESTAMP.sql"

# Dump n8n database
echo "Dumping n8n database..."
PGPASSWORD="password" pg_dump -Fp -h localhost -U "$POSTGRES_USER" -d n8n > "$BACKUP_DIR/n8n_dump_$TIMESTAMP.sql"

# Dump test_db database
echo "Dumping test_db database..."
PGPASSWORD="password" pg_dump -Fp -h localhost -U "$POSTGRES_USER" -d test_db > "$BACKUP_DIR/test_db_dump_$TIMESTAMP.sql"

echo "All databases dumped successfully to $BACKUP_DIR"