#!/bin/bash
set -e

export POSTGRES_USER='user' 
export PGPASSWORD="password"

# Use the first argument as the backup directory, or default to /var/lib/postgresql/backups
BACKUP_DIR="${1:-/var/lib/postgresql/backups}"

# Wait for PostgreSQL to be ready
until pg_isready -h localhost -p 5432 -U "$POSTGRES_USER"; do
  echo "Waiting for postgres..."
  sleep 1
done

# Function to terminate connections and drop/create database
restore_db() {
    DB_NAME=$1
    DUMP_FILE_PATTERN=$2

    echo "Terminating connections to $DB_NAME and dropping/creating database..."
    psql -h localhost -U "$POSTGRES_USER" -d postgres -c "SELECT pg_terminate_backend(pg_stat_activity.pid) FROM pg_stat_activity WHERE pg_stat_activity.datname = '$DB_NAME' AND pid <> pg_backend_pid();"
    psql -h localhost -U "$POSTGRES_USER" -d postgres -c "DROP DATABASE IF EXISTS $DB_NAME;"
    psql -h localhost -U "$POSTGRES_USER" -d postgres -c "CREATE DATABASE $DB_NAME;"

    echo "Restoring $DB_NAME database..."
    LATEST_DUMP=$(ls -t "$BACKUP_DIR"/$DUMP_FILE_PATTERN | head -n 1)
    if [ -z "$LATEST_DUMP" ]; then
        echo "No $DB_NAME dump found. Skipping."
    else
        cat "$LATEST_DUMP" | grep -v "SET transaction_timeout" | psql -h localhost -U "$POSTGRES_USER" -d "$DB_NAME"
    fi
}

# Restore homeservice database
restore_db homeservice "homeservice_dump_*.sql"

# Restore n8n database
restore_db n8n "n8n_dump_*.sql"

# Restore test_db database
restore_db test_db "test_db_dump_*.sql"

echo "All databases restored successfully."