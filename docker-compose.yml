services:
  postgres:
    image: pgvector/pgvector:pg15
    environment:
      POSTGRES_DB: homeservice
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/dbs:/docker-entrypoint-initdb.d
      - ./data:/var/lib/postgresql/backups
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U user -d homeservice"]
      interval: 5s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  backend:
    build: ./backend
    env_file:
      - ./backend/.env
    ports:
      - "8000:8000"
    command:
      [
        "uvicorn",
        "src.main:app",
        "--host",
        "0.0.0.0",
        "--port",
        "8000",
        "--reload",
      ]
    volumes:
      - ./backend:/app
    depends_on:
      - postgres
      - redis

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    env_file:
      - ./frontend/.env
    ports:
      - "3000:3000"
    command: ["npm", "run", "dev", "--", "--port", "3000"]
    volumes:
      # Mount specific source code directories instead of the whole app
      - ./frontend/src:/app/src
      - ./frontend/public:/app/public
      # Use named volumes for dependencies and build artifacts
      - frontend_node_modules:/app/node_modules
      - frontend_next:/app/.next
    environment:
      - WATCHPACK_POLLING=true
    depends_on:
      - backend

  # landing:
  #   build:
  #     context: ./landing
  #     dockerfile: Dockerfile.dev
  #   env_file:
  #     - ./landing/.env
  #   ports:
  #     - "5173:5173"
  #   command: ["npm", "run", "dev", "--", "--port", "5173"]
  #   volumes:
  #     - ./landing:/app
  #   depends_on:
  #     - backend

  n8n:
    image: n8nio/n8n:latest
    ports:
      - "5678:5678"
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=password
      - N8N_HOST=localhost
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - WEBHOOK_URL=http://localhost:5678/
      - GENERIC_TIMEZONE=UTC
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_DATABASE=n8n
      - DB_POSTGRESDB_USER=user
      - DB_POSTGRESDB_PASSWORD=password
    volumes:
      - n8n_data:/home/<USER>/.n8n
    depends_on:
      postgres:
        condition: service_healthy

  pgweb:
    image: sosedoff/pgweb
    ports:
      - "8081:8081"
    environment:
      PGWEB_DATABASE_URL: ****************************************/homeservice?sslmode=disable
    depends_on:
      postgres:
        condition: service_healthy

volumes:
  postgres_data:
  redis_data:
  frontend_node_modules:
  frontend_next:
  n8n_data:
