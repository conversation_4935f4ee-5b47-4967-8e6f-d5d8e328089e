# Project Plan: Voice-First AI Agent for Home Services

This document outlines the plan to build a voice-first AI agent for home services, focusing on HVAC and plumbing as the initial niche.

## 0. Executive Summary

- **Goal:** Ship a voice agent that answers inbound calls, qualifies leads, books appointments, handles FAQs, and escalates to human agents when necessary. The system will be templatized for rapid per-customer rollouts.
- **Niche:** HVAC & Plumbing.
- **Wedge:** The AI will answer calls after a specific number of rings and during after-hours. During business hours, it will triage calls, book appointments, or hand them off to a human.
- **Stack:**
  - **Backend:** FastAPI, LangGraph, OpenAI/Whisper, ElevenLabs, Twilio, n8n, Postgres/pgvector, Redis. All backend code will be in the `backend/` directory.
  - **Frontend (Ops Console):** Next.js. All frontend code will be in the `frontend/` directory.

## 1. Phased Implementation Plan

### Phase 1: Skeleton and Core Infrastructure (Week 1)

- **Objective:** Set up the basic infrastructure and a simple echo-bot to validate the end-to-end voice pipeline.
- **Tasks:**
  1.  **Project Setup:**
      - Initialize the monorepo structure with `backend` and `frontend` directories.
      - Set up `docker-compose.yml` for local development (Postgres, Redis).
  2.  **Backend (`backend/`):**
      - Initialize a FastAPI application.
      - Create a main endpoint for Twilio Voice webhooks.
      - Implement a simple TwiML response to accept the call and stream audio.
      - Set up a WebSocket endpoint to receive Twilio media streams.
      - Integrate a basic ASR (Whisper) to transcribe the audio stream.
      - Integrate a basic TTS (ElevenLabs) to respond.
      - Create a simple "echo" functionality: the agent transcribes the user's speech and echoes it back.
      - Set up Redis for ephemeral call state.
      - Set up Postgres with the initial data model (customers, numbers).
  3.  **Frontend (`frontend/`):**
      - Initialize a Next.js application for the Ops Console.
      - Create a stub page for customer and number configuration.
- **Demo:** Make a live phone call that the AI answers, transcribes the user's speech, and speaks it back.

### Phase 2: Booking, Policies, and Basic Intelligence (Week 2)

- **Objective:** Implement the core appointment booking logic and escalation policies.
- **Tasks:**
  1.  **Backend (`backend/`):**
      - Integrate LangGraph for managing conversation flow.
      - **Nodes:** `InputGate`, `StateLoad`, `IntentDetect`, `FAQ/RAG`, `ResponsePlanner`, `TTS`, `StatePersist`.
      - Implement RAG for FAQ handling using `pgvector` on a seeded knowledge base.
      - Implement a `BookSlot` tool that connects to a mock calendar (or Google Calendar API).
      - Implement escalation rules (keyword-based, sentiment detection).
      - Implement a `HandoffHuman` tool using Twilio `<Dial>`.
      - Set up n8n webhook endpoint for post-call automations.
  2.  **n8n Workflows:**
      - Create a workflow to handle appointment creation: update CRM (mock), send confirmation SMS/email.
      - Create a workflow to asynchronously embed call transcripts for the knowledge base.
  3.  **Frontend (`frontend/`):**
      - Build out the UI for managing customer knowledge (uploading Markdown/URLs).
      - Implement a basic call log viewer.
- **Demo:** A live call where the agent can answer a FAQ and book an appointment, which then sends a confirmation SMS.

### Phase 3: Templatization and Customer Configuration (Week 3)

- **Objective:** Make the system configurable on a per-customer basis.
- **Tasks:**
  1.  **Backend (`backend/`):**
      - Refactor the agent to pull all customer-specific configuration (prompts, policies, integrations) from the Postgres database at the start of a call.
      - Cache customer configs in Redis for low-latency access.
  2.  **Frontend (`frontend/`):**
      - Build out the Ops Console forms for:
        - Customer details.
        - Phone number configuration (ring count, after-hours policy).
        - System prompts and voice selection.
        - Service details, pricing bands, coverage areas.
        - Connecting integrations (OAuth for Google Calendar).
  3.  **Data Model:**
      - Flesh out the Postgres schema to include all configuration tables (`prompts`, `calendars`, `knowledge_items`, etc.).
- **Demo:** Onboard a new "customer" entirely through the Ops Console, and have the agent for that customer behave according to the specified configuration.

### Phase 4: Reliability and Multi-Channel (Week 4)

- **Objective:** Harden the system and add SMS/WhatsApp as a communication channel.
- **Tasks:**
  1.  **Backend (`backend/`):**
      - Implement Twilio SMS/WhatsApp webhook that routes to the same LangGraph agent.
      - Add robust error handling and observability (structured logs, OpenTelemetry traces).
  2.  **Frontend (`frontend/`):**
      - Develop the ROI dashboard (bookings, AHT, handoff rate, estimated cost/booking).
  3.  **Testing:**
      - Create a QA suite with scripted calls to test common and edge-case scenarios.
- **Demo:** Show a conversation that starts on a voice call and sends a follow-up quote via SMS.

### Phase 5: Go-Live and Scaling (Week 5+)

- **Objective:** Prepare for the first paying customers.
- **Tasks:**
  1.  **Security & Compliance:**
      - Implement PII redaction in logs and transcripts.
      - Add pre-call "this call is recorded" notice.
      - Secure all API endpoints and manage secrets properly.
  2.  **Onboarding:**
      - Finalize the per-customer onboarding checklist.
      - Create content packs for HVAC and plumbing FAQs.
  3.  **Ops:**
      - Set up monitoring and alerting for latency and error rates.
      - Perform load testing.

## 2. Directory Structure

```
/
├── backend/
│   ├── src/
│   │   ├── main.py             # FastAPI app
│   │   ├── agent/              # LangGraph agent logic
│   │   │   ├── graph.py
│   │   │   ├── nodes.py
│   │   │   └── tools.py
│   │   ├── config/             # Customer configuration loading
│   │   ├── data/               # Data models (Pydantic)
│   │   ├── services/           # External service integrations (Twilio, OpenAI, etc.)
│   │   └── webhooks/           # Twilio and n8n webhook handlers
│   ├── Dockerfile
│   ├── pyproject.toml
│   └── ...
├── frontend/
│   ├── src/
│   │   ├── app/                # Next.js app router
│   │   │   ├── (auth)/
│   │   │   ├── (dashboard)/
│   │   │   │   ├── customers/
│   │   │   │   ├── calls/
│   │   │   │   └── analytics/
│   │   │   └── layout.tsx
│   │   ├── components/         # UI components
│   │   ├── lib/                # Utility functions
│   │   └── services/           # API client
│   ├── package.json
│   └── ...
├── docker-compose.yml
├── PROJECT_PLAN.md
└── ...
```

This plan will be executed sequentially. I will start with Phase 1.
