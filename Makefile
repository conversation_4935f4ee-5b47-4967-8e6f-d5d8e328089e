# Makefile for convenient git operations

.DEFAULT_GOAL := help

help:
	@echo "Makefile Commands:"
	@echo "  make commit m=\"...\"   - Commit changes in submodules and the parent repository."
	@echo "                           Example: make commit m=\"feat: Implement new feature\""
	@echo "  make push              - Push the current branch to origin in all submodules and the parent."
	@echo "  make pull              - Pull the current branch from origin in all submodules and the parent."
	@echo "  make status            - Show git status for the parent and all submodules."
	@echo "  make dump              - Dumps the database to data/homeservice_dump_YYYYMMDD-HHMMSS.sqlc. Example: data/homeservice_dump_20250820-210724.sqlc."
	@echo "  make restore           - Restores the database from data/homeservice_dump_<latest>.sqlc."
	@echo "  make precommit         - Runs all pre-commit checks."
	@echo "  make clean-tests       - Cleans up leftover test databases and other test artifacts."
	@echo "  make commit-no-verify  m=\"...\"     - Commit changes with a default message and --no-verify."
	@echo "  make draft m="..."    	- Create a timestamped zip archive of current work in 'drafts/' with an optional message."
.PHONY: help commit push pull status dump restore precommit clean-tests commit-no-verify draft


# Use: make commit m="Your commit message"
commit:precommit
	@if [ -z "$(m)" ]; then \
		echo "Error: Commit message is empty."; \
		echo "Usage: make commit \"Your commit message\""; \
		exit 1; \
	fi
	@COMMIT_MESSAGE="$(m)" bash scripts/git-commit-all.sh

push:
	@echo "--- Pushing all changes to origin ---"
	@bash scripts/git-push-all.sh

pull:
	@echo "--- Pulling all changes from origin ---"
	@bash scripts/git-pull-all.sh

status:
	@bash scripts/git-status-all.sh

dump:
	@echo "--- Dumping database ---"
	@bash scripts/dbs/03_dump_dbs.sh ./data

restore:
	@echo "--- Restoring databases ---"
	@bash scripts/dbs/02_restore_dbs.sh ./data

precommit:
	@echo "--- Running pre-commit checks ---"
	@set -e;
	pre-commit run --all-files;
	cd backend && pre-commit run --all-files && cd -
	cd frontend && pre-commit run --all-files && cd -
	@echo "--- ✅ Pre-commit checks complete ---"

clean-tests:
	@echo "--- Cleaning up test artifacts ---"
	@bash scripts/cleanup_test_dbs.sh

commit-no-verify:
	@if [ -z "$(m)" ]; then \
		echo "Error: Commit message is empty."; \
		echo "Usage: make commit \"Your commit message\""; \
		exit 1; \
	fi
	@echo "--- Creating automated draft commit ---"
	@bash -c 'COMMIT_MESSAGE="Draft(automated): $(m) -- NO VERIFY" "$(CURDIR)/scripts/git-commit-all.sh" --no-verify'



draft:
	@echo "--- Creating timestamped zip archive of current work in 'drafts/' ---"
	@bash scripts/zip-wip.sh "$(m)"

