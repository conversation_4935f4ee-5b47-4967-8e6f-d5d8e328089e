#!/bin/bash

# Define common browser headers
HEADERS="User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36"
ACCEPT_HEADER="Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9"
ACCEPT_ENCODING="Accept-Encoding: gzip, deflate, br"
ACCEPT_LANGUAGE="Accept-Language: en-US,en;q=0.9"
CONNECTION="Connection: keep-alive"

# Base directory for images
PUBLIC_DIR="/home/<USER>/Projects/leanchain/hs/frontend/public"

echo "Downloading images with browser headers..."

# Industries page icons
curl -L -o "$PUBLIC_DIR/roofing-icon.jpg" "https://images.unsplash.com/photo-1582719503292-c7550079768b?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" -H "$HEADERS" -H "$ACCEPT_HEADER" -H "$ACCEPT_ENCODING" -H "$ACCEPT_LANGUAGE" -H "$CONNECTION"
curl -L -o "$PUBLIC_DIR/hvac-icon.jpg" "https://images.unsplash.com/photo-1622016290206-91129079427d?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" -H "$HEADERS" -H "$ACCEPT_HEADER" -H "$ACCEPT_ENCODING" -H "$ACCEPT_LANGUAGE" -H "$CONNECTION"
curl -L -o "$PUBLIC_DIR/plumbing-icon.jpg" "https://images.unsplash.com/photo-1580903362372-e07773797769?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" -H "$HEADERS" -H "$ACCEPT_HEADER" -H "$ACCEPT_ENCODING" -H "$ACCEPT_LANGUAGE" -H "$CONNECTION"
curl -L -o "$PUBLIC_DIR/electrical-icon.jpg" "https://images.unsplash.com/photo-1587840171670-8b7301e67c7a?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" -H "$HEADERS" -H "$ACCEPT_HEADER" -H "$ACCEPT_ENCODING" -H "$ACCEPT_LANGUAGE" -H "$CONNECTION"
curl -L -o "$PUBLIC_DIR/landscaping-icon.jpg" "https://images.unsplash.com/photo-1518531932812-bfcefd3c665a?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" -H "$HEADERS" -H "$ACCEPT_HEADER" -H "$ACCEPT_ENCODING" -H "$ACCEPT_LANGUAGE" -H "$CONNECTION"
curl -L -o "$PUBLIC_DIR/pest-control-icon.jpg" "https://images.unsplash.com/photo-1581092919561-dc4377879706?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" -H "$HEADERS" -H "$ACCEPT_HEADER" -H "$ACCEPT_ENCODING" -H "$ACCEPT_LANGUAGE" -H "$CONNECTION"

# Hero images (already downloaded, but re-downloading with headers for consistency)
curl -L -o "$PUBLIC_DIR/hvac-hero.jpg" "https://images.unsplash.com/photo-1622016290206-91129079427d?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" -H "$HEADERS" -H "$ACCEPT_HEADER" -H "$ACCEPT_ENCODING" -H "$ACCEPT_LANGUAGE" -H "$CONNECTION"
curl -L -o "$PUBLIC_DIR/plumbing-hero.jpg" "https://images.unsplash.com/photo-1580903362372-e07773797769?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" -H "$HEADERS" -H "$ACCEPT_HEADER" -H "$ACCEPT_ENCODING" -H "$ACCEPT_LANGUAGE" -H "$CONNECTION"
curl -L -o "$PUBLIC_DIR/electrical-hero.jpg" "https://images.unsplash.com/photo-1587840171670-8b7301e67c7a?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" -H "$HEADERS" -H "$ACCEPT_HEADER" -H "$ACCEPT_ENCODING" -H "$ACCEPT_LANGUAGE" -H "$CONNECTION"
curl -L -o "$PUBLIC_DIR/landscaping-hero.jpg" "https://images.unsplash.com/photo-1518531932812-bfcefd3c665a?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" -H "$HEADERS" -H "$ACCEPT_HEADER" -H "$ACCEPT_ENCODING" -H "$ACCEPT_LANGUAGE" -H "$CONNECTION"
curl -L -o "$PUBLIC_DIR/pest-control-hero.jpg" "https://images.unsplash.com/photo-1581092919561-dc4377879706?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" -H "$HEADERS" -H "$ACCEPT_HEADER" -H "$ACCEPT_ENCODING" -H "$ACCEPT_LANGUAGE" -H "$CONNECTION"

echo "Downloads complete. Please check the '$PUBLIC_DIR' directory."
