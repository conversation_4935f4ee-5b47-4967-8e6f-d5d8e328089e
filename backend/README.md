# Backend Service

This is the backend service for the <PERSON><PERSON> project.

## Features

- FastAPI application
- Modular architecture
- Agentic orchestration with LangGraph
- Real-time communication with WebSockets

## Getting Started

### Prerequisites

- Python 3.12+
- Poetry
- Docker

### Installation

1.  **Install dependencies:**
    ```bash
    uv install
    ```

2.  **Set up environment variables:**
    Copy `.env.example` to `.env` and fill in the required values.

3.  **Run the application:**
    ```bash
    uvicorn src.main:app --reload
    ```

## Observability with LangSmith

This project is integrated with [LangSmith](https://www.langchain.com/langsmith) for tracing and debugging of the agentic components. To enable it, you need to set the following environment variables in your `.env` file:

```
LANGCHAIN_TRACING_V2=true
LANGCHAIN_ENDPOINT="https://api.smith.langchain.com"
LANGCHAIN_API_KEY="YOUR_LANGSMITH_API_KEY"
LANGCHAIN_PROJECT="YOUR_PROJECT_NAME" # Optional: "default" is used if not set
```

Once these variables are set, all agent and LLM runs will be automatically traced in your LangSmith project, providing a detailed, visual representation of the execution flow. This is highly recommended for development and debugging.
