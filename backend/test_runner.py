#!/usr/bin/env python3
"""
Simple test runner to verify our e2e implementation works.
"""

import asyncio
import sys
import os
import logging

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "src"))

from fastapi.testclient import TestClient

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_basic_functionality():
    """Test basic functionality without full pytest setup."""
    try:
        # Import main components
        from src.main import app

        logger.info("✓ Basic imports successful")

        # Test FastAPI app creation
        client = TestClient(app)

        # Test basic endpoint
        response = client.get("/health")
        if response.status_code == 200:
            logger.info("✓ Health endpoint working")
        else:
            logger.warning(f"Health endpoint returned {response.status_code}")

        # Test call initiation endpoint structure
        try:
            response = client.post(
                "/api/call",
                json={
                    "type": "text",
                    "custom_prompt": "Test prompt",
                    "custom_first_message": "Test message",
                },
            )
            logger.info(f"✓ Call endpoint accessible (status: {response.status_code})")
        except Exception as e:
            logger.warning(f"Call endpoint test failed: {e}")

        logger.info("✓ Basic functionality test completed")
        return True

    except Exception as e:
        logger.error(f"✗ Basic functionality test failed: {e}")
        return False


async def test_service_imports():
    """Test that our service implementations can be imported."""
    try:
        logger.info("✓ Service function imports successful")

        logger.info("✓ Test helper imports successful")

        return True

    except Exception as e:
        logger.error(f"✗ Service imports failed: {e}")
        return False


async def test_orchestrator_imports():
    """Test orchestrator components."""
    try:
        from orchestrator.config_loader import load_agent_config

        logger.info("✓ Orchestrator imports successful")

        # Test config loading
        config = load_agent_config("default_agent")
        logger.info(f"✓ Agent config loaded: {config.workflow.name}")

        return True

    except Exception as e:
        logger.error(f"✗ Orchestrator imports failed: {e}")
        return False


async def main():
    """Run all tests."""
    logger.info("Starting basic functionality tests...")

    tests = [
        ("Basic Functionality", test_basic_functionality),
        ("Service Imports", test_service_imports),
        ("Orchestrator Imports", test_orchestrator_imports),
    ]

    results = []
    for test_name, test_func in tests:
        logger.info(f"\n--- Running {test_name} ---")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"Test {test_name} crashed: {e}")
            results.append((test_name, False))

    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("TEST SUMMARY")
    logger.info("=" * 50)

    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1

    logger.info(f"\nPassed: {passed}/{len(results)}")

    if passed == len(results):
        logger.info("🎉 All tests passed! The implementation looks good.")
        return 0
    else:
        logger.warning("⚠️  Some tests failed. Check the logs above.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
