from contextlib import asynccontextmanager
import logging
import socket
import time
import json
import uuid

import redis.asyncio as aioredis
from fastapi import Fast<PERSON><PERSON>, Request, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi_cache import FastAPICache
from fastapi_cache.backends.redis import RedisBackend as CacheRedisBackend
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from core.config import get_settings
from core.db.database import AsyncSessionLocal as SessionLocal
from core.middleware.request_context_middleware import RequestContextMiddleware
from core.middleware.request_logging_middleware import request_logging_middleware
from core.utils.logging import setup_logging
from modules.agents.router import router as agents_router
from modules.auth.router import router as auth_router
from modules.customers import crud as customer_crud
from modules.customers import schemas as customer_schemas
from modules.customers.router import router as customers_router
from modules.dashboard.router import router as dashboard_router
from modules.call.router import router as call_router
from modules.jobs.router import router as jobs_router
from modules.performance.router import router as performance_router
from modules.test_center.router import router as node_tester_router
from orchestrator.graph import AgentGraph
from orchestrator.template_loader import load_agent_template
from orchestrator.router import router as orchestrator_router
from modules.templates.router import router as templates_router

# Setup logging as early as possible
setup_logging()

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    settings = get_settings()
    # Startup events
    host = "localhost"
    port = settings.PORT

    try:
        network_ip = socket.gethostbyname(socket.gethostname())
    except socket.gaierror:
        network_ip = "<network_ip>"

    logger.info("\n" + "=" * 40)
    logger.info("🚀 Backend Server Started")
    logger.info(f"- Local:   http://{host}:{port}")
    logger.info(f"- Network: http://{network_ip}:{port}")
    logger.info("=" * 40 + "\n")

    async with SessionLocal() as db:
        test_customer = await customer_crud.get_customer_by_domain(
            db, domain="test.customer"
        )
        if not test_customer:
            logger.info("Creating default test customer.")
            await customer_crud.create_customer(
                db,
                customer_schemas.CustomerCreate(
                    name="Test Customer", domain="test.customer"
                ),
            )
        else:
            logger.info("Default test customer already exists.")

    cache_redis_client = aioredis.from_url(
        f"redis://{settings.REDIS_HOST}:{settings.REDIS_PORT}/{settings.REDIS_DB}",
        encoding="utf8",
        decode_responses=True,
    )
    FastAPICache.init(CacheRedisBackend(cache_redis_client), prefix="fastapi-cache")
    logger.info("FastAPICache initialized.")

    yield

    await cache_redis_client.aclose()
    logger.info("FastAPICache closed.")




app = FastAPI(lifespan=lifespan)

# Add the request context middleware
app.add_middleware(RequestContextMiddleware)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://localhost:5173",
        "http://localhost:8000",
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request Logging Middleware
app.middleware("http")(request_logging_middleware)


app.include_router(customers_router, prefix="/api")
app.include_router(auth_router, prefix="/api")
app.include_router(jobs_router, prefix="/api")
app.include_router(dashboard_router, prefix="/api")
app.include_router(call_router, prefix="/api")
app.include_router(performance_router, prefix="/api")
app.include_router(node_tester_router, prefix="/api")
app.include_router(agents_router, prefix="/api")
app.include_router(orchestrator_router, prefix="/api")
app.include_router(templates_router, prefix="/api")


@app.get("/")
async def read_root():
    return {"message": "Hello from Fieson backend!"}


@app.get("/health")
async def health_check():
    return {"status": "ok"}


@app.websocket("/ws")
async def websocket(websocket: WebSocket):
    await websocket.accept()
    await websocket.send_json({"msg": "Hello WebSocket"})
    try:
        while True:
            data = await websocket.receive_json()
            await websocket.send_json({"msg": f"Message text was: {data['text']}"})
    except WebSocketDisconnect:
        logger.info("Client disconnected")
