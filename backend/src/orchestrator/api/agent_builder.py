from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, WebSocket, WebSocketDisconnect
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Dict, Any, Optional
import json
import uuid
from datetime import datetime

from core.db.database import get_db
from orchestrator.schemas import (
    AgentConfig, Workflow, NodeConfig, AgentType, 
    AgentCategory, DSPyModuleType, MemoryType, ExecutionMetrics,
    AgentExecutionState
)
from modules.templates.schemas import AgentTemplateResponse
from modules.templates import crud as templates_crud
from orchestrator.models import AgentState, NodeExecutionContext
from orchestrator.services.memory.mem0_service import EnhancedMemoryService
from orchestrator.dspy.modules import DSPyModuleFactory, get_available_dspy_modules
from orchestrator.services.llm_integrations.llm_factory import get_llm_service
from core.config import get_settings

router = APIRouter(prefix="/agent-builder", tags=["agent-builder"])


class AgentBuilderService:
    """Service for managing agent configurations and execution."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.active_executions: Dict[str, AgentExecutionState] = {}
        self.websocket_connections: Dict[str, WebSocket] = {}
    
    async def create_agent_config(self, workflow: Workflow) -> AgentConfig:
        """Create a new agent configuration."""
        # Validate workflow
        await self._validate_workflow(workflow)
        
        # Create agent config
        agent_config = AgentConfig(workflow=workflow)
        
        # Save to database (implement database operations)
        # await self._save_agent_config(agent_config)
        
        return agent_config
    
    async def get_agent_templates(self) -> List[AgentTemplateResponse]:
        """Get available agent templates."""
        return await templates_crud.get_templates(self.db)
    
    async def _validate_workflow(self, workflow: Workflow):
        """Validate workflow configuration."""
        # Check if start node exists
        start_node_exists = any(node.id == workflow.start_node_id for node in workflow.nodes)
        if not start_node_exists:
            raise HTTPException(
                status_code=400, 
                detail=f"Start node '{workflow.start_node_id}' not found in workflow nodes"
            )
        
        # Validate node connections
        for node in workflow.nodes:
            if node.id in workflow.connections.root:
                # Validate connection targets exist
                connections = workflow.connections.root[node.id]
                if hasattr(connections, 'main'):
                    for target in connections.main:
                        if target.node != "END" and not any(n.id == target.node for n in workflow.nodes):
                            raise HTTPException(
                                status_code=400,
                                detail=f"Connection target '{target.node}' not found in workflow nodes"
                            )
    
    
    
    async def execute_agent(
        self, 
        agent_config: AgentConfig, 
        input_data: Dict[str, Any],
        execution_id: Optional[str] = None
    ) -> AgentExecutionState:
        """Execute an agent workflow."""
        
        execution_id = execution_id or str(uuid.uuid4())
        
        # Create execution state
        execution_state = AgentExecutionState(
            execution_id=execution_id,
            agent_id=agent_config.workflow.id,
            created_at=datetime.utcnow().isoformat()
        )
        
        self.active_executions[execution_id] = execution_state
        
        try:
            # Execute workflow
            await self._execute_workflow(agent_config.workflow, input_data, execution_state)
        except Exception as e:
            execution_state.global_state["error"] = str(e)
            # Notify via WebSocket if connected
            await self._notify_execution_update(execution_id, {
                "type": "error",
                "message": str(e),
                "execution_id": execution_id
            })
        
        return execution_state
    
    async def _execute_workflow(
        self, 
        workflow: Workflow, 
        input_data: Dict[str, Any],
        execution_state: AgentExecutionState
    ):
        """Execute workflow nodes in sequence."""
        
        current_node_id = workflow.start_node_id
        execution_state.current_node_id = current_node_id
        
        while current_node_id and current_node_id != "END":
            # Find current node
            current_node = next((n for n in workflow.nodes if n.id == current_node_id), None)
            if not current_node:
                raise Exception(f"Node {current_node_id} not found")
            
            # Execute node
            node_result = await self._execute_node(current_node, input_data, execution_state)
            
            # Update execution path
            execution_state.execution_path.append(current_node_id)
            
            # Notify via WebSocket
            await self._notify_execution_update(execution_state.execution_id, {
                "type": "node_completed",
                "node_id": current_node_id,
                "result": node_result,
                "execution_id": execution_state.execution_id
            })
            
            # Determine next node
            current_node_id = self._get_next_node(workflow, current_node_id, node_result)
            execution_state.current_node_id = current_node_id
    
    async def _execute_node(
        self, 
        node: NodeConfig, 
        input_data: Dict[str, Any],
        execution_state: AgentExecutionState
    ) -> Dict[str, Any]:
        """Execute a single node."""
        
        start_time = datetime.utcnow()
        
        # Create node execution context
        node_context = NodeExecutionContext(
            node_id=node.id,
            execution_id=execution_state.execution_id,
            input_data=input_data
        )
        
        try:
            # Execute based on node type
            if node.type == "llm":
                result = await self._execute_llm_node(node, input_data, execution_state)
            elif node.type == "dspyModule":
                result = await self._execute_dspy_node(node, input_data, execution_state)
            elif node.type == "memory":
                result = await self._execute_memory_node(node, input_data, execution_state)
            else:
                result = {"output": "Node type not implemented"}
            
            node_context.output_data = result
            
            # Create execution metrics
            end_time = datetime.utcnow()
            duration_ms = int((end_time - start_time).total_seconds() * 1000)
            
            metrics = ExecutionMetrics(
                node_id=node.id,
                start_time=start_time.isoformat(),
                end_time=end_time.isoformat(),
                duration_ms=duration_ms,
                status="completed",
                input_data=input_data,
                output_data=result
            )
            
            execution_state.node_metrics.append(metrics)
            
            return result
            
        except Exception as e:
            # Handle node execution error
            end_time = datetime.utcnow()
            duration_ms = int((end_time - start_time).total_seconds() * 1000)
            
            metrics = ExecutionMetrics(
                node_id=node.id,
                start_time=start_time.isoformat(),
                end_time=end_time.isoformat(),
                duration_ms=duration_ms,
                status="failed",
                error_message=str(e),
                input_data=input_data
            )
            
            execution_state.node_metrics.append(metrics)
            raise e
    
    async def _execute_llm_node(
        self, 
        node: NodeConfig, 
        input_data: Dict[str, Any],
        execution_state: AgentExecutionState
    ) -> Dict[str, Any]:
        """Execute an LLM node."""
        
        # Get LLM service
        settings = get_settings()
        llm_service = get_llm_service(settings)
        
        # Prepare messages
        system_prompt = node.system_prompt or "You are a helpful AI assistant."
        user_message = input_data.get("user_message", "")
        
        # Generate response
        response = await llm_service.get_chat_completion([
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_message}
        ])
        
        return {"response": response, "node_id": node.id}
    
    async def _execute_dspy_node(
        self, 
        node: NodeConfig, 
        input_data: Dict[str, Any],
        execution_state: AgentExecutionState
    ) -> Dict[str, Any]:
        """Execute a DSPy module node."""
        
        if not node.dspy_config:
            raise Exception("DSPy configuration missing for DSPy node")
        
        # Create DSPy module
        module = DSPyModuleFactory.create_module(
            node.dspy_config.module_type,
            {"signature": node.dspy_config.signature}
        )
        
        # Execute module
        result = module(**input_data)
        
        return {"result": result, "node_id": node.id}
    
    async def _execute_memory_node(
        self, 
        node: NodeConfig, 
        input_data: Dict[str, Any],
        execution_state: AgentExecutionState
    ) -> Dict[str, Any]:
        """Execute a memory node."""
        
        if not node.memory_config:
            raise Exception("Memory configuration missing for memory node")
        
        # Create memory service
        memory_service = EnhancedMemoryService(
            agent_id=execution_state.agent_id,
            user_id=input_data.get("user_id", "default"),
            memory_config=node.memory_config
        )
        
        # Perform memory operation
        operation = input_data.get("operation", "search")
        
        if operation == "add":
            content = input_data.get("content", "")
            memory_entry = memory_service.add_memory(content, node.memory_config.type)
            return {"memory_entry": memory_entry.dict(), "node_id": node.id}
        
        elif operation == "search":
            query = input_data.get("query", "")
            results = memory_service.search_memory(query, [node.memory_config.type])
            return {"results": [r.dict() for r in results], "node_id": node.id}
        
        else:
            raise Exception(f"Unknown memory operation: {operation}")
    
    def _get_next_node(
        self, 
        workflow: Workflow, 
        current_node_id: str, 
        node_result: Dict[str, Any]
    ) -> Optional[str]:
        """Determine the next node based on connections."""
        
        if current_node_id not in workflow.connections.root:
            return "END"
        
        connections = workflow.connections.root[current_node_id]
        
        # Handle main connections
        if hasattr(connections, 'main') and connections.main:
            return connections.main[0].node
        
        # Handle conditional connections (implement logic based on node result)
        # This would need more sophisticated logic for conditional routing
        
        return "END"
    
    async def _notify_execution_update(self, execution_id: str, message: Dict[str, Any]):
        """Notify connected WebSocket clients about execution updates."""
        
        if execution_id in self.websocket_connections:
            websocket = self.websocket_connections[execution_id]
            try:
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                # Remove disconnected WebSocket
                del self.websocket_connections[execution_id]


# Global service instance
agent_builder_service = None


def get_agent_builder_service(db: AsyncSession = Depends(get_db)) -> AgentBuilderService:
    """Get agent builder service instance."""
    global agent_builder_service
    if agent_builder_service is None:
        agent_builder_service = AgentBuilderService(db)
    return agent_builder_service


@router.get("/templates", response_model=List[AgentTemplateResponse])
async def get_agent_templates(
    service: AgentBuilderService = Depends(get_agent_builder_service)
):
    """Get available agent templates."""
    return await service.get_agent_templates()


@router.post("/agents", response_model=AgentConfig)
async def create_agent(
    workflow: Workflow,
    service: AgentBuilderService = Depends(get_agent_builder_service)
):
    """Create a new agent configuration."""
    return await service.create_agent_config(workflow)


@router.post("/agents/execute")
async def execute_agent(
    agent_config: AgentConfig,
    input_data: Dict[str, Any],
    service: AgentBuilderService = Depends(get_agent_builder_service)
):
    """Execute an agent workflow."""
    execution_state = await service.execute_agent(agent_config, input_data)
    return {"execution_id": execution_state.execution_id, "status": "started"}


@router.get("/executions/{execution_id}")
async def get_execution_status(
    execution_id: str,
    service: AgentBuilderService = Depends(get_agent_builder_service)
):
    """Get execution status and metrics."""
    if execution_id not in service.active_executions:
        raise HTTPException(status_code=404, detail="Execution not found")
    
    return service.active_executions[execution_id]


@router.get("/dspy/modules")
async def get_dspy_modules():
    """Get available DSPy modules."""
    return get_available_dspy_modules()


@router.post("/optimize-prompt")
async def optimize_prompt(
    request: Dict[str, Any],
    service: AgentBuilderService = Depends(get_agent_builder_service)
):
    """Optimize a prompt using DSPy."""
    try:
        from orchestrator.services.prompt_optimization import get_prompt_optimizer

        optimizer = get_prompt_optimizer()
        original_prompt = request.get("original_prompt", "")
        examples = request.get("examples", [])
        method = request.get("optimization_method", "bootstrap")

        # Convert examples to optimization format
        optimization_examples = []
        for example in examples:
            optimization_examples.append({
                "input_data": example.get("input", {}),
                "expected_output": example.get("output", ""),
                "quality_score": example.get("quality_score", 1.0)
            })

        result = await optimizer.optimize_system_prompt(
            original_prompt, optimization_examples, method
        )

        return {
            "optimized_prompt": result.optimized_prompt,
            "improvement_score": result.improvement_score,
            "method": result.optimization_method,
            "examples_used": result.examples_used
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/test-memory")
async def test_memory(
    request: Dict[str, Any],
    service: AgentBuilderService = Depends(get_agent_builder_service)
):
    """Test memory functionality."""
    try:
        from orchestrator.services.memory.mem0_service import EnhancedMemoryService
        from orchestrator.schemas import MemoryType, MemoryConfig

        memory_type = request.get("memory_type", "short_term")
        operation = request.get("operation", "search")
        data = request.get("data", {})

        # Create memory service
        memory_config = MemoryConfig(type=MemoryType(memory_type))
        memory_service = EnhancedMemoryService(
            agent_id="test",
            user_id="test_user",
            memory_config=memory_config
        )

        if operation == "add":
            content = data.get("content", "Test content")
            memory_entry = memory_service.add_memory(content, MemoryType(memory_type))
            return {"success": True, "memory_entry": memory_entry.dict()}

        elif operation == "search":
            query = data.get("query", "test")
            results = memory_service.search_memory(query, [MemoryType(memory_type)])
            return {"success": True, "results": [r.dict() for r in results]}

        else:
            raise HTTPException(status_code=400, detail=f"Unknown operation: {operation}")

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/export")
async def export_workflow(
    request: Dict[str, Any]
):
    """Export workflow as JSON file."""
    try:
        workflow = request.get("workflow")
        if not workflow:
            raise HTTPException(status_code=400, detail="Workflow is required")

        import json
        from fastapi.responses import Response

        workflow_json = json.dumps(workflow, indent=2)

        return Response(
            content=workflow_json,
            media_type="application/json",
            headers={"Content-Disposition": f"attachment; filename={workflow.get('name', 'workflow')}.json"}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/import")
async def import_workflow(
    file: bytes,
    service: AgentBuilderService = Depends(get_agent_builder_service)
):
    """Import workflow from JSON file."""
    try:
        import json

        workflow_data = json.loads(file.decode('utf-8'))

        # Validate workflow structure
        required_fields = ['name', 'nodes', 'connections']
        for field in required_fields:
            if field not in workflow_data:
                raise HTTPException(status_code=400, detail=f"Missing required field: {field}")

        return workflow_data
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON file")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/agents/{agent_id}/analytics")
async def get_agent_analytics(
    agent_id: str,
    time_range: Optional[str] = None,
    service: AgentBuilderService = Depends(get_agent_builder_service)
):
    """Get agent analytics and performance metrics."""
    try:
        # This would typically fetch from a metrics database
        analytics = {
            "agent_id": agent_id,
            "time_range": time_range or "24h",
            "total_executions": 150,
            "successful_executions": 142,
            "failed_executions": 8,
            "average_execution_time": 2.3,
            "average_tokens_used": 450,
            "node_performance": [
                {"node_id": "node1", "avg_duration": 800, "success_rate": 0.98},
                {"node_id": "node2", "avg_duration": 1200, "success_rate": 0.95}
            ],
            "error_breakdown": [
                {"error_type": "timeout", "count": 5},
                {"error_type": "api_error", "count": 3}
            ]
        }
        return analytics
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/executions/{execution_id}/logs")
async def get_execution_logs(
    execution_id: str,
    service: AgentBuilderService = Depends(get_agent_builder_service)
):
    """Get detailed execution logs."""
    try:
        # This would typically fetch from a logging system
        logs = [
            {
                "timestamp": "2024-01-01T10:00:00Z",
                "level": "INFO",
                "node_id": "node1",
                "message": "Node execution started",
                "metadata": {"input_size": 100}
            },
            {
                "timestamp": "2024-01-01T10:00:01Z",
                "level": "INFO",
                "node_id": "node1",
                "message": "Node execution completed",
                "metadata": {"output_size": 200, "duration_ms": 800}
            }
        ]
        return logs
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.websocket("/ws/execution/{execution_id}")
async def websocket_execution_monitor(
    websocket: WebSocket,
    execution_id: str,
    service: AgentBuilderService = Depends(get_agent_builder_service)
):
    """WebSocket endpoint for real-time execution monitoring."""
    await websocket.accept()
    service.websocket_connections[execution_id] = websocket

    try:
        while True:
            # Keep connection alive
            await websocket.receive_text()
    except WebSocketDisconnect:
        if execution_id in service.websocket_connections:
            del service.websocket_connections[execution_id]
