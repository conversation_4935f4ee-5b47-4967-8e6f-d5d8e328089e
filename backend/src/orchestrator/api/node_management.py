from fastapi import APIRouter, HTTPException
from fastapi.params import Depends as FastAPI_Depends
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Dict, Any, Optional, Union
import importlib
import inspect

from core.db.database import get_db
from orchestrator.schemas import (
    NodeConfig, NodeType, DSPyModuleType, MemoryType, 
    EnhancedToolParameter, ToolParameterType, ToolMetadata,
    PythonToolConfig, WebhookToolConfig, DSPyToolConfig
)
from orchestrator.services.llm_integrations.llm_factory import get_llm_service, AVAILABLE_LLMS
from orchestrator.dspy.modules import get_available_dspy_modules
from orchestrator.tools.registry import AVAILABLE_TOOLS as LANGCHAIN_TOOLS
from core.config import get_settings


router = APIRouter(prefix="/nodes", tags=["node-management"])


class NodeManagementService:
    """Service for managing individual nodes and their configurations."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    def get_available_node_types(self) -> List[Dict[str, Any]]:
        """Get all available node types with their configurations."""
        return [
            {
                "type": NodeType.LLM,
                "name": "LLM Node",
                "description": "Large Language Model processing node",
                "category": "AI",
                "icon": "brain",
                "parameters": [
                    {
                        "name": "system_prompt",
                        "type": ToolParameterType.STRING,
                        "description": "System prompt for the LLM",
                        "required": True,
                        "default": "You are a helpful AI assistant."
                    },
                    {
                        "name": "temperature",
                        "type": ToolParameterType.FLOAT,
                        "description": "Temperature for response generation",
                        "required": False,
                        "default": 0.7,
                        "min_value": 0.0,
                        "max_value": 2.0
                    },
                    {
                        "name": "max_tokens",
                        "type": ToolParameterType.INTEGER,
                        "description": "Maximum tokens in response",
                        "required": False,
                        "default": 1000,
                        "min_value": 1,
                        "max_value": 4000
                    }
                ]
            },
            {
                "type": NodeType.DSPY_MODULE,
                "name": "DSPy Module",
                "description": "DSPy-powered AI module with optimization",
                "category": "AI",
                "icon": "zap",
                "parameters": [
                    {
                        "name": "module_type",
                        "type": ToolParameterType.STRING,
                        "description": "Type of DSPy module",
                        "required": True,
                        "enum_values": [t.value for t in DSPyModuleType]
                    },
                    {
                        "name": "signature",
                        "type": ToolParameterType.STRING,
                        "description": "DSPy signature (input -> output)",
                        "required": True,
                        "examples": ["question -> answer", "text -> summary"]
                    }
                ]
            },
            {
                "type": NodeType.MEMORY,
                "name": "Memory Node",
                "description": "Memory storage and retrieval node",
                "category": "Data",
                "icon": "database",
                "parameters": [
                    {
                        "name": "memory_type",
                        "type": ToolParameterType.STRING,
                        "description": "Type of memory to use",
                        "required": True,
                        "enum_values": [t.value for t in MemoryType]
                    },
                    {
                        "name": "operation",
                        "type": ToolParameterType.STRING,
                        "description": "Memory operation to perform",
                        "required": True,
                        "enum_values": ["add", "search", "update", "delete"]
                    }
                ]
            },
            {
                "type": NodeType.TOOL,
                "name": "Tool Node",
                "description": "External tool or function call node",
                "category": "Integration",
                "icon": "tool",
                "parameters": [
                    {
                        "name": "tool_name",
                        "type": ToolParameterType.STRING,
                        "description": "Name of the tool to execute",
                        "required": True
                    },
                    {
                        "name": "tool_parameters",
                        "type": ToolParameterType.OBJECT,
                        "description": "Parameters to pass to the tool",
                        "required": False,
                        "default": {}
                    }
                ]
            },
            {
                "type": NodeType.CONDITIONAL,
                "name": "Conditional Node",
                "description": "Conditional logic and routing node",
                "category": "Logic",
                "icon": "git-branch",
                "parameters": [
                    {
                        "name": "condition_type",
                        "type": ToolParameterType.STRING,
                        "description": "Type of condition to evaluate",
                        "required": True,
                        "enum_values": ["simple", "ai_based", "script"]
                    },
                    {
                        "name": "condition_expression",
                        "type": ToolParameterType.STRING,
                        "description": "Condition expression or prompt",
                        "required": True
                    }
                ]
            },
            {
                "type": NodeType.RAG,
                "name": "RAG Node",
                "description": "Retrieval-Augmented Generation node",
                "category": "AI",
                "icon": "search",
                "parameters": [
                    {
                        "name": "knowledge_base",
                        "type": ToolParameterType.STRING,
                        "description": "Knowledge base to search",
                        "required": True
                    },
                    {
                        "name": "top_k",
                        "type": ToolParameterType.INTEGER,
                        "description": "Number of top results to retrieve",
                        "required": False,
                        "default": 5,
                        "min_value": 1,
                        "max_value": 20
                    }
                ]
            },
            {
                "type": NodeType.INPUT,
                "name": "Input Node",
                "description": "User input collection node",
                "category": "IO",
                "icon": "arrow-down",
                "parameters": [
                    {
                        "name": "input_type",
                        "type": ToolParameterType.STRING,
                        "description": "Type of input to collect",
                        "required": True,
                        "enum_values": ["text", "voice", "file", "structured"]
                    }
                ]
            },
            {
                "type": NodeType.OUTPUT,
                "name": "Output Node",
                "description": "Response output node",
                "category": "IO",
                "icon": "arrow-up",
                "parameters": [
                    {
                        "name": "output_format",
                        "type": ToolParameterType.STRING,
                        "description": "Format of the output",
                        "required": True,
                        "enum_values": ["text", "json", "html", "markdown"]
                    }
                ]
            }
        ]
    
    def get_available_functions(self) -> List[Dict[str, Any]]:
        """Get available Python functions that can be used in nodes."""
        functions = []
        
        # Scan orchestrator.nodes module for available functions
        try:
            import orchestrator.nodes as nodes_module
            for module_name in ['llm', 'rag', 'memory', 'tools']:
                try:
                    module = importlib.import_module(f'orchestrator.nodes.{module_name}')
                    for name, obj in inspect.getmembers(module, inspect.isfunction):
                        if not name.startswith('_'):
                            sig = inspect.signature(obj)
                            functions.append({
                                "name": name,
                                "module": f"orchestrator.nodes.{module_name}",
                                "function_path": f"orchestrator.nodes.{module_name}.{name}",
                                "description": obj.__doc__ or f"Function {name} from {module_name}",
                                "parameters": [
                                    {
                                        "name": param_name,
                                        "type": str(param.annotation) if param.annotation != inspect.Parameter.empty else "Any",
                                        "required": param.default == inspect.Parameter.empty,
                                        "default": param.default if param.default != inspect.Parameter.empty else None
                                    }
                                    for param_name, param in sig.parameters.items()
                                    if param_name not in ['state', 'db', 'llm_service', 'settings']
                                ]
                            })
                except ImportError:
                    continue
        except ImportError:
            pass
        
        return functions
    
    async def test_node(
        self, 
        node_config: NodeConfig, 
        test_input: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Test a single node configuration."""
        
        try:
            if node_config.type == NodeType.LLM:
                return await self._test_llm_node(node_config, test_input)
            elif node_config.type == NodeType.DSPY_MODULE:
                return await self._test_dspy_node(node_config, test_input)
            elif node_config.type == NodeType.MEMORY:
                return await self._test_memory_node(node_config, test_input)
            elif node_config.type == NodeType.PYTHON_FUNCTION:
                return await self._test_python_function_node(node_config, test_input)
            else:
                return {"error": f"Node type {node_config.type} not supported for testing"}
        
        except Exception as e:
            return {"error": str(e), "node_id": node_config.id}
    
    async def _test_llm_node(self, node_config: NodeConfig, test_input: Dict[str, Any]) -> Dict[str, Any]:
        """Test an LLM node."""
        settings = get_settings()
        llm_service = get_llm_service(settings)
        
        system_prompt = node_config.system_prompt or "You are a helpful AI assistant."
        user_message = test_input.get("user_message", "Hello")
        
        response = await llm_service.get_chat_completion([
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_message}
        ])
        
        return {
            "success": True,
            "response": response,
            "node_id": node_config.id,
            "execution_time_ms": 1000  # Placeholder
        }
    
    async def _test_dspy_node(self, node_config: NodeConfig, test_input: Dict[str, Any]) -> Dict[str, Any]:
        """Test a DSPy node."""
        if not node_config.dspy_config:
            return {"error": "DSPy configuration missing"}
        
        from orchestrator.dspy.modules import DSPyModuleFactory
        
        module = DSPyModuleFactory.create_module(
            node_config.dspy_config.module_type,
            {"signature": node_config.dspy_config.signature}
        )
        
        result = module(**test_input)
        
        return {
            "success": True,
            "result": result,
            "node_id": node_config.id,
            "execution_time_ms": 800  # Placeholder
        }
    
    async def _test_memory_node(self, node_config: NodeConfig, test_input: Dict[str, Any]) -> Dict[str, Any]:
        """Test a memory node."""
        if not node_config.memory_config:
            return {"error": "Memory configuration missing"}
        
        from orchestrator.services.memory.mem0_service import EnhancedMemoryService
        
        memory_service = EnhancedMemoryService(
            agent_id="test",
            user_id="test_user",
            memory_config=node_config.memory_config
        )
        
        operation = test_input.get("operation", "search")
        
        if operation == "add":
            content = test_input.get("content", "Test memory content")
            memory_entry = memory_service.add_memory(content, node_config.memory_config.type)
            return {
                "success": True,
                "memory_entry": memory_entry.dict(),
                "node_id": node_config.id
            }
        
        elif operation == "search":
            query = test_input.get("query", "test")
            results = memory_service.search_memory(query, [node_config.memory_config.type])
            return {
                "success": True,
                "results": [r.dict() for r in results],
                "node_id": node_config.id
            }
        
        return {"error": f"Unknown memory operation: {operation}"}
    
    async def _test_python_function_node(self, node_config: NodeConfig, test_input: Dict[str, Any]) -> Dict[str, Any]:
        """Test a Python function node."""
        if not node_config.functionPath:
            return {"error": "Function path missing"}
        
        try:
            # Import and execute the function
            module_path, function_name = node_config.functionPath.rsplit('.', 1)
            module = importlib.import_module(module_path)
            function = getattr(module, function_name)
            
            # Execute function with test input
            result = await function(test_input, **node_config.parameters)
            
            return {
                "success": True,
                "result": result,
                "node_id": node_config.id,
                "execution_time_ms": 500  # Placeholder
            }
        
        except Exception as e:
            return {"error": str(e), "node_id": node_config.id}


def get_node_management_service(db: AsyncSession = FastAPI_Depends(get_db)) -> NodeManagementService:
    """Get node management service instance."""
    return NodeManagementService(db)


@router.get("/types")
async def get_node_types(
    service: NodeManagementService = FastAPI_Depends(get_node_management_service)
):
    """Get available node types and their configurations."""
    return service.get_available_node_types()


@router.get("/functions")
async def get_available_functions(
    service: NodeManagementService = FastAPI_Depends(get_node_management_service)
):
    """Get available Python functions for nodes."""
    return service.get_available_functions()


@router.get("/llm-providers")
async def get_llm_providers():
    """Get available LLM providers."""
    return [
        {
            "name": provider_name,
            "description": f"{provider_name.title()} LLM provider",
            "models": ["default"]  # This could be expanded with actual model lists
        }
        for provider_name in AVAILABLE_LLMS.keys()
    ]


@router.post("/test")
async def test_node(
    node_config: NodeConfig,
    test_input: Dict[str, Any],
    service: NodeManagementService = FastAPI_Depends(get_node_management_service)
):
    """Test a single node configuration."""
    return await service.test_node(node_config, test_input)


@router.get("/dspy/modules")
async def get_dspy_modules_endpoint():
    """Get available DSPy modules."""
    return get_available_dspy_modules()

@router.get("/tools/metadata", response_model=List[ToolMetadata])
async def get_all_tools_metadata(
    service: NodeManagementService = FastAPI_Depends(get_node_management_service)
) -> List[ToolMetadata]:
    """
    Get metadata for all available tools (Python functions, Langchain tools, DSPy modules).
    """
    all_tools_metadata: List[ToolMetadata] = []

    # 1. Add Python functions
    python_functions = service.get_available_functions()
    for func in python_functions:
        params = [
            EnhancedToolParameter(
                name=p["name"],
                type=ToolParameterType.STRING, # Default to string, refine later if needed
                description=f"Parameter {p['name']}",
                required=p["required"],
                default=p["default"]
            ) for p in func["parameters"]
        ]
        all_tools_metadata.append(
            ToolMetadata(
                name=func["name"],
                description=func["description"],
                parameters=params
            )
        )

    # 2. Add Langchain tools
    for tool_name, tool_obj in LANGCHAIN_TOOLS.items():
        # Langchain tools have a description and args_schema
        params = []
        if tool_obj.args_schema:
            # This part might need more sophisticated parsing of Pydantic models
            # For now, a simplified approach
            for field_name, field in tool_obj.args_schema.model_fields.items():
                params.append(
                    EnhancedToolParameter(
                        name=field_name,
                        type=ToolParameterType.STRING, # Default
                        description=field.description or f"Parameter {field_name}",
                        required=field.is_required(),
                        default=field.default
                    )
                )
        all_tools_metadata.append(
            ToolMetadata(
                name=tool_obj.name,
                description=tool_obj.description,
                parameters=params
            )
        )

    # 3. Add DSPy modules
    dspy_modules = get_available_dspy_modules()
    for dspy_module in dspy_modules:
        # DSPy modules have a signature, which can be parsed for parameters
        signature_str = dspy_module.get("signature", "")
        # Simple parsing: "input1, input2 -> output"
        input_part = signature_str.split("->")[0].strip()
        dspy_params = []
        if input_part:
            for param_name in input_part.split(","):
                dspy_params.append(
                    EnhancedToolParameter(
                        name=param_name.strip(),
                        type=ToolParameterType.STRING, # Default
                        description=f"DSPy input: {param_name.strip()}",
                        required=True # Assuming all signature inputs are required
                    )
                )
        all_tools_metadata.append(
            ToolMetadata(
                name=dspy_module["name"],
                description=dspy_module["description"],
                parameters=dspy_params
            )
        )

    return all_tools_metadata


@router.post("/validate")
async def validate_node_config(node_config: NodeConfig):
    """Validate a node configuration."""
    errors = []
    
    # Basic validation
    if not node_config.id:
        errors.append("Node ID is required")
    
    if not node_config.name:
        errors.append("Node name is required")
    
    # Type-specific validation
    if node_config.type == NodeType.LLM and not node_config.system_prompt:
        errors.append("System prompt is required for LLM nodes")
    
    if node_config.type == NodeType.DSPY_MODULE and not node_config.dspy_config:
        errors.append("DSPy configuration is required for DSPy nodes")
    
    if node_config.type == NodeType.MEMORY and not node_config.memory_config:
        errors.append("Memory configuration is required for memory nodes")
    
    if node_config.type == NodeType.PYTHON_FUNCTION and not node_config.functionPath:
        errors.append("Function path is required for Python function nodes")
    
    return {
        "valid": len(errors) == 0,
        "errors": errors,
        "node_id": node_config.id
    }