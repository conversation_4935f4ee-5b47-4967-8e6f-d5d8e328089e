import importlib
import pkgutil
import inspect
from langchain.tools import BaseTool

# Add modules here to be scanned for Langchain tools
TOOL_MODULES = [
    "modules.customers.tools",
    "orchestrator.tools.langchain_tools",
]

def discover_tools() -> dict[str, BaseTool]:
    """
    Discovers all Langchain tools (functions decorated with @tool)
    in the modules specified in TOOL_MODULES.
    """
    tools = {}
    for module_name in TOOL_MODULES:
        module = importlib.import_module(module_name)
        for name, obj in inspect.getmembers(module):
            if isinstance(obj, BaseTool):
                tools[obj.name] = obj
    return tools

# Discover tools on startup
AVAILABLE_TOOLS = discover_tools()
