import logging

from langchain.tools import tool

from modules.jobs import crud as job_crud
from modules.jobs import schemas as job_schemas
from core.db.database import get_db
from datetime import datetime
from core.utils.helpers import model_to_dict


logger = logging.getLogger(__name__)


from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

# ... (other imports)

logger = logging.getLogger(__name__)


@tool
async def schedule_job(
    customer_id: int,
    service_type: str,
    scheduled_time: str,
    notes: str | None = None,
    db: AsyncSession = Depends(get_db),  # <-- Modernized Dependency Injection
):
    """Schedule a new job for a customer."""
    logger.info(
        f"Langchain Tool: schedule_job for customer_id: {customer_id}, service_type: {service_type}"
    )
    # Convert scheduled_time string to datetime object
    scheduled_datetime = datetime.fromisoformat(scheduled_time)
    job_schema = job_schemas.JobCreate(
        customer_id=customer_id,
        service_type=service_type,
        scheduled_time=scheduled_datetime,
        notes=notes,
    )
    job = await job_crud.create_job(db, job_schema)
    return model_to_dict(job)


@tool
async def get_customer_jobs(customer_id: int):
    """Get a list of all jobs for a customer."""
    async with get_db() as db:
        logger.info(f"Langchain Tool: get_customer_jobs for customer_id: {customer_id}")
        jobs = await job_crud.get_jobs_by_customer(db, customer_id)
        return [model_to_dict(job) for job in jobs]
