from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
import importlib
from pydantic import BaseModel
from typing import Any, Dict

from orchestrator.schemas import NodeConfig, Workflow
from core.db.database import get_db
from orchestrator.services.llm_integrations.llm_factory import (
    get_llm_service,
    AVAILABLE_LLMS,
)
from orchestrator.services.llm_integrations.embedding_factory import (
    get_embedding_service,
)
from core.config import get_settings
from orchestrator import nodes as node_functions
from orchestrator import tools as langchain_tools
import inspect

# Import new API routers
from orchestrator.api.agent_builder import router as agent_builder_router
from orchestrator.api.node_management import router as node_management_router


router = APIRouter()

# Include sub-routers
router.include_router(agent_builder_router)
router.include_router(node_management_router)


class NodeTestRequest(BaseModel):
    node_config: NodeConfig
    input_state: Dict[str, Any]
    workflow: Workflow


@router.get("/ui-config")
async def get_ui_config():
    """
    Provides the UI configuration for the agent editor.
    """
    # Get all functions from the nodes module
    node_types = [
        {"type": name, "label": name.replace("_", " ").title()}
        for name, func in inspect.getmembers(node_functions, inspect.isfunction)
    ]

    # Get all functions from the tools module
    tool_types = [
        {"type": name, "label": name.replace("_", " ").title()}
        for name, func in inspect.getmembers(langchain_tools, inspect.isfunction)
    ]

    return {
        "node_types": node_types,
        "tool_types": tool_types,
        "llm_types": list(AVAILABLE_LLMS.keys()),
    }


@router.post("/test-node", response_model=Dict[str, Any])
async def test_agent_node(
    request: NodeTestRequest,
    db: AsyncSession = Depends(get_db),
    llm_service=Depends(get_llm_service),
    embedding_service=Depends(get_embedding_service),
    settings=Depends(get_settings),
):
    """
    Dynamically executes a single agent node and returns the resulting state.
    """
    node_config = request.node_config
    input_state = request.input_state

    try:
        # Dynamically import the function
        module_path, function_name = node_config.functionPath.rsplit(".", 1)
        module = importlib.import_module(module_path)
        node_function = getattr(module, function_name)

        # Prepare dependencies
        dependencies = {
            "llm_service": llm_service,
            "db": db,
            "settings": settings,
            "embedding_service": embedding_service,
        }

        required_deps = {
            dep: dependencies[dep]
            for dep in node_config.dependencies
            if dep in dependencies
        }

        # Execute the function with the provided state and dependencies
        output_state = await node_function(input_state, **required_deps)

        return output_state

    except ImportError:
        raise HTTPException(status_code=404, detail=f"Module not found: {module_path}")
    except AttributeError:
        raise HTTPException(
            status_code=404, detail=f"Function not found: {function_name}"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error executing node: {str(e)}")


def register_agent_routes(app):
    app.include_router(router, prefix="/api/agent", tags=["agent"])
