from abc import ABC, abstractmethod
from typing import Async<PERSON>enerator
from orchestrator.schemas import SpeechConfig
import asyncio


class TTSBase(ABC):
    """Abstract base class for text-to-speech services."""

    @abstractmethod
    async def stream(
        self,
        text_generator: AsyncGenerator[str, None],
        interruption_event: asyncio.Event,
        speech_config: SpeechConfig = SpeechConfig(),
    ) -> AsyncGenerator[str, None]:
        """
        Streams audio froma text generator.

        Args:
            text_generator: An async generator that yields text chunks.
            interruption_event: An event to signal interruption.
        """
        pass

    async def stream_realtime(
        self,
        text_generator: AsyncGenerator[str, None],
        interruption_event: asyncio.Event,
        speech_config: SpeechConfig = SpeechConfig(),
    ) -> AsyncGenerator[str, None]:
        """
        Realtime streaming audio froma text generator using WebSocket connections.
        Default implementation falls back to regular streaming.

        Args:
            text_generator: An async generator that yields text chunks.
            interruption_event: An event to signal interruption.
        """
        # Default implementation falls back to regular streaming
        async for chunk in await self.stream(
            text_generator, interruption_event, speech_config
        ):
            yield chunk
