import asyncio
import base64
import json
import logging
import websockets
from typing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from orchestrator.schemas import SpeechConfig

from elevenlabs.client import ElevenLabs as ElevenLabsClient

from core.config import get_settings
from orchestrator.services.tts.base import TTSBase

logger = logging.getLogger(__name__)
settings = get_settings()


class ElevenLabsTTS(TTSBase):
    def __init__(self):
        self.client = ElevenLabsClient(api_key=settings.ELEVENLABS_API_KEY)
        if settings.ELEVENLABS_API_KEY:
            logger.info("ElevenLabsService initialized with API key.")
        else:
            logger.warning("ElevenLabsService is missing API key in settings.")

    async def stream(
        self,
        text_generator: AsyncGenerator[str, None],
        interruption_event: asyncio.Event,
        speech_config: SpeechConfig = SpeechConfig(),
    ) -> AsyncGenerator[str, None]:  # type: ignore[override]
        """
        Generates audio froma text generator and yields base64 encoded audio chunks.
        """
        text = "".join([chunk async for chunk in text_generator])
        if not text:
            return

        try:
            audio_stream = self.client.text_to_speech.stream(
                text=text,
                voice_id=speech_config.tts_voice_id or settings.ELEVENLABS_VOICE_ID,
                model_id="eleven_turbo_v2_5",
                output_format="pcm_16000",
                # ElevenLabs API does not have a direct 'speaking_rate' parameter for stream
                # It's usually controlled via voice_settings or model choice.
                # For now, we'll rely on the model's default or voice settings.
            )

            for audio_chunk in audio_stream:
                if interruption_event.is_set():
                    break
                yield base64.b64encode(audio_chunk).decode("utf-8")

        except Exception as e:
            logger.error(f"Error in ElevenLabs stream: {e}")

    async def stream_realtime(
        self,
        text_generator: AsyncGenerator[str, None],
        interruption_event: asyncio.Event,
        speech_config: SpeechConfig = SpeechConfig(),
    ) -> AsyncGenerator[str, None]:
        """
        Realtime TTS streaming using ElevenLabs WebSocket API.
        """
        uri = f"wss://api.elevenlabs.io/v1/text-to-speech/{speech_config.tts_voice_id or settings.ELEVENLABS_VOICE_ID}/stream-input?model_id=eleven_turbo_v2_5"
        headers = {"xi-api-key": settings.ELEVENLABS_API_KEY}

        async with websockets.connect(uri, extra_headers=headers) as websocket:
            await websocket.send(
                json.dumps(
                    {
                        "text": " ",
                        "voice_settings": {
                            "stability": 0.5,
                            "similarity_boost": 0.8,
                            "speaking_rate": speech_config.tts_speaking_rate or 1.0,
                        },
                        "generation_config": {"chunk_length_schedule": [50]},
                    }
                )
            )

            async def send_text():
                async for text_chunk in text_generator:
                    if interruption_event.is_set():
                        break
                    await websocket.send(
                        json.dumps({"text": text_chunk, "try_trigger_generation": True})
                    )
                await websocket.send(json.dumps({"text": ""}))

            async def receive_audio():
                while not interruption_event.is_set():
                    try:
                        message = await websocket.recv()
                        data = json.loads(message)
                        if data.get("audio"):
                            yield data["audio"]
                        elif data.get("isFinal"):
                            break
                    except websockets.ConnectionClosed:
                        break

            send_task = asyncio.create_task(send_text())
            async for audio_chunk in receive_audio():
                yield audio_chunk

            await send_task
