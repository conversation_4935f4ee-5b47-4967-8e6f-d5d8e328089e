from mem0 import Memory
from core.config import get_settings
from typing import List, Dict, Any, Optional
from orchestrator.schemas import MemoryType, MemoryConfig
from orchestrator.models import AgentMemoryEntry
import json
import uuid
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)


class EnhancedMemoryService:
    """Enhanced memory service with support for multiple memory types and advanced features."""

    def __init__(self, agent_id: str, user_id: str, memory_config: Optional[MemoryConfig] = None):
        self.agent_id = agent_id
        self.user_id = user_id
        self.memory_config = memory_config or MemoryConfig(type=MemoryType.SHORT_TERM)
        settings = get_settings()

        # Configure Mem0 to use the pgvector backend for persistence
        self.mem0 = Memory(
            config={
                "vector_store": {
                    "provider": "pgvector",
                    "config": {
                        "database_uri": settings.DATABASE_URL,
                    },
                }
            }
        )

        # In-memory storage for different memory types
        self.short_term_memory: List[AgentMemoryEntry] = []
        self.working_memory: Dict[str, Any] = {}

    def add_memory(
        self,
        content: str,
        memory_type: MemoryType = None,
        metadata: Dict[str, Any] = None
    ) -> AgentMemoryEntry:
        """Add a memory entry with specified type and metadata."""

        memory_type = memory_type or self.memory_config.type
        metadata = metadata or {}

        # Create memory entry
        memory_entry = AgentMemoryEntry(
            agent_id=self.agent_id,
            memory_type=memory_type.value,
            content=content,
            metadata={
                **metadata,
                "user_id": self.user_id,
                "source": "agent_interaction"
            }
        )

        # Handle different memory types
        if memory_type == MemoryType.SHORT_TERM:
            self._add_short_term_memory(memory_entry)
        elif memory_type == MemoryType.LONG_TERM:
            self._add_long_term_memory(memory_entry)
        elif memory_type == MemoryType.WORKING:
            self._add_working_memory(memory_entry)
        elif memory_type == MemoryType.EPISODIC:
            self._add_episodic_memory(memory_entry)
        elif memory_type == MemoryType.SEMANTIC:
            self._add_semantic_memory(memory_entry)

        return memory_entry

    def _add_short_term_memory(self, memory_entry: AgentMemoryEntry):
        """Add to short-term memory with size limits."""
        self.short_term_memory.append(memory_entry)

        # Enforce size limits
        max_entries = self.memory_config.max_entries or 20
        if len(self.short_term_memory) > max_entries:
            # Remove oldest entries
            self.short_term_memory = self.short_term_memory[-max_entries:]

    def _add_long_term_memory(self, memory_entry: AgentMemoryEntry):
        """Add to long-term memory using Mem0."""
        try:
            self.mem0.add(
                memory_entry.content,
                user_id=f"{self.agent_id}_{self.user_id}",
                metadata=memory_entry.metadata
            )
        except Exception as e:
            logger.error(f"Failed to add long-term memory: {e}")

    def _add_working_memory(self, memory_entry: AgentMemoryEntry):
        """Add to working memory (temporary storage)."""
        key = memory_entry.metadata.get("key", memory_entry.id)
        self.working_memory[key] = memory_entry

    def _add_episodic_memory(self, memory_entry: AgentMemoryEntry):
        """Add episodic memory (conversation episodes)."""
        memory_entry.metadata["episode_id"] = memory_entry.metadata.get("episode_id", str(uuid.uuid4()))
        self._add_long_term_memory(memory_entry)

    def _add_semantic_memory(self, memory_entry: AgentMemoryEntry):
        """Add semantic memory (facts and knowledge)."""
        memory_entry.metadata["memory_category"] = "semantic"
        self._add_long_term_memory(memory_entry)

    def search_memory(
        self,
        query: str,
        memory_types: List[MemoryType] = None,
        limit: int = 5
    ) -> List[AgentMemoryEntry]:
        """Search across different memory types."""

        memory_types = memory_types or [self.memory_config.type]
        results = []

        for memory_type in memory_types:
            if memory_type == MemoryType.SHORT_TERM:
                results.extend(self._search_short_term_memory(query, limit))
            elif memory_type in [MemoryType.LONG_TERM, MemoryType.EPISODIC, MemoryType.SEMANTIC]:
                results.extend(self._search_long_term_memory(query, limit))
            elif memory_type == MemoryType.WORKING:
                results.extend(self._search_working_memory(query, limit))

        # Sort by relevance and return top results
        return sorted(results, key=lambda x: x.relevance_score or 0, reverse=True)[:limit]

    def _search_short_term_memory(self, query: str, limit: int) -> List[AgentMemoryEntry]:
        """Search short-term memory using simple text matching."""
        results = []
        query_lower = query.lower()

        for memory in self.short_term_memory:
            if query_lower in memory.content.lower():
                memory.relevance_score = 0.8  # Simple scoring
                results.append(memory)

        return results[:limit]

    def _search_long_term_memory(self, query: str, limit: int) -> List[AgentMemoryEntry]:
        """Search long-term memory using Mem0."""
        try:
            mem0_results = self.mem0.search(
                query,
                user_id=f"{self.agent_id}_{self.user_id}",
                limit=limit
            )

            results = []
            for result in mem0_results:
                memory_entry = AgentMemoryEntry(
                    agent_id=self.agent_id,
                    memory_type=MemoryType.LONG_TERM.value,
                    content=result.get("memory", ""),
                    metadata=result.get("metadata", {}),
                    relevance_score=result.get("score", 0.0)
                )
                results.append(memory_entry)

            return results
        except Exception as e:
            logger.error(f"Failed to search long-term memory: {e}")
            return []

    def _search_working_memory(self, query: str, limit: int) -> List[AgentMemoryEntry]:
        """Search working memory."""
        results = []
        query_lower = query.lower()

        for memory in self.working_memory.values():
            if query_lower in memory.content.lower():
                memory.relevance_score = 0.9  # High relevance for working memory
                results.append(memory)

        return results[:limit]

    def get_conversation_context(self, limit: int = 10) -> List[AgentMemoryEntry]:
        """Get recent conversation context."""
        return self.short_term_memory[-limit:] if self.short_term_memory else []

    def clear_working_memory(self):
        """Clear working memory."""
        self.working_memory.clear()

    def cleanup_expired_memories(self):
        """Remove expired memories based on TTL."""
        if not self.memory_config.ttl_seconds:
            return

        current_time = datetime.utcnow()
        ttl_delta = timedelta(seconds=self.memory_config.ttl_seconds)

        # Clean up short-term memory
        self.short_term_memory = [
            memory for memory in self.short_term_memory
            if datetime.fromisoformat(memory.created_at) + ttl_delta > current_time
        ]


class Mem0Service(EnhancedMemoryService):
    """Backward compatibility wrapper for the original Mem0Service."""

    def __init__(self, user_id: str):
        super().__init__(agent_id="default", user_id=user_id)

    def add_memory(self, data: str):
        """Backward compatible add_memory method."""
        return super().add_memory(data, MemoryType.LONG_TERM)

    def search_memory(self, query: str):
        """Backward compatible search_memory method."""
        results = super().search_memory(query, [MemoryType.LONG_TERM])
        return [{"memory": r.content, "score": r.relevance_score} for r in results]
