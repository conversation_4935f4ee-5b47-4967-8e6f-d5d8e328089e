from datetime import datetime
import json
import logging
from typing import Any, AsyncGenerator, Dict, List, Optional, Callable

from langchain_core.messages import AIMessage, HumanMessage, SystemMessage
from sqlalchemy.ext.asyncio import AsyncSession

from core.config import get_settings
from orchestrator.template_loader import load_agent_template
from orchestrator.graph import get_agent_graph
from orchestrator.models import AgentState
from orchestrator.schemas import AgentConfig
from orchestrator.services.llm_integrations.llm_factory import get_llm_service
from modules.call.schemas import ConversationLogEvent
from core.utils.events import _log_and_send_event
from orchestrator.services.callbacks import OrchestrationCallbackHandler

logger = logging.getLogger(__name__)


class OrchestrationService:
    def __init__(self, db_session_factory: Callable[[], AsyncSession]):
        self.db_session_factory = db_session_factory
        # Load the default agent config as a fallback
        default_agent_config = load_agent_config(agent_id="default_agent")
        self.default_agent_graph = get_agent_graph(db_session_factory, default_agent_config)

    def _prepare_run(
        self,
        messages: List[Dict[str, str]],
        context: Optional[Dict] = None,
    ):
        context = context or {}
        formatted_context = {
            "customer_info": json.dumps(context.get("customer_info"), indent=2),
            "call_context": json.dumps(context.get("call_context"), indent=2),
        }
        
        system_prompt = ""
        if messages and messages[0]["role"] == "system":
            system_prompt = messages[0]["content"]
        
        if formatted_context:
            system_prompt += (
                "\n\nHere is some context about the user and the call.\n"
                "Use this context to inform your responses, but do not mention it "
                "unless directly asked.\n\n"
                f"Customer Information:\n{formatted_context['customer_info']}\n\n"
                f"Call Context:\n{formatted_context['call_context']}\n"
            )

        return system_prompt

    async def run_agent_async(
        self,
        messages: List[Dict[str, str]],
        db: AsyncSession,
        context: Optional[Dict] = None,
        call_history_id: Optional[int] = None,
        websocket: Optional[Any] = None,
        input_handler: Optional[Callable] = None,
        output_handler: Optional[Callable] = None,
        agent_config: Optional[AgentConfig] = None,
    ) -> AsyncGenerator[Dict, None]:
        
        system_prompt = self._prepare_run(messages, context)
        
        initial_state = AgentState(
            messages=messages,
            call_history_id=call_history_id,
            system_prompt=system_prompt,
            customer_id=context.get("customer_info", {}).get("id") if context else None,
            websocket=websocket,
            input_handler=input_handler,
            output_handler=output_handler,
        )

        logger.info(f"Invoking AgentGraph with initial state for call_history_id: {call_history_id}")
        
        if agent_config:
            agent_graph_instance = get_agent_graph(self.db_session_factory, agent_config)
            graph = agent_graph_instance.app
        else:
            graph = self.default_agent_graph.app

        config = {}
        if call_history_id:
            callback_handler = OrchestrationCallbackHandler(db=db, call_history_id=call_history_id)
            config["callbacks"] = [callback_handler]

        try:
            async for event in graph.astream(initial_state, config=config):
                if "__end__" in event:
                    final_state = event["__end__"]
                    final_response = final_state.get("response")
                    if final_response:
                        logger.info(f"AgentGraph final response: {final_response}")
                        yield {"output": final_response}
                        
        except Exception as e:
            logger.error(f"Error during agent execution: {e}", exc_info=True)
            yield {"output": "I'm sorry, I encountered an error. Please try again."}
