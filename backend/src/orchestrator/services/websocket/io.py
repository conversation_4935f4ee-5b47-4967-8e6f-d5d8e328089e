import logging
from datetime import datetime
from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession

from orchestrator.models import Agent<PERSON>tate
from modules.call.schemas import Conversation<PERSON>og<PERSON><PERSON>
from core.utils.events import _log_and_send_event


logger = logging.getLogger(__name__)


async def get_user_input(state: AgentState, db: AsyncSession) -> AgentState:
    """A node that gets user input from the websocket."""
    logger.info("---Node: get_user_input---")
    try:
        input_handler = state.input_handler
        websocket = state.websocket
        speech_config = state.speech_config
        call_history_id = state.call_history_id

        logger.info("Waiting for user input...")
        user_message = await input_handler(websocket, speech_config)

        if user_message is None or not user_message.strip():
            state.user_message = None
            logger.info("No user input received. Ending conversation.")
        else:
            state.user_message = user_message
            state.messages.append({"role": "user", "content": user_message})
            logger.info(f"User input received: '{user_message}'")

            # Log the user message to the conversation log
            await _log_and_send_event(
                db,
                call_history_id,
                ConversationLogEvent(
                    call_history_id=call_history_id,
                    event_type="user_message",
                    timestamp=datetime.now(),
                    data={"content": user_message},
                    source="user_websocket",
                ),
            )

    except Exception as e:
        logger.error(f"Error in get_user_input: {e}", exc_info=True)
        state.user_message = None
        state.response = (
            "I'm sorry, I encountered a technical issue. Please try again later."
        )

    logger.info("---End Node: get_user_input---")
    return state
