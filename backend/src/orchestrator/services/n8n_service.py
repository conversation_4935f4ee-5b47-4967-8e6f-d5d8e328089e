"""
N8N Integration Service

This service provides integration with N8N workflows for the orchestrator.
It allows nodes to trigger N8N workflows and receive responses.
"""

import asyncio
import json
import logging
from typing import Any, Dict, Optional, Union
import aiohttp
from urllib.parse import urljoin

logger = logging.getLogger(__name__)


class N8NService:
    """Service for integrating with N8N workflows."""
    
    def __init__(
        self,
        base_url: str = "http://localhost:5678",
        username: str = "admin",
        password: str = "password",
        timeout: int = 30
    ):
        self.base_url = base_url.rstrip('/')
        self.username = username
        self.password = password
        self.timeout = timeout
        self._session: Optional[aiohttp.ClientSession] = None
        
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create HTTP session with authentication."""
        if self._session is None or self._session.closed:
            auth = aiohttp.BasicAuth(self.username, self.password)
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            self._session = aiohttp.ClientSession(
                auth=auth,
                timeout=timeout,
                headers={'Content-Type': 'application/json'}
            )
        return self._session
    
    async def close(self):
        """Close the HTTP session."""
        if self._session and not self._session.closed:
            await self._session.close()
    
    async def execute_workflow(
        self,
        workflow_id: str,
        input_data: Dict[str, Any],
        wait_for_completion: bool = True
    ) -> Dict[str, Any]:
        """
        Execute an N8N workflow.
        
        Args:
            workflow_id: The ID of the workflow to execute
            input_data: Input data to pass to the workflow
            wait_for_completion: Whether to wait for workflow completion
            
        Returns:
            Dict containing the workflow execution result
        """
        try:
            session = await self._get_session()
            
            # Execute workflow via API
            url = f"{self.base_url}/api/v1/workflows/{workflow_id}/execute"
            
            payload = {
                "data": input_data,
                "waitTill": "completed" if wait_for_completion else "started"
            }
            
            logger.info(f"Executing N8N workflow {workflow_id} with data: {input_data}")
            
            async with session.post(url, json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    logger.info(f"N8N workflow {workflow_id} executed successfully")
                    return {
                        "success": True,
                        "data": result,
                        "workflow_id": workflow_id,
                        "execution_id": result.get("id")
                    }
                else:
                    error_text = await response.text()
                    logger.error(f"N8N workflow execution failed: {response.status} - {error_text}")
                    return {
                        "success": False,
                        "error": f"HTTP {response.status}: {error_text}",
                        "workflow_id": workflow_id
                    }
                    
        except asyncio.TimeoutError:
            logger.error(f"N8N workflow {workflow_id} execution timed out")
            return {
                "success": False,
                "error": "Workflow execution timed out",
                "workflow_id": workflow_id
            }
        except Exception as e:
            logger.error(f"N8N workflow {workflow_id} execution error: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "workflow_id": workflow_id
            }
    
    async def trigger_webhook(
        self,
        webhook_url: str,
        data: Dict[str, Any],
        method: str = "POST"
    ) -> Dict[str, Any]:
        """
        Trigger an N8N webhook.
        
        Args:
            webhook_url: The webhook URL to trigger
            data: Data to send to the webhook
            method: HTTP method to use (GET, POST, etc.)
            
        Returns:
            Dict containing the webhook response
        """
        try:
            session = await self._get_session()
            
            logger.info(f"Triggering N8N webhook: {webhook_url}")
            
            if method.upper() == "GET":
                async with session.get(webhook_url, params=data) as response:
                    result = await self._process_webhook_response(response, webhook_url)
            else:
                async with session.request(method.upper(), webhook_url, json=data) as response:
                    result = await self._process_webhook_response(response, webhook_url)
            
            return result
            
        except asyncio.TimeoutError:
            logger.error(f"N8N webhook {webhook_url} timed out")
            return {
                "success": False,
                "error": "Webhook request timed out",
                "webhook_url": webhook_url
            }
        except Exception as e:
            logger.error(f"N8N webhook {webhook_url} error: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "webhook_url": webhook_url
            }
    
    async def _process_webhook_response(
        self,
        response: aiohttp.ClientResponse,
        webhook_url: str
    ) -> Dict[str, Any]:
        """Process webhook response."""
        if response.status in [200, 201]:
            try:
                result = await response.json()
            except:
                result = await response.text()
            
            logger.info(f"N8N webhook {webhook_url} triggered successfully")
            return {
                "success": True,
                "data": result,
                "webhook_url": webhook_url,
                "status_code": response.status
            }
        else:
            error_text = await response.text()
            logger.error(f"N8N webhook failed: {response.status} - {error_text}")
            return {
                "success": False,
                "error": f"HTTP {response.status}: {error_text}",
                "webhook_url": webhook_url,
                "status_code": response.status
            }
    
    async def get_workflows(self) -> Dict[str, Any]:
        """
        Get list of available workflows.
        
        Returns:
            Dict containing the list of workflows
        """
        try:
            session = await self._get_session()
            url = f"{self.base_url}/api/v1/workflows"
            
            async with session.get(url) as response:
                if response.status == 200:
                    workflows = await response.json()
                    return {
                        "success": True,
                        "workflows": workflows
                    }
                else:
                    error_text = await response.text()
                    return {
                        "success": False,
                        "error": f"HTTP {response.status}: {error_text}"
                    }
                    
        except Exception as e:
            logger.error(f"Failed to get N8N workflows: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_workflow_status(self, execution_id: str) -> Dict[str, Any]:
        """
        Get the status of a workflow execution.
        
        Args:
            execution_id: The execution ID to check
            
        Returns:
            Dict containing the execution status
        """
        try:
            session = await self._get_session()
            url = f"{self.base_url}/api/v1/executions/{execution_id}"
            
            async with session.get(url) as response:
                if response.status == 200:
                    execution = await response.json()
                    return {
                        "success": True,
                        "execution": execution,
                        "status": execution.get("finished", False),
                        "execution_id": execution_id
                    }
                else:
                    error_text = await response.text()
                    return {
                        "success": False,
                        "error": f"HTTP {response.status}: {error_text}",
                        "execution_id": execution_id
                    }
                    
        except Exception as e:
            logger.error(f"Failed to get N8N execution status: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "execution_id": execution_id
            }


# Global N8N service instance
n8n_service = N8NService()


async def execute_n8n_workflow(
    workflow_id: str,
    input_data: Dict[str, Any],
    wait_for_completion: bool = True
) -> Dict[str, Any]:
    """
    Convenience function to execute an N8N workflow.
    
    Args:
        workflow_id: The ID of the workflow to execute
        input_data: Input data to pass to the workflow
        wait_for_completion: Whether to wait for workflow completion
        
    Returns:
        Dict containing the workflow execution result
    """
    return await n8n_service.execute_workflow(
        workflow_id=workflow_id,
        input_data=input_data,
        wait_for_completion=wait_for_completion
    )


async def trigger_n8n_webhook(
    webhook_url: str,
    data: Dict[str, Any],
    method: str = "POST"
) -> Dict[str, Any]:
    """
    Convenience function to trigger an N8N webhook.
    
    Args:
        webhook_url: The webhook URL to trigger
        data: Data to send to the webhook
        method: HTTP method to use
        
    Returns:
        Dict containing the webhook response
    """
    return await n8n_service.trigger_webhook(
        webhook_url=webhook_url,
        data=data,
        method=method
    )
