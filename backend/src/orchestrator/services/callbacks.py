import logging
import json
from datetime import datetime
from typing import Any, Dict, List, Union
from uuid import UUID

from langchain.callbacks.base import Async<PERSON><PERSON>backHandler
from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.outputs import LLMResult
from sqlalchemy.ext.asyncio import AsyncSession

from modules.call import crud as call_crud
from core.websocket.connection_manager import connection_manager
from modules.call.schemas import ConversationLogEvent

logger = logging.getLogger(__name__)

class OrchestrationCallbackHandler(AsyncCallbackHandler):
    """
    Callback handler for logging and broadcasting agent events.
    """
    def __init__(self, db: AsyncSession, call_history_id: int):
        self.db = db
        self.call_history_id = call_history_id

    async def _log_and_send_event(self, event_data: ConversationLogEvent):
        try:
            # Log the event to the database
            await call_crud.create_conversation_log(self.db, event_data)

            # Broadcast the event to the frontend client
            await connection_manager.send_personal_message(
                json.dumps({"type": "conversation_log", "log": event_data.model_dump(mode="json")}),
                self.call_history_id,
            )
        except Exception as e:
            logger.error(f"Error in callback handler: {e}", exc_info=True)
            # We don't rollback here as the session is managed by the dependency
            pass

    async def on_llm_start(
        self,
        serialized: Dict[str, Any],
        prompts: List[str],
        *,
        run_id: UUID,
        parent_run_id: Union[UUID, None] = None,
        tags: Union[List[str], None] = None,
        metadata: Union[Dict[str, Any], None] = None,
        **kwargs: Any,
    ) -> None:
        event = ConversationLogEvent(
            call_history_id=self.call_history_id,
            event_type="llm_start",
            timestamp=datetime.now(),
            data={"prompts": prompts, "metadata": metadata},
            source="orchestrator",
        )
        await self._log_and_send_event(event)

    async def on_llm_end(
        self,
        response: LLMResult,
        *,
        run_id: UUID,
        parent_run_id: Union[UUID, None] = None,
        **kwargs: Any,
    ) -> None:
        event = ConversationLogEvent(
            call_history_id=self.call_history_id,
            event_type="llm_end",
            timestamp=datetime.now(),
            data={"response": response.llm_output},
            source="orchestrator",
        )
        await self._log_and_send_event(event)

    async def on_tool_start(
        self,
        serialized: Dict[str, Any],
        input_str: str,
        *,
        run_id: UUID,
        parent_run_id: Union[UUID, None] = None,
        tags: Union[List[str], None] = None,
        metadata: Union[Dict[str, Any], None] = None,
        **kwargs: Any,
    ) -> None:
        event = ConversationLogEvent(
            call_history_id=self.call_history_id,
            event_type="tool_start",
            timestamp=datetime.now(),
            data={"tool_name": serialized.get("name"), "input": input_str, "metadata": metadata},
            source="orchestrator",
        )
        await self._log_and_send_event(event)

    async def on_tool_end(
        self,
        output: str,
        *,
        run_id: UUID,
        parent_run_id: Union[UUID, None] = None,
        **kwargs: Any,
    ) -> None:
        event = ConversationLogEvent(
            call_history_id=self.call_history_id,
            event_type="tool_end",
            timestamp=datetime.now(),
            data={"output": output},
            source="orchestrator",
        )
        await self._log_and_send_event(event)
