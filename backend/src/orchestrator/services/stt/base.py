import asyncio
from abc import ABC, abstractmethod
from orchestrator.schemas import SpeechConfig


class STTBase(ABC):
    """Abstract base class for speech-to-text services."""

    @abstractmethod
    async def transcribe(
        self,
        audio_queue: asyncio.Queue,
        transcript_queue: asyncio.Queue,
        sample_rate: int = 16000,
        speech_config: SpeechConfig = SpeechConfig(),
    ) -> None:
        """
        Transcribes audio froma queue and puts the transcript into another queue.

        Args:
            audio_queue: The queue to receive audio chunks from.
            transcript_queue: The queue to put the transcribed text into.
        """
        pass
