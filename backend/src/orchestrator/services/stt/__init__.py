from core.config import get_settings
from orchestrator.services.stt.assemblyai import AssemblyAI_STT
from orchestrator.services.stt.base import STTBase
from orchestrator.services.stt.nova import NovaSTT

settings = get_settings()


def get_stt_service() -> STTBase:
    provider = settings.STT_PROVIDER
    if provider == "assemblyai":
        return AssemblyAI_STT()
    elif provider == "nova":
        return NovaSTT()

    else:
        raise ValueError(f"Unsupported STT provider: {provider}")
