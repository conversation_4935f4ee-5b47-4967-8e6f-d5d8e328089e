import asyncio
import logging

import assemblyai as aai

from core.config import get_settings
from orchestrator.services.stt.base import STTBase
from orchestrator.schemas import SpeechConfig

logger = logging.getLogger(__name__)
settings = get_settings()


class AssemblyAI_STT(STTBase):
    def __init__(self):
        aai.settings.api_key = settings.ASSEMBLYAI_API_KEY

    async def transcribe(
        self,
        audio_queue: asyncio.Queue,
        transcript_queue: asyncio.Queue,
        sample_rate: int = 16000,
        speech_config: SpeechConfig = SpeechConfig(),
    ):
        transcriber = aai.RealtimeTranscriber(
            sample_rate=sample_rate,
            on_data=lambda transcript: self.on_data(transcript, transcript_queue),
            on_error=self.on_error,
        )

        await transcriber.connect()

        try:
            while True:
                audio_chunk = await audio_queue.get()
                if audio_chunk is None:
                    break
                await transcriber.stream(audio_chunk)
        except Exception as e:
            logger.error(f"Error in AssemblyAI transcription: {e}")
        finally:
            await transcriber.close()
            await transcript_queue.put((None, None))

    async def on_data(
        self, transcript: aai.RealtimeTranscript, transcript_queue: asyncio.Queue
    ):
        if not transcript.text:
            return

        if isinstance(transcript, aai.RealtimeFinalTranscript):
            await transcript_queue.put((transcript.text, True))
        else:
            await transcript_queue.put((transcript.text, False))

    async def on_error(self, error: aai.RealtimeError):
        logger.error(f"AssemblyAI error: {error}")
