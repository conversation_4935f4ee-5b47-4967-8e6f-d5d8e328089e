import logging
from sqlalchemy.ext.asyncio import AsyncSession
from orchestrator.services.llm_integrations.embedding_factory import BaseEmbeddingService
from modules.knowledge_items import crud as knowledge_crud
from core.config import get_settings

logger = logging.getLogger(__name__)

class RAGService:
    def __init__(
        self, db_session: AsyncSession, embedding_service: BaseEmbeddingService
    ):
        self.db = db_session
        self.embedding_service = embedding_service
        self.settings = get_settings()

    async def search(self, query: str, customer_id: int, top_k: int = 5) -> list[str]:
        """
        Searches for relevant knowledge items using vector similarity search.
        """
        logger.info(f"Performing RAG search for query: '{query}'")
        try:
            # The CRUD function handles the embedding and searching
            similar_chunks = await document_crud.find_similar_chunks(
                db=self.db,
                embedding_service=self.embedding_service,
                query_text=query,
                customer_id=customer_id,
                settings=self.settings,
                top_k=top_k,
            )

            if not similar_chunks:
                logger.info("No similar knowledge items found.")
                return []

            logger.info(f"Found {len(similar_chunks)} relevant knowledge items.")
            return similar_chunks

        except Exception as e:
            logger.error(f"Error during RAG search: {e}", exc_info=True)
            return []
