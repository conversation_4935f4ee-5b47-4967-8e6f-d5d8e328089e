import dspy
import logging
from typing import List, Dict, Any, Optional, <PERSON><PERSON>
from dataclasses import dataclass
from orchestrator.schemas import DSPyModuleType, NodeConfig
from orchestrator.services.llm_integrations.llm_factory import get_llm_service
from core.config import get_settings

logger = logging.getLogger(__name__)


@dataclass
class OptimizationExample:
    """Example for prompt optimization."""
    input_data: Dict[str, Any]
    expected_output: str
    quality_score: Optional[float] = None


@dataclass
class OptimizationResult:
    """Result of prompt optimization."""
    optimized_prompt: str
    original_prompt: str
    improvement_score: float
    examples_used: int
    optimization_method: str
    metrics: Dict[str, Any]


class PromptOptimizer:
    """Service for optimizing prompts using DSPy."""
    
    def __init__(self):
        self.settings = get_settings()
        self.llm_service = get_llm_service(self.settings)
        
        # Configure DSPy
        dspy.settings.configure(lm=self.llm_service.llm)
        
        # Initialize optimizers
        self.bootstrap_optimizer = dspy.BootstrapFewShot()
        self.teleprompt_optimizer = dspy.teleprompt.BootstrapFewShotWithRandomSearch()
    
    async def optimize_system_prompt(
        self,
        original_prompt: str,
        examples: List[OptimizationExample],
        optimization_method: str = "bootstrap",
        max_examples: int = 10
    ) -> OptimizationResult:
        """Optimize a system prompt using provided examples."""
        
        try:
            logger.info(f"Starting prompt optimization with {len(examples)} examples")
            
            # Create DSPy signature for the task
            signature = self._create_signature_from_examples(examples)
            
            # Create base module
            base_module = dspy.ChainOfThought(signature)
            
            # Prepare training data
            training_data = self._prepare_training_data(examples[:max_examples])
            
            # Choose optimization method
            if optimization_method == "bootstrap":
                optimizer = dspy.BootstrapFewShot(metric=self._quality_metric)
                optimized_module = optimizer.compile(base_module, trainset=training_data)
            elif optimization_method == "teleprompt":
                optimizer = dspy.teleprompt.BootstrapFewShotWithRandomSearch(
                    metric=self._quality_metric,
                    max_bootstrapped_demos=4,
                    max_labeled_demos=8
                )
                optimized_module = optimizer.compile(base_module, trainset=training_data)
            else:
                raise ValueError(f"Unknown optimization method: {optimization_method}")
            
            # Extract optimized prompt
            optimized_prompt = self._extract_optimized_prompt(optimized_module, original_prompt)
            
            # Evaluate improvement
            improvement_score = await self._evaluate_improvement(
                original_prompt, optimized_prompt, examples
            )
            
            return OptimizationResult(
                optimized_prompt=optimized_prompt,
                original_prompt=original_prompt,
                improvement_score=improvement_score,
                examples_used=len(training_data),
                optimization_method=optimization_method,
                metrics={
                    "training_examples": len(training_data),
                    "signature": signature,
                    "optimization_time": "N/A"  # Could add timing
                }
            )
            
        except Exception as e:
            logger.error(f"Error during prompt optimization: {e}")
            raise e
    
    def _create_signature_from_examples(self, examples: List[OptimizationExample]) -> str:
        """Create DSPy signature from examples."""
        if not examples:
            return "input -> output"
        
        # Analyze input keys from examples
        input_keys = set()
        for example in examples:
            input_keys.update(example.input_data.keys())
        
        input_part = ", ".join(sorted(input_keys))
        return f"{input_part} -> response"
    
    def _prepare_training_data(self, examples: List[OptimizationExample]) -> List[dspy.Example]:
        """Convert optimization examples to DSPy training data."""
        training_data = []
        
        for example in examples:
            # Create DSPy example
            dspy_example = dspy.Example(
                **example.input_data,
                response=example.expected_output
            ).with_inputs(*example.input_data.keys())
            
            training_data.append(dspy_example)
        
        return training_data
    
    def _quality_metric(self, example, prediction, trace=None) -> float:
        """Quality metric for optimization."""
        try:
            # Simple similarity-based metric
            expected = example.response.lower().strip()
            predicted = str(prediction.response).lower().strip() if hasattr(prediction, 'response') else str(prediction).lower().strip()
            
            # Basic similarity score (could be enhanced with semantic similarity)
            if expected == predicted:
                return 1.0
            elif expected in predicted or predicted in expected:
                return 0.7
            else:
                # Simple word overlap
                expected_words = set(expected.split())
                predicted_words = set(predicted.split())
                overlap = len(expected_words & predicted_words)
                total = len(expected_words | predicted_words)
                return overlap / total if total > 0 else 0.0
                
        except Exception as e:
            logger.error(f"Error in quality metric: {e}")
            return 0.0
    
    def _extract_optimized_prompt(self, optimized_module, original_prompt: str) -> str:
        """Extract optimized prompt from DSPy module."""
        try:
            # Try to extract the optimized prompt from the module
            if hasattr(optimized_module, 'predictor') and hasattr(optimized_module.predictor, 'signature'):
                # Get the signature and any optimized instructions
                signature = optimized_module.predictor.signature
                
                # If the module has been optimized, it might have better instructions
                if hasattr(optimized_module.predictor, 'extended_signature'):
                    return str(optimized_module.predictor.extended_signature)
                
                # Fallback to original with improvements
                return f"{original_prompt}\n\nOptimized with DSPy using signature: {signature}"
            
            # If we can't extract, return enhanced version of original
            return f"{original_prompt}\n\nOptimized with DSPy few-shot learning."
            
        except Exception as e:
            logger.error(f"Error extracting optimized prompt: {e}")
            return original_prompt
    
    async def _evaluate_improvement(
        self, 
        original_prompt: str, 
        optimized_prompt: str, 
        examples: List[OptimizationExample]
    ) -> float:
        """Evaluate improvement between original and optimized prompts."""
        
        if not examples:
            return 0.0
        
        try:
            original_scores = []
            optimized_scores = []
            
            # Test both prompts on a subset of examples
            test_examples = examples[:5]  # Use first 5 for evaluation
            
            for example in test_examples:
                # Test original prompt
                original_response = await self._test_prompt(original_prompt, example.input_data)
                original_score = self._score_response(original_response, example.expected_output)
                original_scores.append(original_score)
                
                # Test optimized prompt
                optimized_response = await self._test_prompt(optimized_prompt, example.input_data)
                optimized_score = self._score_response(optimized_response, example.expected_output)
                optimized_scores.append(optimized_score)
            
            # Calculate improvement
            avg_original = sum(original_scores) / len(original_scores)
            avg_optimized = sum(optimized_scores) / len(optimized_scores)
            
            improvement = avg_optimized - avg_original
            return improvement
            
        except Exception as e:
            logger.error(f"Error evaluating improvement: {e}")
            return 0.0
    
    async def _test_prompt(self, prompt: str, input_data: Dict[str, Any]) -> str:
        """Test a prompt with given input data."""
        try:
            from langchain_core.messages import SystemMessage, HumanMessage
            
            # Create user message from input data
            user_message = input_data.get("user_message", str(input_data))
            
            messages = [
                SystemMessage(content=prompt),
                HumanMessage(content=user_message)
            ]
            
            response = await self.llm_service.get_chat_completion(messages)
            return response
            
        except Exception as e:
            logger.error(f"Error testing prompt: {e}")
            return ""
    
    def _score_response(self, response: str, expected: str) -> float:
        """Score a response against expected output."""
        try:
            response_lower = response.lower().strip()
            expected_lower = expected.lower().strip()
            
            if response_lower == expected_lower:
                return 1.0
            elif expected_lower in response_lower:
                return 0.8
            elif response_lower in expected_lower:
                return 0.6
            else:
                # Word overlap score
                response_words = set(response_lower.split())
                expected_words = set(expected_lower.split())
                overlap = len(response_words & expected_words)
                total = len(response_words | expected_words)
                return overlap / total if total > 0 else 0.0
                
        except Exception as e:
            logger.error(f"Error scoring response: {e}")
            return 0.0
    
    async def optimize_node_config(
        self,
        node_config: NodeConfig,
        examples: List[OptimizationExample],
        optimization_method: str = "bootstrap"
    ) -> NodeConfig:
        """Optimize a node configuration using DSPy."""
        
        if node_config.type != "llm" or not node_config.system_prompt:
            logger.warning(f"Node {node_config.id} is not suitable for prompt optimization")
            return node_config
        
        # Optimize the system prompt
        optimization_result = await self.optimize_system_prompt(
            node_config.system_prompt,
            examples,
            optimization_method
        )
        
        # Create optimized node config
        optimized_config = node_config.copy(deep=True)
        optimized_config.system_prompt = optimization_result.optimized_prompt
        
        # Add optimization metadata
        if not optimized_config.ui_config:
            optimized_config.ui_config = {}
        
        optimized_config.ui_config["optimization"] = {
            "optimized": True,
            "improvement_score": optimization_result.improvement_score,
            "method": optimization_result.optimization_method,
            "examples_used": optimization_result.examples_used,
            "original_prompt": optimization_result.original_prompt
        }
        
        return optimized_config
    
    def create_optimization_examples_from_logs(
        self,
        conversation_logs: List[Dict[str, Any]]
    ) -> List[OptimizationExample]:
        """Create optimization examples from conversation logs."""
        examples = []
        
        for log in conversation_logs:
            try:
                # Extract input and expected output from log
                user_message = log.get("user_message", "")
                agent_response = log.get("agent_response", "")
                quality_score = log.get("quality_score")
                
                if user_message and agent_response:
                    example = OptimizationExample(
                        input_data={"user_message": user_message},
                        expected_output=agent_response,
                        quality_score=quality_score
                    )
                    examples.append(example)
                    
            except Exception as e:
                logger.error(f"Error creating example from log: {e}")
                continue
        
        return examples


# Global optimizer instance
_prompt_optimizer = None

def get_prompt_optimizer() -> PromptOptimizer:
    """Get global prompt optimizer instance."""
    global _prompt_optimizer
    if _prompt_optimizer is None:
        _prompt_optimizer = PromptOptimizer()
    return _prompt_optimizer
