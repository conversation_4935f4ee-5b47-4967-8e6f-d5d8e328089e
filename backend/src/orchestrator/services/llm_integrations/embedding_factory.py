from abc import ABC, abstractmethod
from typing import List

from langchain_google_genai import GoogleGenerativeAIEmbeddings
from langchain_openai import OpenAIEmbeddings
from core.config import Settings


class BaseEmbeddingService(ABC):
    @abstractmethod
    async def aget_embedding(self, text: str) -> List[float]:
        pass


class GeminiEmbeddingService(BaseEmbeddingService):
    def __init__(self, settings: Settings):
        self.settings = settings
        self.embeddings_model = GoogleGenerativeAIEmbeddings(
            model="models/embedding-001",
            google_api_key=self.settings.GEMINI_API_KEY,
        )

    async def aget_embedding(self, text: str) -> List[float]:
        # The aembed_query method returns a list of embeddings, we need the first one
        embedding = await self.embeddings_model.aembed_query(text)
        return embedding


class OpenAIEmbeddingService(BaseEmbeddingService):
    def __init__(self, settings: Settings):
        self.settings = settings
        self.embeddings_model = OpenAIEmbeddings(
            model="text-embedding-ada-002",  # Or another appropriate OpenAI embedding model
            api_key=self.settings.OPENAI_API_KEY,
        )

    async def aget_embedding(self, text: str) -> List[float]:
        embedding = await self.embeddings_model.aembed_query(text)
        return embedding


def get_embedding_service(settings: Settings) -> BaseEmbeddingService:
    if settings.LLM_PROVIDER == "gemini":
        return GeminiEmbeddingService(settings)
    elif settings.LLM_PROVIDER == "openai":
        return OpenAIEmbeddingService(settings)
    else:
        raise ValueError(
            f"Unsupported LLM_PROVIDER for embeddings: {settings.LLM_PROVIDER}"
        )
