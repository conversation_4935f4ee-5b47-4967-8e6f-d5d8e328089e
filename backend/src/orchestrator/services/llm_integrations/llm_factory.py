from abc import ABC, abstractmethod
from typing import List
from collections.abc import As<PERSON><PERSON>enerator as AsyncGeneratorAB<PERSON>

from langchain_google_genai import ChatGoogleGenerativeAI
from openai import AsyncOpenAI
from core.config import Settings
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from openai.types.chat import (
    ChatCompletionMessageParam,
    ChatCompletionUserMessageParam,
    ChatCompletionAssistantMessageParam,
    ChatCompletionSystemMessageParam,
)

GEMENI_MODEL = "gemini-2.0-flash"
OPENAI_MODEL = "gpt-4o"

class BaseLLMService(ABC):
    @abstractmethod
    async def get_chat_completion(self, messages: List[BaseMessage]) -> str:
        pass

    @abstractmethod
    def get_chat_completion_stream(
        self, messages: List[BaseMessage]
    ) -> AsyncGeneratorABC[str, None]:
        pass


class GeminiLLMService(BaseLLMService):
    def __init__(self, settings: Settings):
        self.settings = settings
        self.llm = ChatGoogleGenerativeAI(
            model=GEMENI_MODEL,
            google_api_key=self.settings.GEMINI_API_KEY,
            disable_streaming=False,
        )

    async def get_chat_completion(self, messages: List[BaseMessage]) -> str:
        # Assuming the LLM expects messages in a specific format, adjust if needed
        response = await self.llm.ainvoke(messages)
        return str(response.content)

    async def get_chat_completion_stream(
        self, messages: List[BaseMessage]
    ) -> AsyncGeneratorABC[str, None]:
        async for chunk in self.llm.astream(messages):
            if isinstance(chunk.content, str):
                yield chunk.content


class OpenAILLMService(BaseLLMService):
    def __init__(self, settings: Settings):
        self.settings = settings
        self.client = AsyncOpenAI(api_key=self.settings.OPENAI_API_KEY)

    def _convert_messages_to_openai_format(
        self, messages: List[BaseMessage]
    ) -> List[ChatCompletionMessageParam]:
        openai_messages: List[ChatCompletionMessageParam] = []
        for msg in messages:
            if isinstance(msg, HumanMessage):
                openai_messages.append(
                    ChatCompletionUserMessageParam(
                        role="user", content=str(msg.content)
                    )
                )
            elif isinstance(msg, AIMessage):
                openai_messages.append(
                    ChatCompletionAssistantMessageParam(
                        role="assistant", content=str(msg.content)
                    )
                )
            elif isinstance(msg, SystemMessage):
                openai_messages.append(
                    ChatCompletionSystemMessageParam(
                        role="system", content=str(msg.content)
                    )
                )
            # Add handling for other message types if needed (e.g., ToolMessage)
        return openai_messages

    async def get_chat_completion(self, messages: List[BaseMessage]) -> str:
        openai_messages = self._convert_messages_to_openai_format(messages)
        response = await self.client.chat.completions.create(
            model=OPENAI_MODEL,
            messages=openai_messages,
            stream=False,
        )
        return response.choices[0].message.content or ""

    async def get_chat_completion_stream(
        self, messages: List[BaseMessage]
    ) -> AsyncGeneratorABC[str, None]:
        openai_messages = self._convert_messages_to_openai_format(messages)
        stream = await self.client.chat.completions.create(
            model="gpt-4o",  # Or another appropriate OpenAI model
            messages=openai_messages,
            stream=True,
        )
        async for chunk in stream:
            if chunk.choices[0].delta.content is not None:
                yield chunk.choices[0].delta.content


def get_llm_service(settings: Settings) -> BaseLLMService:
    if settings.LLM_PROVIDER == "gemini":
        return GeminiLLMService(settings)
    elif settings.LLM_PROVIDER == "openai":
        return OpenAILLMService(settings)
    else:
        raise ValueError(f"Unsupported LLM_PROVIDER: {settings.LLM_PROVIDER}")


AVAILABLE_LLMS = {
    "gemini": GeminiLLMService,
    "openai": OpenAILLMService,
}
