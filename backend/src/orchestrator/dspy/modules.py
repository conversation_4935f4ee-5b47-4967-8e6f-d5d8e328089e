import dspy
from typing import List, Dict, Any, Optional
from orchestrator.schemas import DSPyModuleType


class IntentClassificationProgram(dspy.Module):
    def __init__(self):
        super().__init__()
        self.intent_classifier = dspy.Predict("user_message -> intent")

    def forward(self, user_message):
        return self.intent_classifier(user_message=user_message)


class ConversationAnalysisProgram(dspy.Module):
    """Analyzes conversation context and extracts key information."""

    def __init__(self):
        super().__init__()
        self.analyzer = dspy.ChainOfThought("conversation_history, user_message -> analysis, sentiment, key_points")

    def forward(self, conversation_history, user_message):
        return self.analyzer(conversation_history=conversation_history, user_message=user_message)


class ResponseGenerationProgram(dspy.Module):
    """Generates contextually appropriate responses."""

    def __init__(self, system_prompt: str = None):
        super().__init__()
        self.system_prompt = system_prompt or "You are a helpful AI assistant."
        self.generator = dspy.ChainOfThought("system_prompt, context, user_message -> response")

    def forward(self, context, user_message):
        return self.generator(
            system_prompt=self.system_prompt,
            context=context,
            user_message=user_message
        )


class MemoryRetrievalProgram(dspy.Module):
    """Retrieves relevant memories based on current context."""

    def __init__(self):
        super().__init__()
        self.retriever = dspy.Retrieve(k=5)
        self.relevance_scorer = dspy.Predict("query, memory -> relevance_score")

    def forward(self, query, memory_entries):
        # Retrieve relevant memories
        retrieved = self.retriever(query)

        # Score relevance for each memory
        scored_memories = []
        for memory in memory_entries:
            score = self.relevance_scorer(query=query, memory=memory)
            scored_memories.append({
                "memory": memory,
                "relevance_score": score.relevance_score
            })

        return scored_memories


class TaskPlanningProgram(dspy.Module):
    """Plans task execution based on user intent and available tools."""

    def __init__(self):
        super().__init__()
        self.planner = dspy.ChainOfThought("user_intent, available_tools, context -> execution_plan, tool_sequence")

    def forward(self, user_intent, available_tools, context):
        return self.planner(
            user_intent=user_intent,
            available_tools=available_tools,
            context=context
        )


class QualityAssessmentProgram(dspy.Module):
    """Assesses the quality of generated responses."""

    def __init__(self):
        super().__init__()
        self.assessor = dspy.Predict("response, user_message, context -> quality_score, feedback")

    def forward(self, response, user_message, context):
        return self.assessor(
            response=response,
            user_message=user_message,
            context=context
        )


class DSPyModuleFactory:
    """Factory for creating DSPy modules based on configuration."""

    @staticmethod
    def create_module(module_type: DSPyModuleType, config: Dict[str, Any]) -> dspy.Module:
        """Create a DSPy module based on type and configuration."""

        if module_type == DSPyModuleType.CLASSIFY:
            return IntentClassificationProgram()

        elif module_type == DSPyModuleType.CHAIN_OF_THOUGHT:
            signature = config.get("signature", "input -> output")
            return dspy.ChainOfThought(signature)

        elif module_type == DSPyModuleType.PREDICT:
            signature = config.get("signature", "input -> output")
            return dspy.Predict(signature)

        elif module_type == DSPyModuleType.REACT:
            signature = config.get("signature", "input -> output")
            tools = config.get("tools", [])
            return dspy.ReAct(signature, tools=tools)

        elif module_type == DSPyModuleType.RETRIEVE:
            k = config.get("k", 5)
            return dspy.Retrieve(k=k)

        elif module_type == DSPyModuleType.GENERATE:
            return ResponseGenerationProgram(config.get("system_prompt"))

        elif module_type == DSPyModuleType.SUMMARIZE:
            signature = config.get("signature", "text -> summary")
            return dspy.ChainOfThought(signature)

        else:
            raise ValueError(f"Unsupported DSPy module type: {module_type}")


def get_available_dspy_modules() -> List[Dict[str, Any]]:
    """Get list of available DSPy modules with their descriptions."""
    return [
        {
            "type": DSPyModuleType.CLASSIFY,
            "name": "Intent Classification",
            "description": "Classifies user intent from messages",
            "signature": "user_message -> intent",
            "examples": ["What is the user trying to do?"]
        },
        {
            "type": DSPyModuleType.CHAIN_OF_THOUGHT,
            "name": "Chain of Thought",
            "description": "Performs step-by-step reasoning",
            "signature": "problem -> reasoning, answer",
            "examples": ["Think through this step by step"]
        },
        {
            "type": DSPyModuleType.PREDICT,
            "name": "Simple Prediction",
            "description": "Makes direct predictions without reasoning",
            "signature": "input -> output",
            "examples": ["Direct question answering"]
        },
        {
            "type": DSPyModuleType.REACT,
            "name": "ReAct (Reasoning + Acting)",
            "description": "Combines reasoning with tool usage",
            "signature": "task -> thought, action, observation",
            "examples": ["Complex task execution with tools"]
        },
        {
            "type": DSPyModuleType.RETRIEVE,
            "name": "Information Retrieval",
            "description": "Retrieves relevant information from knowledge base",
            "signature": "query -> retrieved_passages",
            "examples": ["Find relevant documents"]
        },
        {
            "type": DSPyModuleType.GENERATE,
            "name": "Response Generation",
            "description": "Generates contextual responses",
            "signature": "context, user_message -> response",
            "examples": ["Generate helpful responses"]
        },
        {
            "type": DSPyModuleType.SUMMARIZE,
            "name": "Text Summarization",
            "description": "Summarizes long text content",
            "signature": "text -> summary",
            "examples": ["Summarize documents or conversations"]
        }
    ]
