# This file will contain the tools that the LangG<PERSON>h agent can use.
from modules.calendar import service as calendar_service


def book_slot(
    job_type: str, duration: int, window: str, address: str, contact_details: dict
):
    """
    Books an appointment slot.
    """
    print(f"Tool: book_slot called with: {job_type}, {duration}, {window}, {address}")
    available_slots = calendar_service.find_available_slots(job_type, duration, window)
    if available_slots:
        # For now, just book the first available slot
        return calendar_service.book_appointment(available_slots[0], contact_details)
    else:
        return "I couldn't find any available slots."


def handoff_human(queue: str):
    """
    Handoffs the call to a human agent.
    """
    print(f"Tool: handoff_human called with queue: {queue}")
    return "Transferring you to a human agent."
