import json
import os
from orchestrator.schemas import AgentConfig

import logging

logger = logging.getLogger(__name__)

CONFIG_DIR = os.path.join(os.path.dirname(__file__), "templates")

def load_agent_template(template_id: str = "default_agent") -> AgentConfig:
    """
    Loads a specific agent template file from the templates directory.
    """
    config_path = os.path.join(CONFIG_DIR, f"{template_id}.json")
    logger.info(f"Loading agent template from: {config_path}")

    if not os.path.exists(config_path):
        raise FileNotFoundError(
            f"Agent template for template_id '{template_id}' not found at {config_path}"
        )

    with open(config_path, "r") as f:
        config_data = json.load(f)

    return AgentConfig(**config_data)