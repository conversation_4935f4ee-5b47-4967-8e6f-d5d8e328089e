import logging
from sqlalchemy.ext.asyncio import AsyncSession
from orchestrator.models import AgentState
from orchestrator.services.llm_integrations.llm_factory import BaseLLMService
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
import dspy
from orchestrator.dspy.modules import IntentClassificationProgram
from orchestrator.services.memory.mem0_service import Mem0Service

logger = logging.getLogger(__name__)


async def generate_response(
    state: AgentState, llm_service: BaseLLMService, db: AsyncSession
) -> dict:
    """
    Handles the main conversation logic with the LLM, intent extraction, and memory.
    Returns a dictionary with the updated state fields.
    """
    logger.info("---Node: generate_response---")
    updated_state = {}

    import logging
from sqlalchemy.ext.asyncio import AsyncSession
from orchestrator.models import AgentState
from orchestrator.services.llm_integrations.llm_factory import BaseLLMService
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage

logger = logging.getLogger(__name__)


async def generate_response(
    state: AgentState, llm_service: BaseLLMService, db: AsyncSession
) -> dict:
    """
    Handles the main conversation logic with the LLM, streaming the response back.
    Returns a dictionary with the updated state fields.
    """
    logger.info("---Node: generate_response---")
    updated_state = {}
    output_handler = state.output_handler

    system_prompt = (
        state.system_prompt
        or "You are a helpful AI assistant. Your goal is to have a natural conversation with the user, understand their needs, and assist them. Be friendly and concise."
    )

    llm_messages = [SystemMessage(content=system_prompt)]
    for msg in state.messages:
        role = msg.get("role")
        content = msg.get("content")
        if role == "user":
            llm_messages.append(HumanMessage(content=content))
        elif role == "assistant":
            llm_messages.append(AIMessage(content=content))

    logger.info("Calling LLM for a direct conversational response...")
    
    try:
        # Define a generator that streams from the LLM and accumulates the full response
        async def stream_and_accumulate():
            full_response_list = []
            # Use the injected llm_service directly to get the stream
            async for chunk in llm_service.get_chat_completion_stream(llm_messages):
                full_response_list.append(chunk)
                yield chunk
            
            # After streaming, update the state with the final message
            response_text = "".join(full_response_list).strip()
            new_messages = state.messages + [{"role": "assistant", "content": response_text}]
            updated_state["messages"] = new_messages
            # No "response" field needed as we are handling the output directly
            logger.info(f"LLM generated full response: '{response_text}'")

        # The output_handler will now consume the stream and send it to the client
        await output_handler(stream_and_accumulate())

    except Exception as e:
        logger.error(f"Error in generate_response: {e}", exc_info=True)
        error_message = "I'm sorry, I'm having trouble responding right now."
        updated_state["error"] = error_message
        
        # Manually send the error message if streaming fails
        async def error_stream():
            yield error_message
        await output_handler(error_stream())

    logger.info("---End Node: generate_response---")
    return updated_state


    # Use DSPy to extract the user's intent
    if user_message:
        try:
            # The llm_service *is* the LLM, so we can use it directly.
            # Note: DSPy expects a LangChain-compatible LLM object.
            # We need to ensure the llm_service's LLM instance is compatible.
            # Assuming GeminiLLMService has a .llm attribute that is the ChatGoogleGenerativeAI instance.
            dspy.settings.configure(lm=llm_service.llm)

            intent_program = IntentClassificationProgram()
            prediction = intent_program(user_message=user_message)
            if prediction and prediction.intent:
                updated_state["intent"] = prediction.intent
                logger.info(f"Extracted intent: {updated_state['intent']}")
        except Exception as e:
            logger.error(f"Error using DSPy: {e}", exc_info=True)

    # Use Mem0 to add and search for relevant information
    if user_message:
        try:
            user_id = state.customer_id or "default_user"
            mem0_service = Mem0Service(user_id=str(user_id))
            mem0_service.add_memory(user_message)
            relevant_memory = mem0_service.search_memory(user_message)
            if relevant_memory:
                updated_state["rag_results"] = relevant_memory
                logger.info(f"Found relevant memory: {relevant_memory}")
        except Exception as e:
            logger.error(f"Error using Mem0: {e}", exc_info=True)

    system_prompt = (
        state.system_prompt
        or "You are a helpful AI assistant. Your goal is to have a natural conversation with the user, understand their needs, and assist them. Be friendly and concise."
    )

    # Add relevant memory to the system prompt
    if updated_state.get("rag_results"):
        system_prompt += f"\n\nHere is some relevant information from our previous conversations:\n{updated_state['rag_results']}"

    llm_messages = [SystemMessage(content=system_prompt)]
    for msg in state.messages:
        role = msg.get("role")
        content = msg.get("content")
        if role == "user":
            llm_messages.append(HumanMessage(content=content))
        elif role == "assistant":
            llm_messages.append(AIMessage(content=content))

    logger.info("Calling LLM for a direct conversational response...")
    full_response = []
    try:
        # Use the injected llm_service directly to get the stream
        async for chunk in llm_service.get_chat_completion_stream(
            llm_messages
        ):
            full_response.append(chunk)

        response_text = "".join(full_response).strip()

        new_messages = state.messages + [{"role": "assistant", "content": response_text}]
        updated_state["messages"] = new_messages
        updated_state["response"] = response_text
        logger.info(f"LLM generated response: '{response_text}'")

    except Exception as e:
        logger.error(f"Error in generate_response: {e}", exc_info=True)
        updated_state["error"] = "I'm sorry, I'm having trouble responding right now."
        updated_state["response"] = "I'm sorry, I'm having trouble responding right now."

    logger.info("---End Node: generate_response---")
    return updated_state
