"""
N8N Node for Orchestrator

This node allows the orchestrator to integrate with N8N workflows,
enabling complex automation and external service integrations.
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional, Union

from ..services.n8n_service import n8n_service

logger = logging.getLogger(__name__)


async def execute_n8n_workflow_node(
    input_data: Dict[str, Any],
    node_config: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Execute an N8N workflow node.
    
    Args:
        input_data: Input data from the previous node
        node_config: Node configuration containing N8N settings
        
    Returns:
        Dict containing the workflow execution result
    """
    try:
        # Extract N8N configuration
        n8n_config = node_config.get("n8n_config", {})
        workflow_id = n8n_config.get("workflow_id")
        input_mapping = n8n_config.get("input_mapping", {})
        output_mapping = n8n_config.get("output_mapping", {})
        wait_for_completion = n8n_config.get("wait_for_completion", True)
        
        if not workflow_id:
            return {
                "success": False,
                "error": "No workflow_id specified in n8n_config",
                "output": input_data
            }
        
        # Map input data according to configuration
        mapped_input = {}
        if input_mapping:
            for n8n_key, input_key in input_mapping.items():
                if input_key in input_data:
                    mapped_input[n8n_key] = input_data[input_key]
                else:
                    logger.warning(f"Input key '{input_key}' not found in input_data")
        else:
            # If no mapping specified, pass all input data
            mapped_input = input_data
        
        logger.info(f"Executing N8N workflow {workflow_id} with mapped input: {mapped_input}")
        
        # Execute the workflow
        result = await n8n_service.execute_workflow(
            workflow_id=workflow_id,
            input_data=mapped_input,
            wait_for_completion=wait_for_completion
        )
        
        if result["success"]:
            # Map output data according to configuration
            workflow_output = result.get("data", {})
            
            if output_mapping:
                mapped_output = {}
                for output_key, n8n_key in output_mapping.items():
                    if n8n_key in workflow_output:
                        mapped_output[output_key] = workflow_output[n8n_key]
                    else:
                        logger.warning(f"N8N output key '{n8n_key}' not found in workflow result")
                
                # Merge with original input data
                final_output = {**input_data, **mapped_output}
            else:
                # If no mapping specified, merge workflow output with input
                final_output = {**input_data, "n8n_result": workflow_output}
            
            return {
                "success": True,
                "output": final_output,
                "n8n_execution_id": result.get("execution_id"),
                "workflow_id": workflow_id
            }
        else:
            return {
                "success": False,
                "error": result.get("error", "Unknown N8N workflow error"),
                "output": input_data,
                "workflow_id": workflow_id
            }
            
    except Exception as e:
        logger.error(f"N8N workflow node execution error: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "output": input_data
        }


async def trigger_n8n_webhook_node(
    input_data: Dict[str, Any],
    node_config: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Trigger an N8N webhook node.
    
    Args:
        input_data: Input data from the previous node
        node_config: Node configuration containing webhook settings
        
    Returns:
        Dict containing the webhook response
    """
    try:
        # Extract N8N configuration
        n8n_config = node_config.get("n8n_config", {})
        webhook_url = n8n_config.get("webhook_url")
        method = n8n_config.get("method", "POST")
        input_mapping = n8n_config.get("input_mapping", {})
        output_mapping = n8n_config.get("output_mapping", {})
        
        if not webhook_url:
            return {
                "success": False,
                "error": "No webhook_url specified in n8n_config",
                "output": input_data
            }
        
        # Map input data according to configuration
        mapped_input = {}
        if input_mapping:
            for webhook_key, input_key in input_mapping.items():
                if input_key in input_data:
                    mapped_input[webhook_key] = input_data[input_key]
                else:
                    logger.warning(f"Input key '{input_key}' not found in input_data")
        else:
            # If no mapping specified, pass all input data
            mapped_input = input_data
        
        logger.info(f"Triggering N8N webhook {webhook_url} with mapped input: {mapped_input}")
        
        # Trigger the webhook
        result = await n8n_service.trigger_webhook(
            webhook_url=webhook_url,
            data=mapped_input,
            method=method
        )
        
        if result["success"]:
            # Map output data according to configuration
            webhook_output = result.get("data", {})
            
            if output_mapping:
                mapped_output = {}
                for output_key, webhook_key in output_mapping.items():
                    if isinstance(webhook_output, dict) and webhook_key in webhook_output:
                        mapped_output[output_key] = webhook_output[webhook_key]
                    else:
                        logger.warning(f"Webhook output key '{webhook_key}' not found in response")
                
                # Merge with original input data
                final_output = {**input_data, **mapped_output}
            else:
                # If no mapping specified, merge webhook output with input
                final_output = {**input_data, "webhook_result": webhook_output}
            
            return {
                "success": True,
                "output": final_output,
                "webhook_url": webhook_url,
                "status_code": result.get("status_code")
            }
        else:
            return {
                "success": False,
                "error": result.get("error", "Unknown webhook error"),
                "output": input_data,
                "webhook_url": webhook_url
            }
            
    except Exception as e:
        logger.error(f"N8N webhook node execution error: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "output": input_data
        }


async def n8n_node(
    input_data: Dict[str, Any],
    node_config: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Main N8N node function that can handle both workflows and webhooks.
    
    Args:
        input_data: Input data from the previous node
        node_config: Node configuration
        
    Returns:
        Dict containing the execution result
    """
    try:
        n8n_config = node_config.get("n8n_config", {})
        
        # Determine execution mode based on configuration
        if "workflow_id" in n8n_config:
            return await execute_n8n_workflow_node(input_data, node_config)
        elif "webhook_url" in n8n_config:
            return await trigger_n8n_webhook_node(input_data, node_config)
        else:
            return {
                "success": False,
                "error": "N8N node requires either workflow_id or webhook_url in n8n_config",
                "output": input_data
            }
            
    except Exception as e:
        logger.error(f"N8N node error: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "output": input_data
        }


# Example configurations for different N8N node types

EXAMPLE_WORKFLOW_CONFIG = {
    "id": "n8n_workflow_node",
    "name": "N8N Workflow Node",
    "type": "n8n",
    "dependencies": ["n8n_service"],
    "parameters": {},
    "n8n_config": {
        "workflow_id": "workflow-123",
        "wait_for_completion": True,
        "input_mapping": {
            "user_message": "message",
            "user_id": "user_id"
        },
        "output_mapping": {
            "processed_text": "result",
            "confidence": "confidence_score"
        }
    },
    "description": "Execute N8N workflow with input/output mapping"
}

EXAMPLE_WEBHOOK_CONFIG = {
    "id": "n8n_webhook_node",
    "name": "N8N Webhook Node",
    "type": "n8n",
    "dependencies": ["n8n_service"],
    "parameters": {},
    "n8n_config": {
        "webhook_url": "http://localhost:5678/webhook/process-data",
        "method": "POST",
        "input_mapping": {
            "data": "input_text",
            "options": "processing_options"
        },
        "output_mapping": {
            "result": "processed_result"
        }
    },
    "description": "Trigger N8N webhook with data processing"
}

EXAMPLE_SIMPLE_CONFIG = {
    "id": "n8n_simple_node",
    "name": "Simple N8N Node",
    "type": "n8n",
    "dependencies": ["n8n_service"],
    "parameters": {},
    "n8n_config": {
        "webhook_url": "http://localhost:5678/webhook/simple-process"
        # No mapping - passes all input data and returns webhook result
    },
    "description": "Simple N8N webhook trigger without mapping"
}
