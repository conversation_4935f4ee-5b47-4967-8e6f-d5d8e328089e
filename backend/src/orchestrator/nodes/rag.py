import logging
from sqlalchemy.ext.asyncio import AsyncSession
from orchestrator.models import Agent<PERSON><PERSON>
from orchestrator.services.knowledge.rag_service import RAGService
from orchestrator.services.llm_integrations.embedding_factory import get_embedding_service
from core.config import get_settings

logger = logging.getLogger(__name__)

async def rag_fetch_node(state: AgentState, db: AsyncSession) -> dict:
    """
    Fetches relevant information from the knowledge base using the RAG service.
    """
    logger.info("---Node: rag_fetch_node---")
    updated_state = {}
    
    user_message = state.user_message
    customer_id = state.customer_id

    if not user_message or not customer_id:
        logger.warning("User message or customer ID not found in state. Skipping RAG fetch.")
        return updated_state

    try:
        # Initialize the RAG service
        settings = get_settings()
        embedding_service = get_embedding_service(settings)
        rag_service = RAGService(db_session=db, embedding_service=embedding_service)

        # Perform the search
        rag_results = await rag_service.search(query=user_message, customer_id=customer_id)

        if rag_results:
            updated_state["rag_results"] = rag_results
            logger.info(f"RAG fetch successful. Found {len(rag_results)} results.")
        else:
            logger.info("RAG fetch did not return any results.")

    except Exception as e:
        logger.error(f"Error in rag_fetch_node: {e}", exc_info=True)
        updated_state["error"] = "Failed to fetch information from the knowledge base."

    logger.info("---End Node: rag_fetch_node---")
    return updated_state
