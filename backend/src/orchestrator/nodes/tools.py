import logging
from orchestrator.models import AgentState
from orchestrator.tools.registry import AVAILABLE_TOOLS

logger = logging.getLogger(__name__)

async def tool_executor(state: AgentState) -> dict:
    """
    Executes a tool based on the 'tool_call' field in the state.
    """
    logger.info("---Node: tool_executor---")
    updated_state = {}
    
    tool_call = state.get("tool_call")
    if not tool_call:
        logger.warning("No tool call found in state.")
        return updated_state

    tool_name = tool_call.get("name")
    tool_args = tool_call.get("args", {})

    if tool_name in AVAILABLE_TOOLS:
        tool = AVAILABLE_TOOLS[tool_name]
        try:
            logger.info(f"Executing tool: {tool_name} with args: {tool_args}")
            # Langchain tools can be sync or async, we should handle both
            # For simplicity, assuming async here as our tools are async
            result = await tool.ainvoke(tool_args)
            
            # Add the result to the state. A common pattern is to append it to messages.
            tool_message = {
                "role": "tool",
                "name": tool_name,
                "content": str(result), # Ensure content is string
            }
            updated_state["messages"] = state.messages + [tool_message]
            logger.info(f"Tool {tool_name} executed successfully.")

        except Exception as e:
            logger.error(f"Error executing tool {tool_name}: {e}", exc_info=True)
            error_message = {
                "role": "tool",
                "name": tool_name,
                "content": f"Error: {e}",
            }
            updated_state["messages"] = state.messages + [error_message]
    else:
        logger.error(f"Tool '{tool_name}' not found in registry.")
        error_message = {
            "role": "tool",
            "name": tool_name,
            "content": f"Error: Tool '{tool_name}' not found.",
        }
        updated_state["messages"] = state.messages + [error_message]

    logger.info("---End Node: tool_executor---")
    return updated_state
