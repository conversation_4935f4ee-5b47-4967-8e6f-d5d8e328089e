from typing import List, Dict, Any, Optional, Union, Literal
from pydantic import BaseModel, Field, RootModel
from enum import Enum


class NodeType(str, Enum):
    PYTHON_FUNCTION = "pythonFunction"
    LLM = "llm"
    TOOL = "tool"
    CONDITIONAL = "conditional"
    MEMORY = "memory"
    DSPY_MODULE = "dspyModule"
    RAG = "rag"
    INPUT = "input"
    OUTPUT = "output"


class MemoryType(str, Enum):
    SHORT_TERM = "short_term"
    LONG_TERM = "long_term"
    EPISODIC = "episodic"
    SEMANTIC = "semantic"
    WORKING = "working"


class DSPyModuleType(str, Enum):
    PREDICT = "predict"
    CHAIN_OF_THOUGHT = "chain_of_thought"
    REACT = "react"
    RETRIEVE = "retrieve"
    GENERATE = "generate"
    CLASSIFY = "classify"
    SUMMARIZE = "summarize"


class MemoryConfig(BaseModel):
    type: MemoryType
    max_entries: Optional[int] = 100
    ttl_seconds: Optional[int] = None
    embedding_model: Optional[str] = None
    similarity_threshold: Optional[float] = 0.7


class DSPyConfig(BaseModel):
    module_type: DSPyModuleType
    signature: str
    examples: List[Dict[str, Any]] = Field(default_factory=list)
    optimizer: Optional[str] = None
    max_bootstrapped_demos: Optional[int] = 4
    max_labeled_demos: Optional[int] = 16


class NodeConfig(BaseModel):
    id: str
    name: str
    type: NodeType
    functionPath: Optional[str] = None
    parameters: Dict[str, Any] = Field(default_factory=dict)
    dependencies: List[str] = Field(default_factory=list)
    position: Optional[List[float]] = None

    # Enhanced configurations
    system_prompt: Optional[str] = None
    memory_config: Optional[MemoryConfig] = None
    dspy_config: Optional[DSPyConfig] = None
    tools: Optional[List[str]] = Field(default_factory=list)

    # UI-specific properties
    ui_config: Optional[Dict[str, Any]] = Field(default_factory=dict)
    description: Optional[str] = None
    category: Optional[str] = None


class ConnectionTarget(BaseModel):
    node: str
    type: str
    index: int


class MainConnection(BaseModel):
    main: List[ConnectionTarget]


class ConditionalPaths(RootModel[Dict[str, List[ConnectionTarget]]]):
    pass


class ConditionalConnection(BaseModel):
    type: str
    conditionFunctionPath: Optional[str] = None
    prompt: Optional[str] = None
    paths: ConditionalPaths


class Connections(
    RootModel[Dict[str, Union[MainConnection, Dict[str, ConditionalConnection]]]]
):
    pass


# N8N-style Tool Schemas
class ToolParameterProperty(BaseModel):
    id: str
    type: str
    value_type: str
    description: Optional[str] = None
    dynamic_variable: Optional[str] = None
    constant_value: Optional[Any] = None
    required: bool = False
    properties: Optional[List['ToolParameterProperty']] = None

ToolParameterProperty.update_forward_refs()

class ToolParameter(BaseModel):
    id: str
    type: str
    description: Optional[str] = None
    properties: List[ToolParameterProperty]
    required: bool = False

class RequestHeader(BaseModel):
    type: str
    name: str
    value: Optional[str] = None
    secret_id: Optional[str] = None

class ApiSchema(BaseModel):
    url: str
    method: str
    path_params_schema: List[ToolParameterProperty] = Field(default_factory=list)
    query_params_schema: List[ToolParameterProperty] = Field(default_factory=list)
    request_body_schema: Optional[ToolParameter] = None
    request_headers: List[RequestHeader] = Field(default_factory=list)

class ToolParameterType(str, Enum):
    STRING = "string"
    INTEGER = "integer"
    FLOAT = "float"
    BOOLEAN = "boolean"
    ARRAY = "array"
    OBJECT = "object"
    FILE = "file"


class EnhancedToolParameter(BaseModel):
    name: str
    type: ToolParameterType
    description: str
    required: bool = True
    default: Optional[Any] = None
    enum_values: Optional[List[Any]] = None
    min_value: Optional[Union[int, float]] = None
    max_value: Optional[Union[int, float]] = None
    pattern: Optional[str] = None
    examples: List[Any] = Field(default_factory=list)


# Added ToolMetadata definition
class ToolMetadata(BaseModel):
    name: str
    description: str
    parameters: List[EnhancedToolParameter] = Field(default_factory=list)


class WebhookToolConfig(BaseModel):
    type: str = "webhook"
    name: str
    description: str
    api_schema: ApiSchema
    parameters: List[EnhancedToolParameter] = Field(default_factory=list)
    auth_config: Optional[Dict[str, Any]] = None
    timeout: Optional[int] = 30
    retry_config: Optional[Dict[str, Any]] = None


class PythonToolConfig(BaseModel):
    type: str = "python"
    name: str
    description: str
    functionPath: str
    parameters: List[EnhancedToolParameter] = Field(default_factory=list)
    imports: List[str] = Field(default_factory=list)
    requirements: List[str] = Field(default_factory=list)


class DSPyToolConfig(BaseModel):
    type: str = "dspy"
    name: str
    description: str
    module_type: DSPyModuleType
    signature: str
    parameters: List[EnhancedToolParameter] = Field(default_factory=list)
    examples: List[Dict[str, Any]] = Field(default_factory=list)


class ToolConfig(RootModel[Union[WebhookToolConfig, PythonToolConfig, DSPyToolConfig]]):
    pass


class ExecutionMetrics(BaseModel):
    node_id: str
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    duration_ms: Optional[int] = None
    status: Literal["pending", "running", "completed", "failed"] = "pending"
    error_message: Optional[str] = None
    input_data: Optional[Dict[str, Any]] = None
    output_data: Optional[Dict[str, Any]] = None
    memory_usage: Optional[int] = None
    token_usage: Optional[Dict[str, int]] = None


class AgentExecutionState(BaseModel):
    execution_id: str
    agent_id: str
    current_node_id: Optional[str] = None
    execution_path: List[str] = Field(default_factory=list)
    node_metrics: List[ExecutionMetrics] = Field(default_factory=list)
    global_state: Dict[str, Any] = Field(default_factory=dict)
    created_at: Optional[str] = None
    updated_at: Optional[str] = None


class SpeechConfig(BaseModel):
    stt_vad_threshold: Optional[float] = None
    tts_voice_id: Optional[str] = None
    tts_speaking_rate: Optional[float] = None


class ConversationSettings(BaseModel):
    silence_timeout_seconds: Optional[int] = None
    reprompt_messages: Optional[List[str]] = None
    max_reprompts: Optional[int] = None
    no_input_fallback_message: Optional[str] = None
    no_match_fallback_message: Optional[str] = None


class AgentType(str, Enum):
    SINGLE_NODE = "single_node"
    MULTI_NODE = "multi_node"
    HYBRID = "hybrid"


class AgentCategory(str, Enum):
    CUSTOMER_SERVICE = "customer_service"
    SALES = "sales"
    SUPPORT = "support"
    ANALYSIS = "analysis"
    CREATIVE = "creative"
    RESEARCH = "research"
    AUTOMATION = "automation"
    CUSTOM = "custom"


class GlobalMemoryConfig(BaseModel):
    enabled: bool = True
    types: List[MemoryType] = Field(default_factory=lambda: [MemoryType.SHORT_TERM])
    max_total_entries: Optional[int] = 1000
    cleanup_interval_hours: Optional[int] = 24


class Workflow(BaseModel):
    name: str
    description: Optional[str] = None
    active: bool = True
    id: str
    nodes: List[NodeConfig]
    connections: Connections
    start_node_id: str
    first_message: Optional[str] = None
    speech_config: Optional[SpeechConfig] = None
    conversation_settings: Optional[ConversationSettings] = None
    tools: Optional[List[ToolConfig]] = None

    # Enhanced workflow properties
    agent_type: AgentType = AgentType.SINGLE_NODE
    category: Optional[AgentCategory] = None
    tags: List[str] = Field(default_factory=list)
    version: str = "1.0.0"
    global_memory_config: Optional[GlobalMemoryConfig] = None

    # Execution settings
    max_execution_time_seconds: Optional[int] = 300
    max_iterations: Optional[int] = 100
    error_handling_strategy: Literal["stop", "continue", "retry"] = "stop"

    # UI layout settings
    canvas_settings: Optional[Dict[str, Any]] = None


class AgentTemplate(BaseModel):
    id: str
    name: str
    description: str
    category: AgentCategory
    tags: List[str] = Field(default_factory=list)
    workflow: Workflow
    preview_image: Optional[str] = None
    author: Optional[str] = None
    version: str = "1.0.0"
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    usage_count: int = 0
    rating: Optional[float] = None


class AgentConfig(BaseModel):
    workflow: Workflow
    template_id: Optional[str] = None
    created_from_template: bool = False