from typing import List, Optional, Callable, Any, Awaitable, Async<PERSON>enerator, Dict
from pydantic import BaseModel, <PERSON>
from sqlalchemy.ext.asyncio import AsyncSession
from orchestrator.schemas import SpeechConfig, MemoryConfig, ExecutionMetrics, AgentExecutionState
import uuid
from datetime import datetime


class AgentMemoryEntry(BaseModel):
    """Represents a memory entry for an agent."""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    agent_id: str
    memory_type: str
    content: str
    metadata: Dict[str, Any] = Field(default_factory=dict)
    embedding: Optional[List[float]] = None
    relevance_score: Optional[float] = None
    created_at: str = Field(default_factory=lambda: datetime.utcnow().isoformat())
    expires_at: Optional[str] = None


class NodeExecutionContext(BaseModel):
    """Context for a single node execution."""
    node_id: str
    execution_id: str
    input_data: Dict[str, Any] = Field(default_factory=dict)
    output_data: Dict[str, Any] = Field(default_factory=dict)
    metrics: Optional[ExecutionMetrics] = None
    memory_entries: List[AgentMemoryEntry] = Field(default_factory=list)
    tool_calls: List[Dict[str, Any]] = Field(default_factory=list)


class AgentState(BaseModel):
    """
    Enhanced agent state with comprehensive tracking and memory management.

    Represents the state of the conversation agent with support for
    multi-node workflows, memory management, and execution tracking.
    """

    # Core conversation state
    call_sid: Optional[str] = None
    customer_id: Optional[int] = None
    messages: List[dict] = []
    intent: Optional[str] = None
    rag_results: Optional[List[str]] = None
    response: Optional[str] = None
    call_history_id: Optional[int] = None
    user_message: Optional[str] = None
    system_prompt: Optional[str] = None
    speech_config: Optional[SpeechConfig] = None
    error: Optional[str] = None

    # Enhanced state management
    execution_state: Optional[AgentExecutionState] = None
    current_node_context: Optional[NodeExecutionContext] = None
    global_memory: List[AgentMemoryEntry] = Field(default_factory=list)
    node_outputs: Dict[str, Any] = Field(default_factory=dict)

    # Workflow control
    workflow_variables: Dict[str, Any] = Field(default_factory=dict)
    conditional_results: Dict[str, Any] = Field(default_factory=dict)
    loop_counters: Dict[str, int] = Field(default_factory=dict)

    # Performance tracking
    total_tokens_used: int = 0
    total_execution_time_ms: int = 0
    node_execution_count: int = 0

    # Fields for runtime objects that shouldn't be validated by Pydantic
    db_session: Optional[AsyncSession] = Field(None, exclude=True)
    input_handler: Optional[Callable[[Any, SpeechConfig], Awaitable[str | None]]] = (
        Field(None, exclude=True)
    )
    output_handler: Optional[Callable[[AsyncGenerator[str, None]], Awaitable[str]]] = (
        Field(None, exclude=True)
    )
    websocket: Optional[Any] = Field(None, exclude=True)

    class Config:
        arbitrary_types_allowed = True
