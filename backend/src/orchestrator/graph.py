import logging
import importlib
import inspect
import uuid
from datetime import datetime
from typing import Dict, Any, Optional
from langgraph.graph import StateGraph, END
from core.config import get_settings
from orchestrator.services.llm_integrations.llm_factory import get_llm_service
from orchestrator.services.llm_integrations.embedding_factory import (
    get_embedding_service,
)
from orchestrator.models import AgentState, NodeExecutionContext, AgentMemoryEntry
from orchestrator.schemas import (
    AgentConfig, ConditionalConnection, MainConnection, NodeType,
    ExecutionMetrics, AgentExecutionState
)
from orchestrator.services.memory.mem0_service import EnhancedMemoryService
from orchestrator.dspy.modules import DSPyModuleFactory
from langchain_core.messages import HumanMessage, SystemMessage
import dspy

logger = logging.getLogger(__name__)


def _import_function_from_string(full_path: str):
    module_name, func_name = full_path.rsplit(".", 1)
    module = importlib.import_module(module_name)
    return getattr(module, func_name)


# Initialize settings and LLM service
settings = get_settings()
llm_service = get_llm_service(settings)
embedding_service = get_embedding_service(settings)


def should_book_appointment(state: AgentState):
    return state.intent == "book_appointment"


def should_continue(state: AgentState):
    logger.info(f"should_continue")
    logger.info(f"check: user_message is '{state.user_message}'")
    return state.user_message is not None


def should_continue_on_error(state: AgentState):
    """
    Determines whether the graph should continue or stop due to an error.
    """
    if state.error:
        return False
    return True


class EnhancedAgentGraph:
    """Enhanced agent graph with support for new node types and execution tracking."""

    def __init__(self, db_session_factory, agent_config: AgentConfig):
        self.db_session_factory = db_session_factory
        self.workflow = StateGraph(AgentState)
        self.agent_workflow_config = agent_config.workflow
        self.memory_services: Dict[str, EnhancedMemoryService] = {}
        self.execution_metrics: Dict[str, ExecutionMetrics] = {}
        self._build_graph()
        self.app = self.workflow.compile()

    def _get_memory_service(self, agent_id: str, user_id: str, memory_config=None) -> EnhancedMemoryService:
        """Get or create memory service for agent."""
        key = f"{agent_id}_{user_id}"
        if key not in self.memory_services:
            self.memory_services[key] = EnhancedMemoryService(
                agent_id=agent_id,
                user_id=user_id,
                memory_config=memory_config
            )
        return self.memory_services[key]

    def _build_graph(self):
        # Add the nodes dynamically
        for node_config in self.agent_workflow_config.nodes:
            node_id = node_config.id
            node_function_path = node_config.functionPath
            node_func = _import_function_from_string(node_function_path)

            # Introspect the function signature to determine its parameters
            sig = inspect.signature(node_func)
            func_params = set(sig.parameters.keys())

            dependencies = node_config.dependencies
            node_kwargs = {}
            if dependencies:
                if "llm_service" in dependencies and "llm_service" in func_params:
                    node_kwargs["llm_service"] = llm_service
                if (
                    "embedding_service" in dependencies
                    and "embedding_service" in func_params
                ):
                    node_kwargs["embedding_service"] = embedding_service
                if "settings" in dependencies and "settings" in func_params:
                    node_kwargs["settings"] = settings

            has_db_dep = dependencies and "db" in dependencies and "db" in func_params

            async def node_wrapper(
                state_arg: AgentState,
                node_func=node_func,
                has_db_dep=has_db_dep,
                node_kwargs=node_kwargs,
            ):
                logger.info(
                    f"Entering node '{node_func.__name__}'",
                    extra={"call_history_id": state_arg.call_history_id},
                )
                if has_db_dep:
                    async with self.db_session_factory() as db_session:
                        return await node_func(state_arg, db=db_session, **node_kwargs)
                else:
                    return await node_func(state_arg, **node_kwargs)

            self.workflow.add_node(node_id, node_wrapper)

        # Set the entry point
        self.workflow.set_entry_point(self.agent_workflow_config.start_node_id)

    def _track_node_execution(self, node_id: str, start_time: datetime, success: bool, output_data: Dict[str, Any]):
        """Track node execution metrics."""
        end_time = datetime.utcnow()
        duration_ms = int((end_time - start_time).total_seconds() * 1000)

        metrics = ExecutionMetrics(
            node_id=node_id,
            start_time=start_time.isoformat(),
            end_time=end_time.isoformat(),
            duration_ms=duration_ms,
            status="completed" if success else "failed",
            output_data=output_data if success else None,
            error_message=output_data.get("error") if not success else None
        )

        self.execution_metrics[node_id] = metrics
        logger.info(f"Node {node_id} executed in {duration_ms}ms, success: {success}")

    def _create_enhanced_node_wrapper(self, node_config):
        """Create enhanced node wrapper based on node type."""
        if node_config.type == NodeType.LLM:
            return self._create_llm_node_wrapper(node_config)
        elif node_config.type == NodeType.DSPY_MODULE:
            return self._create_dspy_node_wrapper(node_config)
        elif node_config.type == NodeType.MEMORY:
            return self._create_memory_node_wrapper(node_config)
        else:
            # Fallback to original implementation
            return self._create_legacy_node_wrapper(node_config)

    def _create_llm_node_wrapper(self, node_config):
        """Create wrapper for LLM nodes."""
        async def llm_node_wrapper(state: AgentState):
            start_time = datetime.utcnow()

            try:
                logger.info(f"Executing LLM node: {node_config.id}")

                # Get LLM service
                settings = get_settings()
                llm_service = get_llm_service(settings)

                # Prepare system prompt
                system_prompt = node_config.system_prompt or state.system_prompt or "You are a helpful AI assistant."

                # Get user message
                user_message = state.user_message or ""
                if state.messages:
                    user_message = state.messages[-1].get("content", "")

                # Add memory context if available
                memory_context = ""
                if node_config.memory_config:
                    memory_service = self._get_memory_service(
                        self.agent_workflow_config.id,
                        str(state.customer_id or "default"),
                        node_config.memory_config
                    )
                    relevant_memories = memory_service.search_memory(user_message)
                    if relevant_memories:
                        memory_context = "\n".join([m.content for m in relevant_memories[:3]])
                        system_prompt += f"\n\nRelevant context:\n{memory_context}"

                # Generate response
                from langchain_core.messages import SystemMessage, HumanMessage
                messages = [
                    SystemMessage(content=system_prompt),
                    HumanMessage(content=user_message)
                ]

                response = await llm_service.get_chat_completion(messages)

                # Store in memory if configured
                if node_config.memory_config:
                    memory_service = self._get_memory_service(
                        self.agent_workflow_config.id,
                        str(state.customer_id or "default"),
                        node_config.memory_config
                    )
                    memory_service.add_memory(
                        f"User: {user_message}\nAssistant: {response}",
                        node_config.memory_config.type
                    )

                # Update state
                updated_state = {
                    "response": response,
                    "user_message": user_message
                }

                # Track execution metrics
                self._track_node_execution(node_config.id, start_time, True, updated_state)

                return updated_state

            except Exception as e:
                logger.error(f"Error in LLM node {node_config.id}: {e}")
                self._track_node_execution(node_config.id, start_time, False, {"error": str(e)})
                return {"error": str(e)}

        return llm_node_wrapper

    def _create_dspy_node_wrapper(self, node_config):
        """Create wrapper for DSPy module nodes."""
        async def dspy_node_wrapper(state: AgentState):
            start_time = datetime.utcnow()

            try:
                logger.info(f"Executing DSPy node: {node_config.id}")

                if not node_config.dspy_config:
                    raise ValueError("DSPy configuration missing")

                # Configure DSPy with current LLM
                settings = get_settings()
                llm_service = get_llm_service(settings)
                dspy.settings.configure(lm=llm_service.llm)

                # Create DSPy module
                module = DSPyModuleFactory.create_module(
                    node_config.dspy_config.module_type,
                    {
                        "signature": node_config.dspy_config.signature,
                        "examples": node_config.dspy_config.examples
                    }
                )

                # Prepare input data
                input_data = {
                    "user_message": state.user_message or "",
                    "context": state.node_outputs.get("context", ""),
                    **node_config.parameters
                }

                # Execute DSPy module
                result = module(**input_data)

                # Update state
                updated_state = {
                    "dspy_result": result,
                    f"{node_config.id}_output": result
                }

                # Track execution metrics
                self._track_node_execution(node_config.id, start_time, True, updated_state)

                return updated_state

            except Exception as e:
                logger.error(f"Error in DSPy node {node_config.id}: {e}")
                self._track_node_execution(node_config.id, start_time, False, {"error": str(e)})
                return {"error": str(e)}

        return dspy_node_wrapper

    def _create_memory_node_wrapper(self, node_config):
        """Create wrapper for memory nodes."""
        async def memory_node_wrapper(state: AgentState):
            start_time = datetime.utcnow()

            try:
                logger.info(f"Executing memory node: {node_config.id}")

                if not node_config.memory_config:
                    raise ValueError("Memory configuration missing")

                # Get memory service
                memory_service = self._get_memory_service(
                    self.agent_workflow_config.id,
                    str(state.customer_id or "default"),
                    node_config.memory_config
                )

                # Determine operation
                operation = node_config.parameters.get("operation", "search")

                if operation == "add":
                    content = state.user_message or node_config.parameters.get("content", "")
                    memory_entry = memory_service.add_memory(content, node_config.memory_config.type)
                    updated_state = {"memory_entry": memory_entry.dict()}

                elif operation == "search":
                    query = state.user_message or node_config.parameters.get("query", "")
                    results = memory_service.search_memory(query, [node_config.memory_config.type])
                    updated_state = {"memory_results": [r.dict() for r in results]}

                else:
                    raise ValueError(f"Unknown memory operation: {operation}")

                # Track execution metrics
                self._track_node_execution(node_config.id, start_time, True, updated_state)

                return updated_state

            except Exception as e:
                logger.error(f"Error in memory node {node_config.id}: {e}")
                self._track_node_execution(node_config.id, start_time, False, {"error": str(e)})
                return {"error": str(e)}

        return memory_node_wrapper

    def _create_legacy_node_wrapper(self, node_config):
        """Create wrapper for legacy Python function nodes."""
        node_function_path = node_config.functionPath
        if not node_function_path:
            raise ValueError(f"Function path missing for node {node_config.id}")

        node_func = _import_function_from_string(node_function_path)

        # Introspect the function signature to determine its parameters
        sig = inspect.signature(node_func)
        func_params = set(sig.parameters.keys())

        dependencies = node_config.dependencies
        node_kwargs = {}
        if dependencies:
            settings = get_settings()
            llm_service = get_llm_service(settings)
            embedding_service = get_embedding_service()

            if "llm_service" in dependencies and "llm_service" in func_params:
                node_kwargs["llm_service"] = llm_service
            if "embedding_service" in dependencies and "embedding_service" in func_params:
                node_kwargs["embedding_service"] = embedding_service
            if "settings" in dependencies and "settings" in func_params:
                node_kwargs["settings"] = settings

        has_db_dep = dependencies and "db" in dependencies and "db" in func_params

        async def legacy_node_wrapper(state_arg: AgentState):
            start_time = datetime.utcnow()

            try:
                logger.info(f"Executing legacy node: {node_config.id}")

                if has_db_dep:
                    async with self.db_session_factory() as db_session:
                        result = await node_func(state_arg, db=db_session, **node_kwargs)
                else:
                    result = await node_func(state_arg, **node_kwargs)

                self._track_node_execution(node_config.id, start_time, True, result or {})
                return result

            except Exception as e:
                logger.error(f"Error in legacy node {node_config.id}: {e}")
                self._track_node_execution(node_config.id, start_time, False, {"error": str(e)})
                return {"error": str(e)}

        return legacy_node_wrapper

        # Add the edges dynamically
        for (
            source_node_id,
            connections_obj,
        ) in self.agent_workflow_config.connections.root.items():
            if isinstance(connections_obj, dict) and "conditional" in connections_obj:
                conditional_config = connections_obj["conditional"]
                condition_type = conditional_config.type
                paths = conditional_config.paths

                condition_func = None
                if condition_type == "pythonFunction":
                    condition_function_path = conditional_config.conditionFunctionPath
                    condition_func = _import_function_from_string(
                        condition_function_path
                    )
                elif condition_type == "aiBased":
                    ai_prompt = conditional_config.prompt

                    async def ai_condition_func(state: AgentState):
                        messages = [
                            SystemMessage(
                                content=self.agent_workflow_config.system_prompt
                            ),
                            HumanMessage(content=ai_prompt),
                        ] + state.messages
                        response = await llm_service.get_chat_completion(messages)
                        return response.strip().lower()

                    condition_func = ai_condition_func

                converted_paths = {}
                for key, targets in paths.root.items():
                    target_node_str = targets[0].node
                    actual_target = END if target_node_str == "END" else target_node_str

                    if key == "True":
                        converted_paths[True] = actual_target
                    elif key == "False":
                        converted_paths[False] = actual_target
                    elif key == "None":
                        converted_paths[None] = actual_target
                    else:
                        converted_paths[key] = actual_target

                self.workflow.add_conditional_edges(
                    source_node_id,
                    condition_func,
                    converted_paths,
                )
            elif isinstance(connections_obj, MainConnection):
                for target_info in connections_obj.main:
                    target_node_id = target_info.node
                    if target_node_id == "END":
                        self.workflow.add_edge(source_node_id, END)
                    else:
                        self.workflow.add_edge(source_node_id, target_node_id)


# Backward compatibility alias
AgentGraph = EnhancedAgentGraph

def get_agent_graph(db_session_factory, agent_config: AgentConfig):
    return EnhancedAgentGraph(db_session_factory, agent_config)
