{"workflow": {"name": "Customer Support Agent Workflow", "description": "Orchestrates the customer support agent's conversational flow.", "active": true, "id": "agent-workflow-1", "nodes": [{"id": "get_user_input_node", "name": "Get User Input", "type": "pythonFunction", "functionPath": "orchestrator.services.websocket.io.get_user_input", "dependencies": ["db"]}, {"id": "llm_conversation_node", "name": "LLM Conversation", "type": "pythonFunction", "functionPath": "orchestrator.nodes.llm.generate_response", "dependencies": ["llm_service", "db"]}], "connections": {"get_user_input_node": {"conditional": {"type": "pythonFunction", "conditionFunctionPath": "orchestrator.graph.should_continue", "paths": {"True": [{"node": "llm_conversation_node", "type": "main", "index": 0}], "False": [{"node": "END", "type": "main", "index": 0}]}}}, "llm_conversation_node": {"conditional": {"type": "pythonFunction", "conditionFunctionPath": "orchestrator.graph.should_continue_on_error", "paths": {"True": [{"node": "get_user_input_node", "type": "main", "index": 0}], "False": [{"node": "END", "type": "main", "index": 0}]}}}}, "start_node_id": "get_user_input_node", "first_message": "Hello! How can I assist you today?", "speech_config": {"stt_vad_threshold": 0.7, "tts_voice_id": "default", "tts_speaking_rate": 1.0}, "conversation_settings": {"silence_timeout_seconds": 10, "reprompt_messages": ["Are you still there?", "Did you have any other questions?"], "max_reprompts": 2, "no_input_fallback_message": "I didn't hear anything. Can you please repeat that?", "no_match_fallback_message": "I'm sorry, I didn't understand that. Could you rephrase your request?"}, "tools": [{"type": "webhook", "name": "book_appointment", "description": "book a 30-minute appointment", "api_schema": {"url": "https://api.cal.com/v2/bookings", "method": "POST", "request_body_schema": {"id": "body", "type": "object", "description": "Timeslot to book", "properties": [{"id": "start", "type": "string", "value_type": "llm_prompt", "description": "The start time of the booking in ISO 8601 format in UTC timezone.", "required": true}, {"id": "eventTypeSlug", "type": "string", "value_type": "constant", "constant_value": "30min", "required": true}]}, "request_headers": [{"type": "value", "name": "Content-Type", "value": "application/json"}]}}]}}