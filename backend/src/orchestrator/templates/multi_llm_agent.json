{"workflow": {"name": "Multi-LLM Analyst Agent", "description": "A complex agent that uses a chain of LLM nodes to analyze, strategize, and respond.", "active": true, "id": "multi-llm-agent-1", "nodes": [{"id": "get_user_input_node", "name": "Get User Input", "type": "pythonFunction", "functionPath": "orchestrator.services.websocket.io.get_user_input", "dependencies": ["db"]}, {"id": "analyst_llm_node", "name": "Analyst LLM", "type": "pythonFunction", "functionPath": "orchestrator.nodes.llm.generate_response", "dependencies": ["llm_service", "db"], "parameters": {"system_prompt": "You are an expert analyst. Your job is to take the user's request and break it down into its core components and identify key questions."}}, {"id": "strategist_llm_node", "name": "Strategist LLM", "type": "pythonFunction", "functionPath": "orchestrator.nodes.llm.generate_response", "dependencies": ["llm_service", "db"], "parameters": {"system_prompt": "You are a master strategist. Take the analysis from the previous step and formulate a high-level plan to address the user's request."}}, {"id": "responder_llm_node", "name": "Responder LLM", "type": "pythonFunction", "functionPath": "orchestrator.nodes.llm.generate_response", "dependencies": ["llm_service", "db"], "parameters": {"system_prompt": "You are a friendly and helpful customer-facing agent. Take the plan from the strategist and use it to formulate a clear and concise response to the user."}}], "connections": {"get_user_input_node": {"conditional": {"type": "pythonFunction", "conditionFunctionPath": "orchestrator.graph.should_continue", "paths": {"True": [{"node": "analyst_llm_node", "type": "main", "index": 0}], "False": [{"node": "END", "type": "main", "index": 0}]}}}, "analyst_llm_node": {"main": [{"node": "strategist_llm_node", "type": "main", "index": 0}]}, "strategist_llm_node": {"main": [{"node": "responder_llm_node", "type": "main", "index": 0}]}, "responder_llm_node": {"main": [{"node": "get_user_input_node", "type": "main", "index": 0}]}}, "start_node_id": "get_user_input_node", "first_message": "Hello! I am a multi-step assistant. How can I help you today?", "conversation_settings": {"silence_timeout_seconds": 30}}}