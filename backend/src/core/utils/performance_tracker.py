import time
import asyncio
from functools import wraps
from typing import Any, Callable
from modules.performance.crud import create_performance_metric
from modules.performance.schemas import PerformanceMetricCreate
from core.db.database import AsyncSessionLocal
from core.utils.request_context import request_id_var


def track_latency(service: str, action: str) -> Callable:
    """
    A decorator to measure and record the latency of function calls into the database.
    Supports both synchronous and asynchronous functions.
    """

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args: Any, **kwargs: Any) -> Any:
            start_time = time.time()
            result = await func(*args, **kwargs)
            end_time = time.time()
            latency = end_time - start_time

            if service != "database":
                async with AsyncSessionLocal() as db:
                    try:
                        metric = PerformanceMetricCreate(
                            service=service,
                            action=action,
                            latency=latency,
                            request_id=request_id_var.get(),
                        )
                        await create_performance_metric(db, metric)
                    except Exception:
                        # Avoid crashing the application if metric recording fails
                        pass

            return result

        @wraps(func)
        def sync_wrapper(*args: Any, **kwargs: Any) -> Any:
            # This is a simplified sync version. For a truly robust solution,
            # you'd run the async metric creation in a separate thread
            # or use an async-to-sync adapter.
            # start_time = time.time()
            result = func(*args, **kwargs)
            # end_time = time.time()
            # Removed unused 'latency' variable
            # latency = end_time - start_time

            if service != "database":
                # This part is tricky in a sync context with an async db call.
                # For now, we'll skip logging fromsync functions to avoid blocking.
                # A more advanced setup might use a background task.
                pass

            return result

        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator
