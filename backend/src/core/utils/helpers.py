from sqlalchemy.orm import DeclarativeBase
from sqlalchemy.orm.query import Query
from sqlalchemy.inspection import inspect
from typing import Any, Dict, List, Optional, TypeVar, Union

_T = TypeVar("_T", bound=DeclarativeBase)


def model_to_dict(
    obj: Union[_T, Any], follow_relationships: bool = False
) -> Dict[str, Any]:
    """
    Converts a SQLAlchemy model instance to a dictionary.

    Args:
        obj: The SQLAlchemy model instance to convert.
        follow_relationships: If True, also converts related objects (one-to-one, one-to-many)
                              into dictionaries. Be cautious with this to avoid recursion.

    Returns:
        A dictionary representation of the model instance.
    """
    if not isinstance(obj, DeclarativeBase):
        raise TypeError(
            f"Expected a SQLAlchemy DeclarativeBase instance, but got {type(obj)}"
        )

    data = {}
    mapper = inspect(obj.__class__)

    # Handle columns
    for column in mapper.columns:
        data[column.key] = getattr(obj, column.key)

    # Handle relationships if requested
    if follow_relationships:
        for relationship in mapper.relationships:
            related_obj = getattr(obj, relationship.key)
            if related_obj is not None:
                if relationship.uselist:  # One-to-many or many-to-many
                    data[relationship.key] = [
                        model_to_dict(item, False) for item in related_obj
                    ]  # Avoid deep recursion
                else:  # One-to-one or many-to-one
                    data[relationship.key] = model_to_dict(
                        related_obj, False
                    )  # Avoid deep recursion
    return data


def query_to_list(
    query: Query, follow_relationships: bool = False
) -> List[Dict[str, Any]]:
    """
    Converts a SQLAlchemy query result to a list of dictionaries.

    Args:
        query: The SQLAlchemy query object.
        follow_relationships: If True, also converts related objects into dictionaries.

    Returns:
        A list of dictionaries, where each dictionary represents a row from the query result.
    """
    return [model_to_dict(obj, follow_relationships) for obj in query.all()]


def get_first_or_none(
    query: Query, follow_relationships: bool = False
) -> Optional[Dict[str, Any]]:
    """
    Executes a query and returns the first result as a dictionary, or None if no result.

    Args:
        query: The SQLAlchemy query object.
        follow_relationships: If True, also converts related objects into dictionaries.

    Returns:
        A dictionary representation of the first result, or None.
    """
    obj = query.first()
    if obj:
        return model_to_dict(obj, follow_relationships)
    return None
