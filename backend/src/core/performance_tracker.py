import time
from functools import wraps


def track_latency(module_name, function_name):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.perf_counter()
            result = await func(*args, **kwargs)
            end_time = time.perf_counter()
            latency = (end_time - start_time) * 1000  # in milliseconds
            print(
                f"[{module_name}.{function_name}] Function {func.__name__} took {latency:.2f} ms"
            )
            return result

        return wrapper

    return decorator
