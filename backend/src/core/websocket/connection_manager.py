import logging
import asyncio
from typing import Dict

from fastapi import WebSocket


logger = logging.getLogger(__name__)


class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[int, WebSocket] = {}
        self.connection_events: Dict[int, asyncio.Event] = {}

    def get_connection_event(self, call_history_id: int) -> asyncio.Event:
        """Get or create an event for a call history ID."""
        if call_history_id not in self.connection_events:
            self.connection_events[call_history_id] = asyncio.Event()
        return self.connection_events[call_history_id]

    async def wait_for_connection(self, call_history_id: int, timeout: float = 5.0):
        """Wait for the frontend to connect."""
        event = self.get_connection_event(call_history_id)
        try:
            await asyncio.wait_for(event.wait(), timeout=timeout)
            logger.info(f"Frontend connection confirmed for call_history_id: {call_history_id}")
        except asyncio.TimeoutError:
            logger.warning(f"Timed out waiting for frontend connection for call_history_id: {call_history_id}")

    async def connect(self, websocket: WebSocket, call_history_id: int):
        await websocket.accept()
        self.active_connections[call_history_id] = websocket
        # Signal that the connection is established
        event = self.get_connection_event(call_history_id)
        event.set()
        logger.info(
            f"WebSocket connected: {call_history_id}. Active connections: {len(self.active_connections)}"
        )

    def disconnect(self, call_history_id: int):
        if call_history_id in self.active_connections:
            del self.active_connections[call_history_id]
            logger.info(
                f"WebSocket disconnected: {call_history_id}. Active connections: {len(self.active_connections)}"
            )
        if call_history_id in self.connection_events:
            del self.connection_events[call_history_id]

    async def send_personal_message(self, message: str, call_history_id: int):
        websocket = self.active_connections.get(call_history_id)
        if websocket:
            try:
                await websocket.send_text(message)
            except Exception as e:
                logger.error(f"Error sending message to {call_history_id}: {e}")
                self.disconnect(call_history_id)  # Disconnect if sending fails
        else:
            logger.warning(
                f"WebSocket for {call_history_id} not found. Message not sent."
            )

    async def broadcast(self, message: str):
        for connection in self.active_connections.values():
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error(f"Error broadcasting message: {e}")


connection_manager = ConnectionManager()
