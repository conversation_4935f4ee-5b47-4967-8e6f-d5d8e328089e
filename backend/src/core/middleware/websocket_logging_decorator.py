import uuid
import functools
import logging
from fastapi import WebSocket
from core.utils.request_context import request_id_var

logger = logging.getLogger(__name__)

class WebSocketLogger:
    def __init__(self, websocket: WebSocket):
        self._websocket = websocket
        self.received_messages = []

    async def receive_text(self, *args, **kwargs):
        data = await self._websocket.receive_text(*args, **kwargs)
        self.received_messages.append(data)
        return data

    async def receive_json(self, *args, **kwargs):
        data = await self._websocket.receive_json(*args, **kwargs)
        self.received_messages.append(data)
        return data

    def __getattr__(self, name):
        """Delegate all other attribute access to the original websocket object."""
        return getattr(self._websocket, name)

def websocket_logging_decorator(func):
    @functools.wraps(func)
    async def wrapper(websocket: WebSocket, *args, **kwargs):
        request_id = websocket.headers.get("X-Request-ID")
        if not request_id:
            request_id = str(uuid.uuid4())
        
        token = request_id_var.set(request_id)
        
        logging_websocket = WebSocketLogger(websocket)
        
        logger.info("WebSocket connection started.")
        try:
            await func(logging_websocket, *args, **kwargs)
        finally:
            logger.info(
                "WebSocket connection ended.",
                extra={"received_data": logging_websocket.received_messages},
            )
            request_id_var.reset(token)
            
    return wrapper
