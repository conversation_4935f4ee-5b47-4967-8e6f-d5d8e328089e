import logging
import time
from datetime import datetime
from typing import Any, AsyncGenerator, <PERSON>wai<PERSON>, Callable, List, Dict, Optional

from sqlalchemy.ext.asyncio import AsyncSession

from modules.call import crud as call_crud
from modules.customers import crud as customer_crud
from modules.call.schemas import Conversation<PERSON>og<PERSON><PERSON>
from orchestrator.services.orchestration_service import OrchestrationService
from core.utils.events import _log_and_send_event
from core.websocket.connection_manager import connection_manager
from orchestrator.schemas import AgentConfig

logger = logging.getLogger(__name__)


class ConversationManager:
    def __init__(self, db_session_factory: Callable[[], AsyncSession]):
        self.db_session_factory = db_session_factory
        self.agent_service = OrchestrationService(db_session_factory=db_session_factory)
        self.start_time = time.time()

    async def handle_conversation(
        self,
        call_history_id: int,
        input_handler: Callable[[Any, Any], Awaitable[str | None]],
        output_handler: Callable[[AsyncGenerator[str, None]], <PERSON>wai<PERSON>[str]],
        first_message_handler: Callable[[str], <PERSON>waitable[None]],
        get_init_message: Callable[[], Awaitable[dict]],
        websocket: Any,
        agent_config_data: Optional[Dict[str, Any]] = None,
    ):
        logger.info(f"Starting conversation for call history ID: {call_history_id}")

        # Wait for the frontend to establish its update WebSocket connection
        await connection_manager.wait_for_connection(call_history_id)

        agent_config = None
        if agent_config_data:
            try:
                agent_config = AgentConfig.parse_obj(agent_config_data)
                logger.info(f"Using dynamic agent config for call: {call_history_id}")
            except Exception as e:
                logger.error(f"Invalid agent_config provided: {e}", exc_info=True)
                # Fallback to default agent if config is invalid
                agent_config = None

        async with self.db_session_factory() as db:
            try:
                init_message = await get_init_message()
                logger.info(f"Received init message: {init_message}")

                call_history = await call_crud.get_call_history(db, call_history_id)
                if not call_history:
                    raise Exception(f"Call history {call_history_id} not found.")

                call_context = await customer_crud.get_call_context(
                    db, call_history.customer_id, init_message.get("webhook_data")
                )
                if not call_context:
                    raise Exception(
                        f"Could not generate context for customer {call_history.customer_id}"
                    )

                system_prompt = (
                    call_history.system_prompt or "You are a helpful assistant."
                )
                messages: List[Dict[str, Any]] = [
                    {"role": "system", "content": system_prompt}
                ]

                first_message = (
                    call_history.first_message
                    or "Hello, {customer_name}! How can I help you today?"
                ).format(
                    customer_name=call_context.get("customer_info", {}).get(
                        "name", "there"
                    ),
                )

                messages.append({"role": "assistant", "content": first_message})

                await _log_and_send_event(
                    db,
                    call_history_id,
                    ConversationLogEvent(
                        call_history_id=call_history_id,
                        event_type="agent_message",
                        timestamp=datetime.now(),
                        data={"content": first_message},
                        source="conversation_manager",
                    ),
                )

                async for final_state in self.agent_service.run_agent_async(
                    messages=messages,
                    db=db,
                    context=call_context,
                    call_history_id=call_history_id,
                    websocket=websocket,
                    input_handler=input_handler,
                    output_handler=output_handler,
                    agent_config=agent_config,
                ):
                    logger.info(
                        f"Graph execution finished with final state: {final_state}"
                    )

            except Exception as e:
                logger.error(f"Error in conversation: {e}", exc_info=True)
            finally:
                duration = int(time.time() - self.start_time)
                await call_crud.update_call_history(
                    db, call_history_id, call_duration=duration, status="completed"
                )
                await db.commit()
                logger.info(
                    f"Conversation ended for call history ID: {call_history_id}"
                )
