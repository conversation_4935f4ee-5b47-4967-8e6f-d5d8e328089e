from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker, AsyncSession
from sqlalchemy.orm import declarative_base
from core.config import get_settings
from typing import AsyncGenerator, <PERSON><PERSON>, Any


def get_db_components(database_url: str, echo: bool = False) -> Tuple[Any, Any]:
    """
    Returns a SQLAlchemy async engine and an async sessionmaker.
    """
    engine = create_async_engine(database_url, echo=echo)
    AsyncSessionLocal = async_sessionmaker(
        expire_on_commit=False,
        autoflush=False,
        autocommit=False,
        bind=engine,
        class_=AsyncSession,
    )
    return engine, AsyncSessionLocal


settings = get_settings()

# Create the SQLAlchemy async engine and session local for the application
engine, AsyncSessionLocal = get_db_components(settings.DATABASE_URL, echo=False)

# Base class for declarative models
Base = declarative_base()


# Dependency to get an async database session
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    db = AsyncSessionLocal()
    try:
        yield db
    finally:
        await db.close()
