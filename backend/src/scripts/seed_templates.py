# Usage:
# DATABASE_URL="postgresql+asyncpg://user:password@localhost:5432/homeservice" uv run python src/scripts/seed_templates.py (Run the seed_templates.py

import json
import asyncio
import os


from sqlalchemy.future import select

import sys
from pathlib import Path

sys.path.append(str(Path(__file__).resolve().parents[2]))

from core.db.database import get_db
from modules.agents.models import AgentTemplate
from modules.agents.schemas import AgentTemplateCreate


async def seed_or_update_templates():
    """
    Seeds the database with the agent template from the agent_config.json file.
    If a template with the same name exists, it will be updated.
    """
    print("Starting to seed or update agent templates...")
    db_generator = get_db()
    db = await anext(db_generator)
    try:
        # Construct the path to the agent_config.json file
        script_dir = os.path.dirname(__file__)
        config_path = os.path.join(
            script_dir, "..", "orchestrator", "agent_config.json"
        )

        print(f"Loading agent configuration from: {config_path}")
        with open(config_path, "r") as f:
            agent_config = json.load(f)

        workflow = agent_config.get("workflow")

        if workflow:
            template_name = workflow.get("name", "Default Agent Template")
            template_description = workflow.get(
                "description",
                "A comprehensive agent for handling customer interactions.",
            )

            # Check if a template with the correct name already exists
            result = await db.execute(
                select(AgentTemplate).filter(AgentTemplate.name == template_name)
            )
            existing_template = result.scalars().first()

            if existing_template:
                print(f"Template '{existing_template.name}' found. Updating it.")
                existing_template.description = template_description
                existing_template.workflow = workflow
                await db.commit()
                print(f"✅ Template '{template_name}' updated successfully.")
            else:
                print(
                    f"No existing template found. Creating new template '{template_name}'."
                )
                template = AgentTemplateCreate(
                    name=template_name,
                    description=template_description,
                    workflow=workflow,
                )
                db_template = AgentTemplate(**template.model_dump())
                db.add(db_template)
                await db.commit()
                print(f"✅ Template '{template_name}' seeded successfully.")
        else:
            print("⚠️ No workflow found in agent_config.json. Nothing to seed.")
    finally:
        await db.close()
        print("Database session closed.")


if __name__ == "__main__":
    print("Running seed script...")
    asyncio.run(seed_or_update_templates())
    print("Seed script finished.")
