import asyncio
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy import text
from configparser import ConfigParser
import os


async def enable_pgvector_extension():
    # Read database URL fromalembic.ini
    config = ConfigParser()
    config_path = os.path.join(
        os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "alembic.ini"
    )
    config.read(config_path)
    db_url = config.get("alembic", "sqlalchemy.url")

    engine = create_async_engine(db_url)

    async with engine.connect() as conn:
        print("Enabling pgvector extension...")
        await conn.execute(text("CREATE EXTENSION IF NOT EXISTS vector;"))
        await conn.commit()
        print("pgvector extension enabled successfully.")


if __name__ == "__main__":
    asyncio.run(enable_pgvector_extension())
