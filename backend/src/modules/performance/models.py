from sqlalchemy import (
    Column,
    Integer,
    String,
    DateTime,
    Float,
)
from sqlalchemy.sql import func
from core.db.database import Base


class PerformanceMetric(Base):
    __tablename__ = "performance_metrics"

    id = Column(Integer, primary_key=True, index=True)
    request_id = Column(String, nullable=True, index=True)
    chat_history_id = Column(String, nullable=True, index=True)
    service = Column(String, nullable=False, index=True)
    action = Column(String, nullable=False, index=True)
    latency = Column(Float, nullable=False)
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
