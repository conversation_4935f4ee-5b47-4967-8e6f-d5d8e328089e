from pydantic import BaseModel, ConfigDict
from datetime import datetime
from typing import Optional


class PerformanceMetricBase(BaseModel):
    service: str
    action: str
    latency: float
    request_id: Optional[str] = None


class PerformanceMetricCreate(PerformanceMetricBase):
    pass


class PerformanceMetric(PerformanceMetricBase):
    id: int
    timestamp: datetime

    model_config = ConfigDict(from_attributes=True)
