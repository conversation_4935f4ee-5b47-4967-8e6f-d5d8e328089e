from fastapi import APIRouter, Depends
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from . import crud
from . import schemas
from core.db.database import get_db
from datetime import datetime
from modules.auth.service import get_current_user
from modules.auth.schemas import UserPublic

router = APIRouter(prefix="/performance", tags=["performance"])


@router.get("/metrics", response_model=List[schemas.PerformanceMetric])
async def read_metrics(
    start_time: Optional[datetime] = None,
    db: AsyncSession = Depends(get_db),
):
    """
    Retrieve all collected performance metrics fromthe database.
    """
    metrics = await crud.get_performance_metrics(db, start_time=start_time)
    return metrics


@router.get("/live", response_model=List[schemas.PerformanceMetric])
async def get_live_performance(
    db: AsyncSession = Depends(get_db),
    current_user: UserPublic = Depends(get_current_user),
):
    return await crud.get_latest_performance_metrics(db)
