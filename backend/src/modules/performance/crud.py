from typing import Optional
from datetime import datetime

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from modules.performance.models import PerformanceMetric
from . import schemas


# Performance Metric CRUD operations
async def create_performance_metric(
    db: AsyncSession, metric: schemas.PerformanceMetricCreate
):
    db_metric = PerformanceMetric(**metric.model_dump())
    db.add(db_metric)
    await db.commit()
    await db.refresh(db_metric)
    return db_metric


async def get_performance_metrics(
    db: AsyncSession, start_time: Optional[datetime] = None
):
    query = select(PerformanceMetric)
    if start_time:
        query = query.filter(PerformanceMetric.timestamp >= start_time)
    result = await db.execute(query)
    return result.scalars().all()


async def get_latest_performance_metrics(db: AsyncSession, limit: int = 10):
    query = (
        select(PerformanceMetric)
        .order_by(PerformanceMetric.timestamp.desc())
        .limit(limit)
    )
    result = await db.execute(query)
    return result.scalars().all()
