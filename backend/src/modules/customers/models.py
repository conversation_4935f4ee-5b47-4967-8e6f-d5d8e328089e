from sqlalchemy import (
    Column,
    Integer,
    String,
    DateTime,
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from core.db.database import Base
from modules.documents.models import Document  # noqa: F401, needed for relationship


class Customer(Base):
    __tablename__ = "customers"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False, index=True)
    domain = Column(String, unique=True, nullable=False, index=True)
    phone_number = Column(String, nullable=True)
    address = Column(String, nullable=True)
    city = Column(String, nullable=True)
    state = Column(String, nullable=True)
    zip_code = Column(String, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    documents = relationship("Document", back_populates="customer")
    embeddings = relationship("Embedding", back_populates="customer")
    jobs = relationship("Job", back_populates="customer")
    phone_numbers = relationship("PhoneNumber", back_populates="customer")
    call_history = relationship("CallHistory", back_populates="customer")

    def __repr__(self):
        return f"<Customer(id={self.id}, name='{self.name}', domain='{self.domain}')>"
