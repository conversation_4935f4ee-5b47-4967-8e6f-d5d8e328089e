from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from . import models, schemas
from typing import Optional, Dict, Any


async def get_customer(db: AsyncSession, customer_id: int):
    return await db.get(models.Customer, customer_id)


async def get_customer_by_name(db: AsyncSession, name: str):
    result = await db.execute(
        select(models.Customer).filter(models.Customer.name == name)
    )
    return result.scalars().first()


async def get_customer_by_domain(db: AsyncSession, domain: str):
    result = await db.execute(
        select(models.Customer).filter(models.Customer.domain == domain)
    )
    return result.scalars().first()


async def get_customers(db: AsyncSession, skip: int = 0, limit: int = 100):
    result = await db.execute(select(models.Customer).offset(skip).limit(limit))
    return result.scalars().all()


async def create_customer(db: AsyncSession, customer: schemas.CustomerCreate):
    db_customer = models.Customer(**customer.model_dump())
    db.add(db_customer)
    await db.commit()
    await db.refresh(db_customer)
    return db_customer


async def update_customer(
    db: AsyncSession, customer_id: int, customer: schemas.CustomerUpdate
):
    db_customer = await db.get(models.Customer, customer_id)
    if db_customer:
        update_data = customer.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_customer, key, value)
        await db.commit()
        await db.refresh(db_customer)
    return db_customer


async def delete_customer(db: AsyncSession, customer_id: int):
    db_customer = await db.get(models.Customer, customer_id)
    if db_customer:
        await db.delete(db_customer)
        await db.commit()
        return True
    return False


async def get_call_context(
    db: AsyncSession, customer_id: int, webhook_data: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Gathers the full context for a call, including customer details and webhook data.
    """
    customer = await get_customer(db, customer_id)
    if not customer:
        return {}

    customer_info = {
        "id": customer.id,
        "name": customer.name,
        "phone_number": customer.phone_number,
        "address": customer.address,
        "city": customer.city,
        "state": customer.state,
        "zip_code": customer.zip_code,
    }

    return {
        "customer_info": customer_info,
        "call_context": webhook_data or {},
    }
