from pydantic import BaseModel, ConfigDict
from datetime import datetime
from typing import Optional


class CustomerBase(BaseModel):
    name: str
    phone_number: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    zip_code: Optional[str] = None
    domain: Optional[str] = None  # Keep domain for RAG


class CustomerCreate(CustomerBase):
    pass


class CustomerUpdate(CustomerBase):
    pass


class CustomerInDB(CustomerBase):
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)
