from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List

from fastapi_cache.decorator import cache

from fastapi_cache import FastAPICache

from core.db.database import get_db
from . import schemas
from . import crud

router = APIRouter(prefix="/customers", tags=["customers"])


@router.post(
    "/", response_model=schemas.CustomerInDB, status_code=status.HTTP_201_CREATED
)
async def create_customer(
    customer: schemas.CustomerCreate, db: AsyncSession = Depends(get_db)
):
    db_customer = await crud.get_customer_by_name(db, name=customer.name)
    if db_customer:
        raise HTTPException(
            status_code=400, detail="Customer with this name already exists"
        )
    new_customer = await crud.create_customer(db=db, customer=customer)
    await FastAPICache.clear(
        namespace="customers"
    )  # Invalidate cache for customer list
    return new_customer


from modules.auth.service import get_current_user
from modules.auth.schemas import UserPublic

# ... (other imports)


@router.get("/", response_model=List[schemas.CustomerInDB])
@cache(namespace="customers", expire=60)  # Cache for 60 seconds
async def read_customers(
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db),
    current_user: UserPublic = Depends(get_current_user),  # <-- Endpoint Protection
):
    customers = await crud.get_customers(db, skip=skip, limit=limit)
    return customers


@router.get("/{customer_id}", response_model=schemas.CustomerInDB)
@cache(namespace="customers", expire=60)  # Cache for 60 seconds
async def read_customer(customer_id: int, db: AsyncSession = Depends(get_db)):
    db_customer = await crud.get_customer(db, customer_id=customer_id)
    if db_customer is None:
        raise HTTPException(status_code=404, detail="Customer not found")
    return db_customer


@router.put("/{customer_id}", response_model=schemas.CustomerInDB)
async def update_customer(
    customer_id: int,
    customer: schemas.CustomerUpdate,
    db: AsyncSession = Depends(get_db),
):
    db_customer = await crud.update_customer(
        db, customer_id=customer_id, customer=customer
    )
    if db_customer is None:
        raise HTTPException(status_code=404, detail="Customer not found")
    await FastAPICache.clear(
        namespace="customers"
    )  # Invalidate cache for customer list and specific customer
    return db_customer


@router.delete("/{customer_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_customer(
    customer_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: UserPublic = Depends(get_current_user),
):
    success = await crud.delete_customer(db, customer_id=customer_id)
    if not success:
        raise HTTPException(status_code=404, detail="Customer not found")
    await FastAPICache.clear(
        namespace="customers"
    )  # Invalidate cache for customer list and specific customer
    return {"message": "Customer deleted successfully"}
