from fastapi import APIRouter, Depends, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List

from core.db.database import get_db
from . import schemas, service
from modules.auth.service import get_current_user
from modules.auth.schemas import UserPublic


router = APIRouter(prefix="/node", tags=["node_tester"])


@router.get("/prompts/", response_model=List[schemas.TestPrompt])
async def get_all_prompt_tests(db: AsyncSession = Depends(get_db)):
    return await service.get_all_test_prompts(db)


@router.get("/prompts/{prompt_test_id}", response_model=schemas.TestPrompt)
async def get_prompt_test(prompt_test_id: int, db: AsyncSession = Depends(get_db)):
    return await service.get_test_prompt(db, prompt_test_id)


@router.post(
    "/prompts/", response_model=schemas.TestPrompt, status_code=status.HTTP_201_CREATED
)
async def create_prompt_test(
    prompt_test: schemas.TestPromptCreate, db: AsyncSession = Depends(get_db)
):
    return await service.create_test_prompt(db, prompt_test)


@router.put("/prompts/{prompt_test_id}", response_model=schemas.TestPrompt)
async def update_prompt_test(
    prompt_test_id: int,
    prompt_test: schemas.TestPromptUpdate,
    db: AsyncSession = Depends(get_db),
):
    return await service.update_test_prompt(db, prompt_test_id, prompt_test)


@router.delete("/prompts/{prompt_test_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_prompt_test(
    prompt_test_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: UserPublic = Depends(get_current_user),
):
    return await service.delete_test_prompt(db, prompt_test_id)
