from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from fastapi import HTTPEx<PERSON>, status

from . import models, schemas


async def get_all_test_prompts(db: AsyncSession):
    result = await db.execute(select(models.TestPrompt))
    return result.scalars().all()


async def get_test_prompt(db: AsyncSession, test_prompt_id: int):
    result = await db.execute(
        select(models.TestPrompt).where(models.TestPrompt.id == test_prompt_id)
    )
    test_prompt = result.scalars().first()
    if not test_prompt:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Prompt test not found"
        )
    return test_prompt


async def create_test_prompt(db: AsyncSession, test_prompt: schemas.TestPromptCreate):
    db_test_prompt = models.TestPrompt(**test_prompt.model_dump())
    db.add(db_test_prompt)
    await db.commit()
    await db.refresh(db_test_prompt)
    return db_test_prompt


async def update_test_prompt(
    db: AsyncSession, test_prompt_id: int, test_prompt: schemas.TestPromptUpdate
):
    stmt = (
        update(models.TestPrompt)
        .where(models.TestPrompt.id == test_prompt_id)
        .values(**test_prompt.model_dump(exclude_unset=True))
        .returning(models.TestPrompt)
    )
    result = await db.execute(stmt)
    updated_test_prompt = result.scalars().first()
    if not updated_test_prompt:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Prompt test not found"
        )
    await db.commit()
    return updated_test_prompt


async def delete_test_prompt(db: AsyncSession, test_prompt_id: int):
    stmt = delete(models.TestPrompt).where(models.TestPrompt.id == test_prompt_id)
    result = await db.execute(stmt)
    if result.rowcount == 0:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Prompt test not found"
        )
    await db.commit()
    return {"message": "Prompt test deleted successfully"}
