from pydantic import BaseModel, ConfigDict
from typing import Optional
from datetime import datetime


class TestPromptBase(BaseModel):
    name: Optional[str] = None
    phone_number: Optional[str] = None
    system_prompt: Optional[str] = None
    first_message: Optional[str] = None


class TestPromptCreate(TestPromptBase):
    pass


class TestPromptUpdate(BaseModel):
    name: Optional[str] = None
    phone_number: Optional[str] = None
    system_prompt: Optional[str] = None
    first_message: Optional[str] = None


class TestPrompt(TestPromptBase):
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)
