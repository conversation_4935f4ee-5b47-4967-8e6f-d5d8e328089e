from sqlalchemy import Column, Integer, String, Text, DateTime
from sqlalchemy.sql import func
from core.db.database import Base


class TestPrompt(Base):
    __tablename__ = "test_prompts"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True, nullable=False)
    phone_number = Column(String, nullable=True)
    system_prompt = Column(Text, nullable=False)
    first_message = Column(Text, nullable=False)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now(), server_default=func.now())
