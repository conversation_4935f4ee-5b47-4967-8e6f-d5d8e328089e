from pydantic import BaseModel


class Token(BaseModel):
    access_token: str
    token_type: str


class TokenData(BaseModel):
    email: str | None = None


class UserInDB(BaseModel):
    email: str
    hashed_password: str
    is_active: bool


class UserCreate(BaseModel):
    email: str
    password: str
    full_name: str


class UserPublic(BaseModel):
    email: str
    full_name: str
    is_active: bool
