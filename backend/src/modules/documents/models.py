from sqlalchemy import (
    Column,
    Integer,
    String,
    Text,
    DateTime,
    ForeignKey,
    ARRAY,
)
from sqlalchemy.orm import relationship, Mapped
from sqlalchemy.sql import func
from pgvector.sqlalchemy import Vector
from core.db.database import Base
from typing import List


class Document(Base):
    __tablename__ = "documents"

    id = Column(Integer, primary_key=True, index=True)
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=False)
    title = Column(String, nullable=False, index=True)
    body_md = Column(Text, nullable=False)
    tags: Mapped[List[str]] = Column(ARRAY(String))  # type: ignore[arg-type, assignment]
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    customer = relationship("Customer", back_populates="documents")
    embeddings = relationship("Embedding", back_populates="document")

    def __repr__(self):
        return f"<Document(id={self.id}, title='{self.title}')>"


class Embedding(Base):
    __tablename__ = "embeddings"

    id = Column(Integer, primary_key=True, index=True)
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=False)
    document_id = Column(Integer, ForeignKey("documents.id"), nullable=False)
    chunk_text = Column(Text, nullable=False)
    embedding = Column(Vector(1536), nullable=False)  # Assuming OpenAI embeddings size
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    customer = relationship("Customer", back_populates="embeddings")
    document = relationship("Document", back_populates="embeddings")

    def __repr__(self):
        return f"<Embedding(id={self.id}, document_id={self.document_id})>"
