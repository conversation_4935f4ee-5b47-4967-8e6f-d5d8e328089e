from typing import List
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from core.config import Settings
from modules.documents.models import Embedding, Document
from orchestrator.services.llm_integrations.embedding_factory import (
    BaseEmbeddingService,
)


async def find_similar_chunks(
    db: AsyncSession,
    embedding_service: BaseEmbeddingService,
    query_text: str,
    customer_id: int,
    settings: Settings,
    top_k: int = 5,
) -> List[str]:
    """
    Finds the most similar document chunks for a given query.
    """
    # Get the embedding vector for the query text
    query_embedding = await embedding_service.aget_embedding(query_text)

    # Use the pgvector cosine distance operator explicitly
    distance_expr = Embedding.embedding.op("<#>")(query_embedding).label("distance")

    # Construct the SQL query
    stmt = (
        select(Embedding, distance_expr)
        .filter(Embedding.customer_id == customer_id)
        .order_by(distance_expr)
        .limit(top_k)
    )

    # Execute and collect results
    results = await db.execute(stmt)
    similar_rows = results.all()  # List of (Embedding, distance)
    similar_embeddings = [row[0] for row in similar_rows]

    # Retrieve document content
    document_ids = [emb.document_id for emb in similar_embeddings]
    if not document_ids:
        return []

    documents_stmt = select(Document.body_md).filter(
        Document.id.in_(document_ids)
    )
    documents_results = await db.execute(documents_stmt)

    return [row for row in documents_results.scalars().all()]
