from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from . import crud, schemas
from core.db.database import get_db
from typing import List

router = APIRouter(
    prefix="/agents",
    tags=["agents"],
    responses={404: {"description": "Not found"}},
)


#
# Agent Endpoints
#


@router.post("/", response_model=schemas.Agent)
async def create_agent(agent: schemas.AgentCreate, db: AsyncSession = Depends(get_db)):
    return await crud.create_agent(db=db, agent=agent)


@router.get("/", response_model=List[schemas.Agent])
async def read_agents(
    skip: int = 0, limit: int = 100, db: AsyncSession = Depends(get_db)
):
    agents = await crud.get_agents(db, skip=skip, limit=limit)
    return agents


@router.get("/{agent_id}", response_model=schemas.Agent)
async def read_agent(agent_id: int, db: AsyncSession = Depends(get_db)):
    db_agent = await crud.get_agent(db, agent_id=agent_id)
    if db_agent is None:
        raise HTTPException(status_code=404, detail="Agent not found")
    return db_agent


@router.put("/{agent_id}", response_model=schemas.Agent)
async def update_agent(
    agent_id: int, agent: schemas.AgentUpdate, db: AsyncSession = Depends(get_db)
):
    db_agent = await crud.update_agent(db, agent_id=agent_id, agent=agent)
    if db_agent is None:
        raise HTTPException(status_code=404, detail="Agent not found")
    return db_agent


@router.delete("/{agent_id}", response_model=schemas.Agent)
async def delete_agent(agent_id: int, db: AsyncSession = Depends(get_db)):
    db_agent = await crud.delete_agent(db, agent_id=agent_id)
    if db_agent is None:
        raise HTTPException(status_code=404, detail="Agent not found")
    return db_agent


@router.post("/run", response_model=dict)
async def run_agent(agent_run: schemas.AgentRun, db: AsyncSession = Depends(get_db)):
    return await crud.run_agent(db=db, agent_run=agent_run)


# Additional endpoints that frontend expects
@router.post("/{agent_id}/test", response_model=dict)
async def test_agent(agent_id: int, test_data: dict, db: AsyncSession = Depends(get_db)):
    """Test an agent with provided input data."""
    # For now, return a mock response
    return {
        "success": True,
        "result": "Agent test completed successfully",
        "input": test_data,
        "agent_id": agent_id
    }


@router.post("/{agent_id}/deploy", response_model=dict)
async def deploy_agent(agent_id: int, db: AsyncSession = Depends(get_db)):
    """Deploy an agent."""
    # For now, return a mock response
    return {
        "success": True,
        "deployment_url": f"https://api.example.com/agents/{agent_id}",
        "agent_id": agent_id
    }


@router.post("/{agent_id}/undeploy", response_model=dict)
async def undeploy_agent(agent_id: int, db: AsyncSession = Depends(get_db)):
    """Undeploy an agent."""
    return {"success": True, "agent_id": agent_id}


@router.get("/{agent_id}/analytics", response_model=dict)
async def get_agent_analytics(agent_id: int, time_range: str = "7d", db: AsyncSession = Depends(get_db)):
    """Get analytics for an agent."""
    # Mock analytics data
    return {
        "agent_id": agent_id,
        "time_range": time_range,
        "total_conversations": 42,
        "avg_response_time": 1.2,
        "success_rate": 0.95,
        "user_satisfaction": 4.3
    }


@router.get("/{agent_id}/conversations", response_model=list)
async def get_agent_conversations(
    agent_id: int,
    skip: int = 0,
    limit: int = 50,
    db: AsyncSession = Depends(get_db)
):
    """Get conversation history for an agent."""
    # Mock conversation data
    return [
        {
            "id": 1,
            "agent_id": agent_id,
            "user_message": "Hello",
            "agent_response": "Hi there! How can I help you?",
            "timestamp": "2024-01-01T12:00:00Z"
        }
    ]


@router.post("/validate-config", response_model=dict)
async def validate_agent_config(config_data: dict):
    """Validate agent configuration."""
    # Basic validation - in real implementation, this would be more comprehensive
    required_fields = ["name"]
    missing_fields = [field for field in required_fields if field not in config_data.get("config", {})]

    if missing_fields:
        return {
            "valid": False,
            "errors": [f"Missing required field: {field}" for field in missing_fields]
        }

    return {"valid": True, "errors": []}



