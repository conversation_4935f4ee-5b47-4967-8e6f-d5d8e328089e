from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from . import schemas
from .models import Agent


#
# Agent CRUD
#


async def get_agent(db: AsyncSession, agent_id: int):
    result = await db.execute(select(Agent).filter(Agent.id == agent_id))
    return result.scalars().first()


async def get_agents(db: AsyncSession, skip: int = 0, limit: int = 100):
    result = await db.execute(select(Agent).offset(skip).limit(limit))
    return result.scalars().all()


async def create_agent(db: AsyncSession, agent: schemas.AgentCreate):
    db_agent = Agent(**agent.model_dump())
    db.add(db_agent)
    await db.commit()
    await db.refresh(db_agent)
    return db_agent


async def update_agent(db: AsyncSession, agent_id: int, agent: schemas.AgentUpdate):
    db_agent = await get_agent(db, agent_id)
    if db_agent:
        update_data = agent.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_agent, key, value)
        await db.commit()
        await db.refresh(db_agent)
    return db_agent


async def delete_agent(db: AsyncSession, agent_id: int):
    db_agent = await get_agent(db, agent_id)
    if db_agent:
        await db.delete(db_agent)
        await db.commit()
    return db_agent


async def run_agent(db: AsyncSession, agent_run: schemas.AgentRun):
    # This is a placeholder. The actual implementation will depend on the agent execution logic.
    return {"status": "Agent run initiated", "details": agent_run.model_dump()}



