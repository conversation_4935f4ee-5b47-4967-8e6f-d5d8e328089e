from pydantic import BaseModel, ConfigDict
from datetime import datetime
from typing import Optional, Any


# Agent Schemas
class AgentBase(BaseModel):
    name: str
    description: Optional[str] = None
    workflow: dict[str, Any] = {}
    is_active: bool = False


class AgentCreate(AgentBase):
    pass


class AgentUpdate(AgentBase):
    pass


class Agent(AgentBase):
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class AgentRun(BaseModel):
    agent_id: int
    session_id: str
    input_data: dict[str, Any]


