from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from modules.jobs.models import Job
from . import schemas


async def create_job(db: AsyncSession, job: schemas.JobCreate):
    db_job = Job(**job.model_dump())
    db.add(db_job)
    await db.commit()
    await db.refresh(db_job)
    return db_job


async def get_job(db: AsyncSession, job_id: int):
    result = await db.execute(select(Job).filter(Job.id == job_id))
    return result.scalars().first()


async def get_jobs(db: AsyncSession, skip: int = 0, limit: int = 100):
    result = await db.execute(select(Job).offset(skip).limit(limit))
    return result.scalars().all()


async def update_job(db: AsyncSession, job_id: int, job: schemas.JobUpdate):
    db_job = await get_job(db, job_id)
    if db_job:
        for key, value in job.model_dump(exclude_unset=True).items():
            setattr(db_job, key, value)
        await db.commit()
        await db.refresh(db_job)
    return db_job


async def delete_job(db: AsyncSession, job_id: int):
    db_job = await get_job(db, job_id)
    if db_job:
        await db.delete(db_job)
        await db.commit()
        return True
    return False


async def get_jobs_by_customer(db: AsyncSession, customer_id: int):
    result = await db.execute(select(Job).filter(Job.customer_id == customer_id))
    return result.scalars().all()
