from pydantic import BaseModel, ConfigDict
from datetime import datetime
from typing import Optional


class JobBase(BaseModel):
    customer_id: int
    service_type: str
    scheduled_time: datetime
    status: Optional[str] = "scheduled"
    notes: Optional[str] = None


class JobCreate(JobBase):
    pass


class JobUpdate(JobBase):
    pass


class JobInDB(JobBase):
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)
