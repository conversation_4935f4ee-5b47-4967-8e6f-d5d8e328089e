from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List

from fastapi_cache.decorator import cache

from fastapi_cache import FastAPICache

from core.db.database import get_db
from . import schemas
from . import crud
from modules.customers import crud as customer_crud
from modules.auth.service import get_current_user
from modules.auth.schemas import UserPublic

router = APIRouter(prefix="/jobs", tags=["jobs"])


@router.post("/", response_model=schemas.JobInDB, status_code=status.HTTP_201_CREATED)
async def create_job(job: schemas.JobCreate, db: AsyncSession = Depends(get_db)):
    db_customer = await customer_crud.get_customer(db, customer_id=job.customer_id)
    if db_customer is None:
        raise HTTPException(status_code=404, detail="Customer not found")
    new_job = await crud.create_job(db=db, job=job)
    await FastAPICache.clear(namespace="jobs")  # Invalidate cache for job list
    return new_job


@router.get("/", response_model=List[schemas.JobInDB])
@cache(namespace="jobs", expire=60)  # Cache for 60 seconds
async def read_jobs(
    skip: int = 0, limit: int = 100, db: AsyncSession = Depends(get_db)
):
    jobs = await crud.get_jobs(db, skip=skip, limit=limit)
    return jobs


@router.get("/{job_id}", response_model=schemas.JobInDB)
@cache(namespace="jobs", expire=60)  # Cache for 60 seconds
async def read_job(job_id: int, db: AsyncSession = Depends(get_db)):
    db_job = await crud.get_job(db, job_id=job_id)
    if db_job is None:
        raise HTTPException(status_code=404, detail="Job not found")
    return db_job


@router.put("/{job_id}", response_model=schemas.JobInDB)
async def update_job(
    job_id: int, job: schemas.JobUpdate, db: AsyncSession = Depends(get_db)
):
    db_job = await crud.update_job(db, job_id=job_id, job=job)
    if db_job is None:
        raise HTTPException(status_code=404, detail="Job not found")
    await FastAPICache.clear(
        namespace="jobs"
    )  # Invalidate cache for job list and specific job
    return db_job


@router.delete("/{job_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_job(
    job_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: UserPublic = Depends(get_current_user),
):
    success = await crud.delete_job(db, job_id=job_id)
    if not success:
        raise HTTPException(status_code=404, detail="Job not found")
    await FastAPICache.clear(
        namespace="jobs"
    )  # Invalidate cache for job list and specific job
    return {"message": "Job deleted successfully"}
