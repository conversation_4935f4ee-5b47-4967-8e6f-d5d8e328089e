import json
import os
from typing import List, Optional, Dict, Any

from modules.templates import schemas

TEMPLATES_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "..", "orchestrator", "templates") 

def _get_template_filepath(template_id: str) -> str:
    """Constructs the absolute path to a template JSON file."""
    return os.path.join(TEMPLATES_DIR, f"{template_id}.json")

async def _read_template_file(filepath: str) -> Optional[schemas.AgentTemplateResponse]:
    """Reads a JSON file and parses it into an AgentTemplateResponse schema."""
    if not os.path.exists(filepath):
        return None
    try:
        with open(filepath, 'r') as f:
            data = json.load(f)
        
        filename = os.path.basename(filepath)
        template_id = os.path.splitext(filename)[0]

        template_data = {
            "id": template_id,
            "name": data.get("workflow", {}).get("name", template_id),
            "description": data.get("workflow", {}).get("description", ""),
            "category": data.get("category", "custom"),
            "tags": data.get("tags", []),
            "workflow": data.get("workflow", {}),
            "is_public": data.get("is_public", True),
            "is_featured": data.get("is_featured", False),
            "created_by": data.get("created_by", "system"),
            "created_at": data.get("created_at", "2023-01-01T00:00:00Z"),
            "updated_at": data.get("updated_at", "2023-01-01T00:00:00Z"),
            "preview_image": data.get("preview_image"),
            "author": data.get("author"),
            "version": data.get("version", "1.0.0"),
            "usage_count": data.get("usage_count", 0),
            "rating": data.get("rating")
        }
        return schemas.AgentTemplateResponse(**template_data)
    except (json.JSONDecodeError, KeyError) as e:
        print(f"Error reading template file {filepath}: {e}")
        return None

async def get_templates(
    skip: int = 0, 
    limit: int = 100,
    category: Optional[str] = None,
    is_featured: Optional[bool] = None,
    search: Optional[str] = None
) -> List[schemas.AgentTemplateResponse]:
    """Gets all agent templates from JSON files, with optional filtering and pagination."""
    all_templates: List[schemas.AgentTemplateResponse] = []
    for filename in os.listdir(TEMPLATES_DIR):
        if filename.endswith(".json"):
            filepath = os.path.join(TEMPLATES_DIR, filename)
            template = await _read_template_file(filepath)
            if template:
                all_templates.append(template)
    
    # Apply filters
    filtered_templates = []
    for template in all_templates:
        match = True
        if category and category != "all" and template.category != category:
            match = False
        if is_featured is not None and template.is_featured != is_featured:
            match = False
        if search and not (search.lower() in template.name.lower() or search.lower() in template.description.lower()):
            match = False
        
        if match:
            filtered_templates.append(template)
            
    # Apply pagination
    return filtered_templates[skip : skip + limit]

async def get_template(template_id: str) -> Optional[schemas.AgentTemplateResponse]:
    """Gets a specific template by ID from its JSON file."""
    filepath = _get_template_filepath(template_id)
    return await _read_template_file(filepath)

async def get_template_categories() -> List[str]:
    """Gets all available template categories from JSON files."""
    categories = set()
    for filename in os.listdir(TEMPLATES_DIR):
        if filename.endswith(".json"):
            filepath = os.path.join(TEMPLATES_DIR, filename)
            template = await _read_template_file(filepath)
            if template and template.category:
                categories.add(template.category)
    return ["all"] + sorted(list(categories))

async def get_templates_count() -> int:
    """Counts the number of agent templates (JSON files)."""
    count = 0
    for filename in os.listdir(TEMPLATES_DIR):
        if filename.endswith(".json"):
            count += 1
    return count