from fastapi import APIRouter, HTTPException, Query
from typing import List, Optional

from modules.templates import crud, schemas

router = APIRouter(prefix="/templates", tags=["templates"])


@router.get("/", response_model=schemas.TemplateListResponse)
async def get_templates_endpoint(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    category: Optional[str] = Query(None),
    is_featured: Optional[bool] = Query(None),
    search: Optional[str] = Query(None)
):
    """Get all public agent templates with optional filtering"""
    templates = await crud.get_templates(
        skip=skip, limit=limit, category=category, 
        is_featured=is_featured, search=search
    )
    total = await crud.get_templates_count()
    categories = await crud.get_template_categories()
    
    return schemas.TemplateListResponse(
        templates=templates,
        total=total,
        categories=categories
    )


@router.get("/categories", response_model=List[str])
async def get_template_categories_endpoint():
    """Get all available template categories"""
    return await crud.get_template_categories()


@router.get("/featured", response_model=List[schemas.AgentTemplateResponse])
async def get_featured_templates_endpoint(
    limit: int = Query(10, ge=1, le=50)
):
    """Get featured templates"""
    return await crud.get_templates(limit=limit, is_featured=True)


@router.get("/{template_id}", response_model=schemas.AgentTemplateResponse)
async def get_template_endpoint(template_id: str):
    """Get a specific template by ID"""
    template = await crud.get_template(template_id)
    if not template:
        raise HTTPException(status_code=404, detail="Template not found")
    return template