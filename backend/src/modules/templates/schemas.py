from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from datetime import datetime


class AgentTemplateBase(BaseModel):
    name: str
    description: Optional[str] = None
    category: str = "custom"
    tags: List[str] = []
    workflow: Dict[str, Any]
    is_public: bool = True
    is_featured: bool = False
    created_by: Optional[str] = None


class AgentTemplateCreate(AgentTemplateBase):
    pass


class AgentTemplateUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[List[str]] = None
    workflow: Optional[Dict[str, Any]] = None
    is_public: Optional[bool] = None
    is_featured: Optional[bool] = None


class AgentTemplateInDB(AgentTemplateBase):
    id: str
    created_at: datetime
    updated_at: Optional[datetime] = None

    model_config = {"from_attributes": True}


class AgentTemplateResponse(AgentTemplateInDB):
    preview_image: Optional[str] = None
    author: Optional[str] = None
    version: Optional[str] = None
    usage_count: Optional[int] = None
    rating: Optional[float] = None



class TemplateListResponse(BaseModel):
    templates: List[AgentTemplateResponse]
    total: int
    categories: List[str]
