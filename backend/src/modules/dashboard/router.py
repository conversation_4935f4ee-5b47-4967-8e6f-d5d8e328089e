from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List

from core.db.database import get_db
from . import schemas
from . import service
from modules.auth.service import get_current_user
from modules.auth.schemas import UserPublic

router = APIRouter(prefix="/dashboard", tags=["Dashboard"])


@router.get("/summary", response_model=schemas.DashboardSummary)
async def get_summary(db: AsyncSession = Depends(get_db)):
    summary = await service.get_dashboard_summary(db)
    return summary


@router.get("/recent-customers", response_model=List[schemas.CustomerInDB])
async def get_recent_customers(db: AsyncSession = Depends(get_db)):
    customers = await service.get_recent_customers(db, limit=5)
    return customers


@router.get("/recent-calls", response_model=List[schemas.CallHistoryInDB])
async def get_recent_calls(
    db: AsyncSession = Depends(get_db),
    current_user: UserPublic = Depends(get_current_user),
):
    calls = await service.get_recent_calls(db, limit=5)
    return calls
