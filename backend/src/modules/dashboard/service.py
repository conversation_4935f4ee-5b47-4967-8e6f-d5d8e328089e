from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func
from datetime import datetime, timedelta

from modules.customers.models import Customer
from modules.call.models import CallHistory
from modules.call.models import PhoneNumber
from modules.dashboard import schemas


async def get_dashboard_summary(db: AsyncSession):
    total_customers_result = await db.execute(select(func.count(Customer.id)))
    total_customers = total_customers_result.scalar_one()

    twenty_four_hours_ago = datetime.utcnow() - timedelta(hours=24)
    recent_calls_result = await db.execute(
        select(func.count(CallHistory.id)).filter(
            CallHistory.created_at >= twenty_four_hours_ago
        )
    )
    recent_calls_24h = recent_calls_result.scalar_one()

    total_numbers_result = await db.execute(select(func.count(TwilioNumber.id)))
    total_numbers = total_numbers_result.scalar_one()

    return schemas.DashboardSummary(
        total_customers=total_customers,
        recent_calls_24h=recent_calls_24h,
        total_numbers=total_numbers,
    )


async def get_recent_customers(db: AsyncSession, limit: int = 5):
    result = await db.execute(
        select(Customer).order_by(Customer.created_at.desc()).limit(limit)
    )
    return result.scalars().all()


async def get_recent_calls(db: AsyncSession, limit: int = 5):
    result = await db.execute(
        select(CallHistory).order_by(CallHistory.created_at.desc()).limit(limit)
    )
    return result.scalars().all()
