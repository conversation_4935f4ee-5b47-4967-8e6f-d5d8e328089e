from pydantic import BaseModel, ConfigDict
from datetime import datetime
from typing import Optional, List, Dict, Any

# Forward references for relationships
from modules.customers.schemas import CustomerInDB


class PhoneNumberBase(BaseModel):
    phone_number: str
    friendly_name: Optional[str] = None
    provider: str = "twilio"  # Provider-agnostic field
    account_sid: str
    customer_id: int
    capabilities: list[str] = ["voice", "sms"]


class PhoneNumberCreate(PhoneNumberBase):
    pass


class PhoneNumberUpdate(BaseModel):
    friendly_name: Optional[str] = None
    provider: Optional[str] = None


class PhoneNumberInDB(PhoneNumberBase):
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class CallHistoryBase(BaseModel):
    customer_id: int
    call_sid: str
    call_status: str
    number_id: Optional[int] = None
    call_duration: Optional[int] = None
    system_prompt: Optional[str] = None
    first_message: Optional[str] = None


class CallHistoryCreate(CallHistoryBase):
    pass


class CallHistory(CallHistoryBase):
    id: int
    
    created_at: datetime
    completed_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


class CallHistoryInDB(CallHistoryBase):
    id: int
    
    created_at: datetime
    completed_at: Optional[datetime] = None
    customer: CustomerInDB
    number: Optional[PhoneNumberInDB] = None

    model_config = ConfigDict(from_attributes=True)


class CallHistoryUpdate(BaseModel):
    
    call_duration: Optional[int] = None
    status: Optional[str] = None
    completed_at: Optional[datetime] = None


class ConversationLogEvent(BaseModel):
    call_history_id: int
    event_type: str
    timestamp: datetime
    data: Dict[str, Any]
    source: str


class CallRequest(BaseModel):
    type: str
    phone_number: Optional[str] = None
    custom_prompt: Optional[str] = None
    custom_first_message: Optional[str] = None
    webhook_data: Optional[Dict[str, Any]] = None
    agent_config: Optional[Dict[str, Any]] = None
    to: Optional[str] = None
    from_: Optional[str] = None

    class Config:
        fields = {"from_": "from"}

class CallResponse(BaseModel):
    success: bool
    message: str
    call_sid: Optional[str] = None
    call_history_id: Optional[int] = None
