from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import joinedload
from . import schemas
from modules.call.models import CallHistory, PhoneNumber, ConversationLog


async def create_call_history(
    db: AsyncSession, call_history: schemas.CallHistoryCreate
):
    db_call_history = CallHistory(**call_history.model_dump())
    db.add(db_call_history)
    await db.commit()
    await db.refresh(db_call_history)
    return db_call_history


async def get_call_history(db: AsyncSession, call_history_id: int):
    result = await db.execute(
        select(CallHistory)
        .options(joinedload(CallHistory.customer))
        .where(CallHistory.id == call_history_id)
    )
    return result.scalars().first()


async def update_call_history(db: AsyncSession, call_history_id: int, call_sid: str = None, call_status: str = None, call_duration: int = None, status: str = None):
    db_call_history = await db.get(CallHistory, call_history_id)
    if db_call_history:
        if call_sid:
            db_call_history.call_sid = call_sid
        if call_status:
            db_call_history.call_status = call_status
        if call_duration:
            db_call_history.call_duration = call_duration
        if status:
            db_call_history.call_status = status
        await db.commit()
        await db.refresh(db_call_history)
    return db_call_history


async def create_conversation_log(
    db: AsyncSession, log_data: schemas.ConversationLogEvent
):
    db_log = ConversationLog(
        call_history_id=log_data.call_history_id,
        event_type=log_data.event_type,
        data=log_data.data,
        source=log_data.source,
    )
    db.add(db_log)
    await db.commit()
    await db.refresh(db_log)
    return db_log


async def create_phone_number(db: AsyncSession, number: schemas.PhoneNumberCreate):
    db_number = PhoneNumber(**number.model_dump())
    db.add(db_number)
    await db.commit()
    await db.refresh(db_number)
    return db_number

async def get_phone_numbers(db: AsyncSession, skip: int = 0, limit: int = 100):
    result = await db.execute(select(PhoneNumber).offset(skip).limit(limit))
    return result.scalars().all()

async def get_phone_number(db: AsyncSession, number_id: int):
    result = await db.execute(select(PhoneNumber).where(PhoneNumber.id == number_id))
    return result.scalars().first()

async def update_phone_number(db: AsyncSession, number_id: int, number: schemas.PhoneNumberUpdate):
    db_number = await db.get(PhoneNumber, number_id)
    if db_number:
        update_data = number.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_number, key, value)
        await db.commit()
        await db.refresh(db_number)
    return db_number

async def delete_phone_number(db: AsyncSession, number_id: int):
    db_number = await db.get(PhoneNumber, number_id)
    if db_number:
        await db.delete(db_number)
        await db.commit()
        return True
    return False