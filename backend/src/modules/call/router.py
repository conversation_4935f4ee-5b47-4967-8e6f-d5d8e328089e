import logging

from fastapi import APIRouter, Depends, WebSocket, Request, Form, status, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List

from core.db.database import get_db
from core.middleware.websocket_logging_decorator import websocket_logging_decorator
from . import service
from . import crud
from . import schemas

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/call", tags=["call"])


@router.post("", response_model=schemas.CallResponse)
async def initiate_call_endpoint(
    call_request: schemas.CallRequest,
    db: AsyncSession = Depends(get_db),
):
    return await service.initiate_call(call_request, db)


#
# Webhook Endpoints
#


@router.post("/webhook/{call_history_id}/status")
async def twilio_status_webhook_endpoint(
    call_history_id: int, request: Request, db: AsyncSession = Depends(get_db)
):
    return await service.handle_twilio_status_webhook(call_history_id, request, db)


@router.post("/sms")
async def twilio_sms_webhook_endpoint(
    From: str = Form(...), Body: str = Form(...), db: AsyncSession = Depends(get_db)
):
    # Placeholder implementation
    logger.info(f"SMS webhook received from {From}: {Body}")
    return {"status": "ok"}


#
# WebSocket Endpoints
#


@router.websocket("/ws/phone-relay/{call_history_id}")
@websocket_logging_decorator
async def phone_relay_ws_endpoint(websocket: WebSocket, call_history_id: int):
    await service.handle_phone_relay_conversation(websocket, call_history_id)


@router.websocket("/ws/web-call")
@websocket_logging_decorator
async def web_call_ws_endpoint(websocket: WebSocket):
    await service.handle_web_call_conversation(websocket)


@router.websocket("/ws/text-chat")
@websocket_logging_decorator
async def text_chat_ws_endpoint(websocket: WebSocket):
    await service.handle_text_chat_conversation(websocket)


@router.websocket("/ws/call-updates/{call_history_id}")
@websocket_logging_decorator
async def call_updates_ws_endpoint(
    websocket: WebSocket, call_history_id: int, db: AsyncSession = Depends(get_db)
):
    await service.handle_websocket_endpoint(websocket, call_history_id, db)


#
# TwiML Endpoint
#


@router.post("/twiml/{call_history_id}")
async def generate_twiml_endpoint(request: Request, call_history_id: int):
    return await service.handle_generate_twiml(request, call_history_id)


#
# Phone Number Management Endpoints
#


@router.post(
    "/numbers",
    response_model=schemas.PhoneNumberInDB,
    status_code=status.HTTP_201_CREATED,
)
async def create_phone_number_endpoint(
    number: schemas.PhoneNumberCreate, db: AsyncSession = Depends(get_db)
):
    return await crud.create_phone_number(db=db, number=number)


@router.get("/numbers", response_model=List[schemas.PhoneNumberInDB])
async def read_phone_numbers_endpoint(
    skip: int = 0, limit: int = 100, db: AsyncSession = Depends(get_db)
):
    return await crud.get_phone_numbers(db, skip=skip, limit=limit)


@router.get("/numbers/{number_id}", response_model=schemas.PhoneNumberInDB)
async def read_phone_number_endpoint(
    number_id: int, db: AsyncSession = Depends(get_db)
):
    db_number = await crud.get_phone_number(db, number_id=number_id)
    if db_number is None:
        raise HTTPException(status_code=404, detail="Phone number not found")
    return db_number


@router.put("/numbers/{number_id}", response_model=schemas.PhoneNumberInDB)
async def update_phone_number_endpoint(
    number_id: int,
    number: schemas.PhoneNumberUpdate,
    db: AsyncSession = Depends(get_db),
):
    db_number = await crud.update_phone_number(db, number_id=number_id, number=number)
    if db_number is None:
        raise HTTPException(status_code=404, detail="Phone number not found")
    return db_number


@router.delete("/numbers/{number_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_phone_number_endpoint(
    number_id: int, db: AsyncSession = Depends(get_db)
):
    success = await crud.delete_phone_number(db, number_id=number_id)
    if not success:
        raise HTTPException(status_code=404, detail="Phone number not found")
    return {"message": "Phone number deleted successfully"}
