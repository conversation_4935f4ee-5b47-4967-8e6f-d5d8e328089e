from sqlalchemy import (
    Column,
    Integer,
    String,
    DateTime,
    ForeignKey,
    ARRAY,
    JSON,
    Text,
)
from sqlalchemy.orm import relationship, Mapped
from sqlalchemy.sql import func
from core.db.database import Base
from typing import List


class PhoneNumber(Base):
    __tablename__ = "phone_numbers"

    id = Column(Integer, primary_key=True, index=True)
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=False)
    provider = Column(String, nullable=False, default="twilio")  # Provider-agnostic field
    account_sid = Column(String, nullable=False, index=True)  # Provider-specific identifier
    phone_number = Column(String, unique=True, nullable=False, index=True)
    friendly_name = Column(String, nullable=True)
    capabilities: Mapped[List[str]] = Column(ARRAY(String), default=["voice", "sms"])  # type: ignore[arg-type, assignment]
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    customer = relationship("Customer", back_populates="phone_numbers")
    call_history = relationship("CallHistory", back_populates="number")

    def __repr__(self):
        return f"<PhoneNumber(id={self.id}, phone_number='{self.phone_number}', provider='{self.provider}')>"


class CallHistory(Base):
    __tablename__ = "call_history"

    id = Column(Integer, primary_key=True, index=True)
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=False)
    number_id = Column(Integer, ForeignKey("phone_numbers.id"), nullable=True)
    call_sid = Column(String, unique=True, index=True)
    call_status = Column(
        String
    )  # e.g., "initiated", "ringing", "in-progress", "completed", "failed"
    call_duration = Column(Integer)
    call_metadata = Column(JSON)  # type: ignore[arg-type]
    system_prompt = Column(Text)
    first_message = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True))

    customer = relationship("Customer", back_populates="call_history")
    number = relationship("PhoneNumber", back_populates="call_history")
    conversation_logs = relationship("ConversationLog", back_populates="call_history")


class ConversationLog(Base):
    __tablename__ = "conversation_logs"

    id = Column(Integer, primary_key=True, index=True)
    call_history_id = Column(Integer, ForeignKey("call_history.id"), nullable=False)
    event_type = Column(String)  # e.g., "user_message", "agent_message", "tool_call"
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
    data = Column(JSON)  # type: ignore[arg-type]
    source = Column(String)  # e.g., "twilio_stream", "conversation_manager"

    call_history = relationship("CallHistory", back_populates="conversation_logs")
