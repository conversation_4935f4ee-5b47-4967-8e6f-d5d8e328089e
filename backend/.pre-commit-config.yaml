repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.6.0
    hooks:
      - id: check-yaml
      - id: end-of-file-fixer
      - id: trailing-whitespace
      - id: debug-statements
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.5.0
    hooks:
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]
      - id: ruff-format
        exclude: ^temp/|.*_generated\\.py$
  - repo: local
    hooks:
      - id: mypy
        name: mypy
        entry: uv run mypy src
        language: system
        types: [python]
        pass_filenames: false
        always_run: true
      # - id: pytest
      #   name: pytest
      #   entry: bash -c "uv pip install -e ".[dev]" && uv run pytest"
      #   language: system
      #   types: [python]
      #   pass_filenames: false
      #   always_run: true
