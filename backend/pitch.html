<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>24/7 Virtual Assistant for Home Services</title>
    <script src="[https://cdn.tailwindcss.com](https://cdn.tailwindcss.com)"></script>
    <script src="[https://cdn.jsdelivr.net/npm/chart.js](https://cdn.jsdelivr.net/npm/chart.js)"></script>
    <link
        href="[https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap](https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap)"
        rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }

        .chart-container {
            position: relative;
            width: 100%;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            height: 300px;
            max-height: 400px;
        }

        @media (min-width: 768px) {
            .chart-container {
                height: 350px;
            }
        }
    </style>
</head>
<body class="bg-gray-100">

    <div class="container mx-auto p-4 md:p-8">

        <header class="text-center mb-12">
            <h1 class="text-4xl md:text-5xl font-bold text-gray-800 mb-2">The 24/7 Virtual Assistant for Home Services
            </h1>
            <p class="text-lg text-gray-600">Your Solution for Lost Calls and Inefficient Intake</p>
        </header>

        <section class="mb-12">
            <h2 class="text-3xl font-bold text-center mb-6 text-gray-800">The Hidden Cost of Inefficiency</h2>
            <div class="grid md:grid-cols-3 gap-8 text-center">
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-semibold mb-2">Missed Calls are Lost Revenue</h3>
                    <p class="text-5xl font-bold text-red-500">35%</p>
                    <p class="text-gray-600 mt-2">of after-hours calls are potential jobs lost to competitors.</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-semibold mb-2">Technician Time is Money</h3>
                    <p class="text-5xl font-bold text-blue-500">15 hrs</p>
                    <p class="text-gray-600 mt-2">per week spent on low-value tasks like repetitive FAQ calls.</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-semibold mb-2">A Broken Funnel</h3>
                    <p class="text-5xl font-bold text-yellow-500">20%</p>
                    <p class="text-gray-600 mt-2">of intake information is incorrect due to human error.</p>
                </div>
            </div>
        </section>

        <section class="mb-12">
            <h2 class="text-3xl font-bold text-center mb-6 text-gray-800">A Smart AI Assistant, Ready to Work</h2>
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-semibold mb-2">Intelligent Booking</h3>
                    <p class="text-gray-600">Qualifies leads and books appointments directly into your existing
                        calendars.</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-semibold mb-2">Instant Answers</h3>
                    <p class="text-gray-600">Provides real-time answers to common questions, freeing up your team.</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-semibold mb-2">Seamless Handoff</h3>
                    <p class="text-gray-600">Transfers high-value or frustrated callers to a human agent with full
                        context.</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md col-span-1 lg:col-span-3">
                    <h3 class="text-xl font-semibold mb-2">Clear ROI</h3>
                    <p class="text-gray-600">A dashboard shows you the tangible value and cost savings of every call.
                    </p>
                </div>
            </div>
        </section>

        <section class="mb-12">
            <h2 class="text-3xl font-bold text-center mb-6 text-gray-800">How It Works: A Battle-Tested Stack</h2>
            <div class="bg-white p-6 rounded-lg shadow-md">
                <div class="chart-container mx-auto">
                    <canvas id="techStackChart"></canvas>
                </div>
            </div>
        </section>

        <section class="mb-12">
            <h2 class="text-3xl font-bold text-center mb-6 text-gray-800">Value-Based Pricing</h2>
            <div class="bg-white p-6 rounded-lg shadow-md">
                <div class="chart-container mx-auto">
                    <canvas id="pricingModelChart"></canvas>
                </div>
            </div>
        </section>

        <section class="mb-12">
            <h2 class="text-3xl font-bold text-center mb-6 text-gray-800">Go-to-Market Strategy</h2>
            <div class="grid md:grid-cols-2 gap-8">
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-semibold mb-2">Target Audience</h3>
                    <p class="text-gray-600">Small to medium-sized HVAC & plumbing businesses.</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-semibold mb-2">Outbound Pitch</h3>
                    <p class="text-gray-600">A simple, compelling email offering to demonstrate value by handling their
                        overflow line.</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-semibold mb-2">Rapid Onboarding</h3>
                    <p class="text-gray-600">A streamlined checklist to go live in just a few days.</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-semibold mb-2">Strategic Wedge</h3>
                    <p class="text-gray-600">Our focus on answering after-hours and overflow calls provides immediate,
                        tangible ROI without disrupting their existing operations.</p>
                </div>
            </div>
        </section>

        <section class="mb-12">
            <h2 class="text-3xl font-bold text-center mb-6 text-gray-800">Traction & Metrics</h2>
            <div class="bg-white p-6 rounded-lg shadow-md">
                <div class="chart-container mx-auto" style="height: 400px;">
                    <canvas id="kpiChart"></canvas>
                </div>
            </div>
        </section>

        <section class="mb-12">
            <h2 class="text-3xl font-bold text-center mb-6 text-gray-800">Expanding Beyond the Call</h2>
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-semibold mb-2">AI-Powered Outbound</h3>
                    <p class="text-gray-600">Future models could handle outbound calls for appointment reminders,
                        follow-ups, and customer feedback surveys.</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-semibold mb-2">Omni-channel Support</h3>
                    <p class="text-gray-600">Expand beyond voice and SMS to include live chat on a web widget and
                        messaging platforms.</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-semibold mb-2">Predictive Lead Scoring</h3>
                    <p class="text-gray-600">Use call data to score leads and prioritize those with the highest
                        potential value.</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-semibold mb-2">Proactive Engagement</h3>
                    <p class="text-gray-600">The AI could identify repeat customers and offer personalized discounts or
                        service reminders.</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md col-span-1 lg:col-span-2">
                    <h3 class="text-xl font-semibold mb-2">API Ecosystem</h3>
                    <p class="text-gray-600">Integrate with more platforms (e.g., payment systems, inventory management)
                        to create a more comprehensive solution.</p>
                </div>
            </div>
        </section>

    </div>

    <script>
        const wrapLabel = (label) => {
            const words = label.split(' ');
            const lines = [];
            let currentLine = '';
            words.forEach(word => {
                if ((currentLine + word).length > 16) {
                    lines.push(currentLine.trim());
                    currentLine = '';
                }
                currentLine += word + ' ';
            });
            lines.push(currentLine.trim());
            return lines;
        };

        const tooltipTitleCallback = (tooltipItems) => {
            const item = tooltipItems[0];
            let label = item.chart.data.labels[item.dataIndex];
            if (Array.isArray(label)) {
                return label.join(' ');
            } else {
                return label;
            }
        };

        const techStackCtx = document.getElementById('techStackChart').getContext('2d');
        new Chart(techStackCtx, {
            type: 'bar',
            data: {
                labels: ['Twilio Voice', 'Whisper ASR', 'LangGraph Agent', 'ElevenLabs TTS', 'ServiceTitan', 'Jobber', 'Housecall Pro', 'n8n Workflows', 'Redis', 'Postgres'],
                datasets: [{
                    label: 'Technology Stack',
                    data: [10, 9, 10, 9, 8, 8, 8, 9, 10, 10],
                    backgroundColor: 'rgba(54, 162, 235, 0.5)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                maintainAspectRatio: false,
                plugins: {
                    tooltip: {
                        callbacks: {
                            title: tooltipTitleCallback
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        const pricingModelCtx = document.getElementById('pricingModelChart').getContext('2d');
        new Chart(pricingModelCtx, {
            type: 'doughnut',
            data: {
                labels: ['Low-Risk Pilot', 'Per-Minute Usage', 'Success Kicker'],
                datasets: [{
                    label: 'Pricing Model',
                    data: [30, 40, 30],
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.5)',
                        'rgba(54, 162, 235, 0.5)',
                        'rgba(255, 206, 86, 0.5)'
                    ],
                    borderColor: [
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                maintainAspectRatio: false,
                plugins: {
                    tooltip: {
                        callbacks: {
                            title: tooltipTitleCallback
                        }
                    }
                }
            }
        });

        const kpiCtx = document.getElementById('kpiChart').getContext('2d');
        new Chart(kpiCtx, {
            type: 'radar',
            data: {
                labels: ['Booking Rate', 'Handoff Rate', 'Latency', 'Accuracy'],
                datasets: [{
                    label: 'Key Performance Indicators',
                    data: [20, 25, 90, 95],
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    borderColor: 'rgba(75, 192, 192, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                maintainAspectRatio: false,
                plugins: {
                    tooltip: {
                        callbacks: {
                            title: tooltipTitleCallback
                        }
                    }
                },
                scales: {
                    r: {
                        angleLines: {
                            display: false
                        },
                        suggestedMin: 0,
                        suggestedMax: 100
                    }
                }
            }
        });
    </script>
</body>
</html>
