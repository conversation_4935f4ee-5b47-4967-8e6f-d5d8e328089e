import os
from typing import Async<PERSON>enerator, Generator
from unittest.mock import patch

import pytest_asyncio
from httpx import AsyncClient, ASGITransport
from sqlalchemy.ext.asyncio import (
    create_async_engine,
    async_sessionmaker,
    AsyncSession,
    AsyncEngine,
)
from sqlalchemy.engine.url import make_url
from dotenv import load_dotenv
from sqlalchemy import text
from asyncpg import connect
from asyncpg.exceptions import DuplicateDatabaseError

# Load environment variables from .env.test
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), "..", ".env.test"))
os.environ["TESTING"] = "True"

from core.db.database import Base, get_db
from src.main import app
from core.config import get_settings
from modules.users.models import User
from modules.auth.service import create_access_token
from passlib.context import CryptContext
from core.utils.logging import setup_logging

setup_logging()

settings = get_settings()
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

settings = get_settings()
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


async def _ensure_database_exists():
    """Connects to the postgres DB and creates the test_db if it doesn't exist."""
    db_url = make_url(settings.DATABASE_URL)
    db_name = db_url.database

    # Connect to the default 'postgres' database to create the new one
    conn = await connect(
        user=db_url.username,
        password=db_url.password,
        host=db_url.host,
        port=db_url.port,
        database="postgres",
    )
    try:
        await conn.execute(f'CREATE DATABASE "{db_name}"')
        print(f"Database '{db_name}' created.")
    except DuplicateDatabaseError:
        print(f"Database '{db_name}' already exists.")
        pass  # Database already exists
    finally:
        await conn.close()


@pytest_asyncio.fixture(scope="session", autouse=True)
async def manage_test_database():
    """
    Session-scoped fixture to ensure the test database exists before any tests run.
    """
    await _ensure_database_exists()

    # Disable PostHog telemetry during tests
    with patch("mem0.memory.telemetry.AnonymousTelemetry") as mock_telemetry:
        mock_telemetry.return_value.disabled = True
        yield


@pytest_asyncio.fixture(scope="function")
async def db_engine() -> AsyncGenerator[AsyncEngine, None]:
    """
    Pytest fixture to create a test database engine for the session.
    """
    engine = create_async_engine(settings.DATABASE_URL, echo=False)

    async with engine.begin() as conn:
        await conn.execute(text("CREATE EXTENSION IF NOT EXISTS vector"))
        await conn.run_sync(Base.metadata.drop_all)
        await conn.run_sync(Base.metadata.create_all)

    yield engine

    await engine.dispose()


@pytest_asyncio.fixture(scope="function")
async def db_session(db_engine: AsyncEngine) -> AsyncGenerator[AsyncSession, None]:
    """
    Pytest fixture to provide an async database session for each test function.
    """
    Session = async_sessionmaker(bind=db_engine, expire_on_commit=False)
    async with Session() as session:
        yield session


@pytest_asyncio.fixture(scope="function")
async def client(db_session: AsyncSession) -> AsyncGenerator[AsyncClient, None]:
    """
    Pytest fixture to create a basic, unauthenticated async test client.
    """

    def override_get_db() -> Generator[AsyncSession, None, None]:
        yield db_session

    app.dependency_overrides[get_db] = override_get_db

    async with AsyncClient(
        transport=ASGITransport(app=app), base_url="http://test"
    ) as c:
        yield c

    del app.dependency_overrides[get_db]


@pytest_asyncio.fixture(scope="function")
async def authenticated_client(
    db_session: AsyncSession, client: AsyncClient
) -> AsyncClient:
    """
    Pytest fixture to create an authenticated async test client.
    """
    test_user = User(
        email="<EMAIL>",
        hashed_password=pwd_context.hash("testpassword"),
        full_name="Test User",
        is_active=True,
    )

    db_session.add(test_user)
    await db_session.commit()
    await db_session.refresh(test_user)

    token = create_access_token(data={"sub": test_user.email})
    client.headers.update({"Authorization": f"Bearer {token}"})

    return client
