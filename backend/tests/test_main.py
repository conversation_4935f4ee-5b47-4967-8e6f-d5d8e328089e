from httpx import AsyncClient


async def test_read_main(client: AsyncClient):
    response = await client.get("/")
    assert response.status_code == 200
    assert response.json() == {"message": "Hello from <PERSON>eson backend!"}


async def test_health_check(client: AsyncClient):
    """
    Tests that the health check endpoint is working correctly.
    """
    response = await client.get("/health")
    assert response.status_code == 200
    assert response.json() == {"status": "ok"}


from fastapi.testclient import TestClient
from main import app


def test_websocket():
    client = TestClient(app)
    with client.websocket_connect("/ws") as websocket:
        data = websocket.receive_json()
        assert data == {"msg": "Hello WebSocket"}
        websocket.send_json({"text": "Hello from test"})
        data = websocket.receive_json()
        assert data == {"msg": "Message text was: Hello from test"}
