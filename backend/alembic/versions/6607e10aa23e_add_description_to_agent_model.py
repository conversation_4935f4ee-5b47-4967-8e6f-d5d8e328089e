"""Add description to Agent model

Revision ID: 6607e10aa23e
Revises: 11687ed232b6
Create Date: 2025-08-21 00:41:41.588574

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '6607e10aa23e'
down_revision: Union[str, None] = '11687ed232b6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('agents', sa.Column('description', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('agents', 'description')
    # ### end Alembic commands ###
