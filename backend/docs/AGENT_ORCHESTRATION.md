# Agent Orchestration Configuration

This document explains how to configure the agent's conversational workflow using a JSON file, inspired by n8n's workflow definition.

The agent's behavior, including its nodes (steps), the functions they execute, their dependencies, and the flow between them, are all defined in `src/agent/agent_config.json`.

## Purpose of `agent_config.json`

The `agent_config.json` file serves as the blueprint for your agent's conversational flow. It allows you to:

- **Define the sequence of operations**: Specify the order in which the agent processes information and responds.
- **Integrate custom logic**: Link to specific Python functions that implement the agent's capabilities (e.g., intent detection, RAG, tool execution).
- **Manage dependencies**: Declare which services (like LLM service, database, settings) a particular node function requires.
- **Control conversational flow**: Implement conditional logic to branch the conversation based on detected intent or other criteria.
- **Centralize configuration**: Keep the agent's orchestration logic separate from the core code, making it easier to modify and extend.

## Workflow Structure (n8n-like format)

The `agent_config.json` follows a structure similar to n8n workflows, making it intuitive for those familiar with visual programming tools.

```json
{
  "workflow": {
    "name": "Customer Support Agent Workflow",
    "description": "Orchestrates the customer support agent's conversational flow.",
    "active": true,
    "id": "agent-workflow-1",
    "nodes": [
      // Node definitions go here
    ],
    "connections": {
      // Connection definitions go here
    },
    "start_node_id": "input_gate_node",
    "global_tools": [
      // Global tool definitions go here
    ]
  }
}
```

### Nodes

Each object in the `nodes` array represents a step in the agent's workflow. Key properties include:

- `id` (string, required): A unique identifier for the node.
- `name` (string, required): A human-readable name for the node.
- `type` (string, required): The type of node. Currently, `pythonFunction` is supported, indicating that the node executes a Python function.
- `functionPath` (string, required): The Python path to the function executed by this node (e.g., `agent.nodes.input_gate`). This path is relative to the `src` directory.
- `parameters` (object, optional): A dictionary of parameters specific to this node's function. For example, a node executing an LLM call might have a `system_prompt` here.
- `dependencies` (array of strings, optional): A list of service dependencies that the `functionPath` requires. Supported dependencies include `llm_service`, `db`, and `settings`. These are automatically injected by the graph builder.
- `position` (array of numbers, optional): X and Y coordinates for visual layout (for future visualization tools).

**Example Node Definition:**

```json
{
  "id": "intent_detect_node",
  "name": "Intent Detection",
  "type": "pythonFunction",
  "functionPath": "agent.nodes.intent_detect",
  "parameters": {
    "system_prompt": "Given the following conversation, what is the user's intent? Possible intents: book_appointment, rag_query, handoff_human. Return only the intent name."
  },
  "dependencies": ["llm_service"],
  "position": [0, 2]
}
```

### Connections

The `connections` object defines the flow between nodes. It's a dictionary where keys are source node IDs, and values describe the outgoing connections.

- **`main` connections**: For linear flows, an array of objects specifying the `node` (target node ID or `END`), `type` (e.g., `main`), and `index` (for multiple outputs, if applicable).
- **`conditional` connections**: For branching logic, an object with:
  - `type` (string, required): The type of conditional logic. Can be `pythonFunction` (executes a Python function) or `aiBased` (uses an LLM to decide based on a prompt).
  - `conditionFunctionPath` (string, conditional, required if `type` is `pythonFunction`): The Python path to a function that determines the conditional path (e.g., `agent.graph.should_book_appointment`). This function should accept the `AgentState` and return a value (e.g., `True`, `False`, or a string) that maps to a key in the `paths` object.
  - `prompt` (string, conditional, required if `type` is `aiBased`): A natural language prompt for the LLM to decide the next path. The LLM's response will be matched against the keys in the `paths` object.
  - `paths` (object, required): A mapping where keys are the expected return values of the `conditionFunctionPath` (for `pythonFunction` type) or the LLM's response (for `aiBased` type), and values are arrays of target node objects (similar to `main` connections).

**Example Connections Definition:**

```json
"connections": {
  "state_load_node": {
    "main": [
      { "node": "intent_detect_node", "type": "main", "index": 0 }
    ]
  },
  "intent_detect_node": {
    "conditional": {
      "conditionFunctionPath": "agent.graph.should_book_appointment",
      "paths": {
        "True": [ { "node": "book_appointment_node", "type": "main", "index": 0 } ],
        "False": [ { "node": "rag_fetch_node", "type": "main", "index": 0 } ]
      }
    }
  },
  "state_persist_node": {
    "main": [
      { "node": "END", "type": "main", "index": 0 }
    ]
  }
}
```

### Start Node

The `start_node_id` (string, required) in the `workflow` object specifies the `id` of the node where the workflow execution begins.

### First Message

The `first_message` (string, optional) in the `workflow` object defines the initial message the agent speaks at the beginning of a conversation. This can be a static greeting or include placeholders like `{customer_name}` which will be dynamically replaced at runtime.

**Example First Message:**

```json
"first_message": "Hello, {customer_name}! How can I help you today?"
```

### Speech Configuration (`speech_config`)

The `speech_config` object (optional) within the `workflow` defines parameters for Speech-to-Text (STT) and Text-to-Speech (TTS) services, allowing for fine-grained control over the agent's voice and listening behavior.

- `stt_vad_threshold` (number, optional): Voice Activity Detection (VAD) sensitivity for STT. A value between 0 and 1, where higher values mean less aggressive VAD (more likely to detect speech).
- `tts_voice_id` (string, optional): The specific voice ID to use for TTS, if supported by the chosen TTS provider.
- `tts_speaking_rate` (number, optional): The speaking rate for TTS. A value of 1.0 is normal. Higher values are faster, lower values are slower.

**Example Speech Configuration:**

```json
"speech_config": {
  "stt_vad_threshold": 0.7,
  "tts_voice_id": "default",
  "tts_speaking_rate": 1.0
}
```

### Conversation Settings (`conversation_settings`)

The `conversation_settings` object (optional) within the `workflow` defines parameters for managing the conversational flow, including timeouts and reprompting strategies.

- `silence_timeout_seconds` (number, optional): The duration in seconds the agent waits for user input before considering it a silence and potentially reprompting or ending the conversation.
- `reprompt_messages` (array of strings, optional): A list of messages the agent can use to reprompt the user after a period of silence or no input.
- `max_reprompts` (number, optional): The maximum number of times the agent will reprompt the user before taking a fallback action (e.g., ending the conversation).
- `no_input_fallback_message` (string, optional): The message the agent speaks when no user input is detected after reprompts.
- `no_match_fallback_message` (string, optional): The message the agent speaks when it cannot understand the user's input after multiple attempts.

**Example Conversation Settings:**

```json
"conversation_settings": {
  "silence_timeout_seconds": 10,
  "reprompt_messages": [
    "Are you still there?",
    "Did you have any other questions?"
  ],
  "max_reprompts": 2,
  "no_input_fallback_message": "I didn't hear anything. Can you please repeat that?",
  "no_match_fallback_message": "I'm sorry, I didn't understand that. Could you rephrase your request?"
}
```

### Global Tools

The `global_tools` (array of objects, optional) defines tools that are available globally to the agent, typically passed to the LLM for tool-calling capabilities.

- `name` (string, required): The name of the tool.
- `functionPath` (string, required): The Python path to the tool function (e.g., `agent.tools.langchain_tools.get_customer_details`).

**Example Global Tools Definition:**

```json
"global_tools": [
  {
    "name": "get_customer_details",
    "functionPath": "agent.tools.langchain_tools.get_customer_details"
  },
  {
    "name": "schedule_job",
    "functionPath": "agent.tools.langchain_tools.schedule_job"
  }
]
```

## JSON Schema Reference

For a detailed and formal definition of the `agent_config.json` structure, please refer to the JSON schema located at:

`docs/schemas/agent_config_schema.json`
