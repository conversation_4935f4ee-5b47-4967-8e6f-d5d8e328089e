{"type": "object", "title": "Agent Workflow Schema", "description": "Schema for defining n8n-like workflows for agent orchestration.", "required": ["workflow"], "properties": {"workflow": {"type": "object", "required": ["name", "nodes", "connections", "start_node_id"], "properties": {"name": {"type": "string", "description": "The name of the workflow."}, "description": {"type": "string", "description": "A brief description of the workflow."}, "active": {"type": "boolean", "description": "Whether the workflow is active."}, "id": {"type": "string", "description": "A unique identifier for the workflow."}, "nodes": {"type": "array", "description": "An array of node definitions.", "items": {"type": "object", "required": ["id", "name", "type", "functionPath"], "properties": {"id": {"type": "string", "description": "Unique identifier for the node."}, "name": {"type": "string", "description": "Display name of the node."}, "type": {"type": "string", "description": "Type of the node (e.g., 'pythonFunction', 'apiCall')."}, "functionPath": {"type": "string", "description": "Python path to the function to be executed by this node."}, "parameters": {"type": "object", "description": "Parameters specific to this node's function."}, "dependencies": {"type": "array", "description": "List of service dependencies required by the node's function.", "items": {"type": "string"}}, "position": {"type": "array", "description": "Optional: X and Y coordinates for visual layout.", "items": {"type": "number"}, "minItems": 2, "maxItems": 2}}}}, "connections": {"type": "object", "description": "Defines the flow between nodes.", "patternProperties": {"^[a-zA-Z0-9_]+$": {"oneOf": [{"type": "object", "properties": {"main": {"type": "array", "items": {"type": "object", "required": ["node", "type", "index"], "properties": {"node": {"type": "string"}, "type": {"type": "string"}, "index": {"type": "number"}}}}}}, {"type": "object", "properties": {"conditional": {"type": "object", "required": ["type", "paths"], "properties": {"type": {"type": "string", "enum": ["pythonFunction", "aiBased"]}, "conditionFunctionPath": {"type": "string"}, "prompt": {"type": "string"}, "paths": {"type": "object", "patternProperties": {"^(True|False|None|[a-zA-Z0-9_]+)$": {"type": "array", "items": {"type": "object", "required": ["node", "type", "index"], "properties": {"node": {"type": "string"}, "type": {"type": "string"}, "index": {"type": "number"}}}}}}}}}}]}}}, "start_node_id": {"type": "string", "description": "The ID of the node where the workflow execution begins."}, "first_message": {"type": "string", "description": "Optional: The initial message the agent speaks."}, "speech_config": {"type": "object", "properties": {"stt_vad_threshold": {"type": "number"}, "tts_voice_id": {"type": "string"}, "tts_speaking_rate": {"type": "number"}}}, "conversation_settings": {"type": "object", "properties": {"silence_timeout_seconds": {"type": "number"}, "reprompt_messages": {"type": "array", "items": {"type": "string"}}, "max_reprompts": {"type": "number"}, "no_input_fallback_message": {"type": "string"}, "no_match_fallback_message": {"type": "string"}}}, "tools": {"type": "array", "description": "Tools available to the agent.", "items": {"oneOf": [{"type": "object", "properties": {"type": {"const": "webhook"}, "name": {"type": "string"}, "description": {"type": "string"}, "api_schema": {"type": "object", "properties": {"url": {"type": "string"}, "method": {"type": "string"}, "path_params_schema": {"$ref": "#/definitions/toolParameterPropertyArray"}, "query_params_schema": {"$ref": "#/definitions/toolParameterPropertyArray"}, "request_body_schema": {"$ref": "#/definitions/toolParameter"}, "request_headers": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string"}, "name": {"type": "string"}, "value": {"type": "string"}, "secret_id": {"type": "string"}}}}}}}}, {"type": "object", "properties": {"type": {"const": "python"}, "name": {"type": "string"}, "description": {"type": "string"}, "functionPath": {"type": "string"}, "parameters": {"type": "object"}}}]}}}}}, "definitions": {"toolParameterProperty": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string"}, "value_type": {"type": "string"}, "description": {"type": "string"}, "dynamic_variable": {"type": "string"}, "constant_value": {}, "required": {"type": "boolean"}, "properties": {"type": "array", "items": {"$ref": "#/definitions/toolParameterProperty"}}}}, "toolParameterPropertyArray": {"type": "array", "items": {"$ref": "#/definitions/toolParameterProperty"}}, "toolParameter": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string"}, "description": {"type": "string"}, "properties": {"$ref": "#/definitions/toolParameterPropertyArray"}, "required": {"type": "boolean"}}}}}