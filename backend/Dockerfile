# Use a slim, modern Python base image
FROM python:3.12-slim

# Set the working directory
WORKDIR /app

# Add /app/src to PYTHONPATH
ENV PYTHONPATH=/app/src

# Install system dependencies including C++ compiler for forecasting libraries
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    postgresql-client \
    curl \
    build-essential \
    gfortran \
    libopenblas-dev \
    liblapack-dev \
    pkg-config \
    python3-dev \
    && rm -rf /var/lib/apt/lists/*

COPY pyproject.toml ./

# Install uv
RUN pip install --no-cache-dir uv

COPY . .

RUN pip install --no-cache-dir --upgrade pip
RUN pip install --no-cache-dir .

# Expose port

EXPOSE 8000

RUN echo "--- Contents of /app/src ---"
RUN ls -F /app/src
RUN echo "--- PYTHONPATH ---"
RUN echo $PYTHONPATH

# Run the application
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
