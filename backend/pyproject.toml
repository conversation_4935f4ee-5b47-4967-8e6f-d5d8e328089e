[project]
name = "backend"
version = "0.1.0"
requires-python = ">=3.12, <3.13"

dependencies = [
    "fastapi[all]==0.116.1",
    "uvicorn[standard]==0.35.0",
    "python-dotenv==1.1.1",
    "requests==2.32.4",
    "langgraph==0.6.5",
    "openai==1.99.9",
    "elevenlabs==2.9.2",
    "twilio==9.7.0",
    "psycopg2-binary==2.9.10",
    "asyncpg==0.30.0",
    "redis==6.4.0",
    "pgvector==0.4.1",
    "n8n==0.11.0",
    "pydantic-settings==2.10.1",
    "passlib[bcrypt]==1.7.4",
    "python-jose[cryptography]==3.5.0",
    "python-multipart>=0.0.9",
    "deepgram-sdk>=4.7.0",
    "langchain>=0.1.16",
    "SQLAlchemy==2.0.43",
    "alembic==1.16.4",
    "soundfile==0.13.1",
    "fastapi-cache2==0.2.2",
    "assemblyai==0.43.1",
    "mypy>=1.17.1",
    "ruff>=0.12.9",
    "pre-commit>=4.3.0",
    "langchain-google-genai==2.1.9",
    "langchain-openai==0.3.30",
    "websockets>=12.0",
    "aiofiles",
    "g711",
    "resampy",
    "httpx-ws>=0.7.2",
    "vosk",
    "webrtcvad",
    "dspy-ai",
    "mem0ai",
    "guardrails-ai",
    "pydantic",
    "python-json-logger",
    "httpx>=0.28.1",
    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
]

[project.optional-dependencies]
dev = [
    "pytest",
    "pytest-asyncio",
    "async-asgi-testclient",
    "httpx",
"aiosqlite",
]

[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
package-dir = {"" = "src"}

[tool.mypy]

[tool.ruff]
