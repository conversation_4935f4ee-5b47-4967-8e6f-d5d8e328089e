# Professional Agent Builder System

A comprehensive, production-ready agent builder system with intuitive UI for creating single and multi-node AI agents, featuring native DSPy support, advanced memory management, live chat testing, and comprehensive template management.

## 🚀 Features

### Core Agent Building
- **Visual Workflow Designer**: Drag-and-drop interface for building complex agent workflows
- **Multi-Node Support**: Create sophisticated agents with multiple processing nodes
- **Single-Node Testing**: Quick testing environment for individual agent configurations
- **Real-time Execution Monitoring**: Live visualization of agent execution with node-level metrics

### Advanced AI Capabilities
- **Native DSPy Integration**: Built-in support for DSPy modules with automatic prompt optimization
- **Memory Management**: Multiple memory types (short-term, long-term, episodic, semantic, working)
- **LLM Integration**: Support for multiple LLM providers with configurable parameters
- **RAG Support**: Built-in retrieval-augmented generation capabilities

### Template System
- **Template Browser**: Discover and use pre-built agent templates
- **Template Categories**: Organized by use case (customer service, sales, analysis, etc.)
- **Template Rating**: Community-driven rating and review system
- **Import/Export**: Share and distribute agent templates

### Testing & Analytics
- **Live Chat Interface**: Real-time testing with conversation history
- **Batch Testing**: Run multiple test cases simultaneously
- **Performance Analytics**: Detailed metrics and performance monitoring
- **Execution Logs**: Comprehensive logging for debugging and optimization

### Production Features
- **Error Handling**: Comprehensive error management with user-friendly messages
- **WebSocket Support**: Real-time updates and monitoring
- **Configuration Management**: Flexible configuration system with user preferences
- **Responsive Design**: Works seamlessly across desktop and mobile devices

## 🏗️ Architecture

### Backend Components

#### Core Services
- **Agent Builder Service** (`backend/src/orchestrator/services/agent_builder.py`)
  - Workflow execution engine
  - Node management and orchestration
  - Real-time monitoring and metrics

- **Enhanced Agent Graph** (`backend/src/orchestrator/graph.py`)
  - Multi-node workflow execution
  - Enhanced node types (LLM, DSPy, Memory, RAG)
  - Execution state management

- **Memory Service** (`backend/src/orchestrator/services/memory/`)
  - Multiple memory types with different retention policies
  - Vector-based semantic search
  - Automatic memory cleanup and optimization

- **DSPy Integration** (`backend/src/orchestrator/services/dspy_factory.py`)
  - DSPy module factory for different module types
  - Prompt optimization service
  - Example-based learning

#### API Endpoints
- **Agent Builder API** (`/api/agent-builder/`)
  - Agent creation and management
  - Workflow execution
  - Real-time monitoring via WebSocket

- **Templates API** (`/api/templates/`)
  - Template discovery and management
  - Usage tracking and analytics
  - Import/export functionality

- **Node Management API** (`/api/nodes/`)
  - Node type definitions
  - Node testing and validation
  - Parameter configuration

### Frontend Components

#### Core UI Components
- **WorkflowCanvas** (`frontend/src/components/features/AgentBuilder/WorkflowCanvas.tsx`)
  - Visual workflow editor with drag-and-drop
  - Real-time execution visualization
  - Node connection management

- **NodeEditor** (`frontend/src/components/features/AgentBuilder/NodeEditor.tsx`)
  - Comprehensive node configuration interface
  - Parameter validation and testing
  - Memory and DSPy configuration

- **NodePalette** (`frontend/src/components/features/AgentBuilder/NodePalette.tsx`)
  - Categorized node types
  - Search and filtering
  - Drag-and-drop node creation

#### Specialized Components
- **MemoryConfigEditor** - Advanced memory configuration
- **DSPyConfigEditor** - DSPy module setup and optimization
- **ChatInterface** - Real-time agent testing
- **TemplateBrowser** - Template discovery and management
- **AgentAnalytics** - Performance metrics and monitoring

#### Services Layer
- **Agent Builder Service** - Core agent management
- **Template Service** - Template operations
- **API Client** - Centralized API communication with retry logic
- **Error Handler** - Comprehensive error management
- **WebSocket Service** - Real-time communication
- **Testing Service** - Automated testing capabilities
- **Config Service** - Application configuration management

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ and npm/yarn
- Python 3.9+
- Redis (for caching and real-time features)
- PostgreSQL (for data persistence)

### Backend Setup

1. **Install Dependencies**
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env
   # Configure your database, Redis, and API keys
   ```

3. **Database Setup**
   ```bash
   alembic upgrade head
   ```

4. **Start the Server**
   ```bash
   uvicorn src.main:app --reload --host 0.0.0.0 --port 8000
   ```

### Frontend Setup

1. **Install Dependencies**
   ```bash
   cd frontend
   npm install
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env.local
   # Configure API endpoints
   ```

3. **Start Development Server**
   ```bash
   npm run dev
   ```

### Access the Application
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- API Documentation: http://localhost:8000/docs

## 📖 Usage Guide

### Creating Your First Agent

1. **Navigate to Agent Builder**
   - Go to `/agents/builder` in the application
   - Choose between single-node or multi-node agent

2. **Design Your Workflow**
   - Drag nodes from the palette to the canvas
   - Connect nodes to define execution flow
   - Configure each node's parameters

3. **Configure Advanced Features**
   - Set up memory for context retention
   - Enable DSPy for prompt optimization
   - Add tools and integrations

4. **Test Your Agent**
   - Use the built-in chat interface
   - Run batch tests with multiple scenarios
   - Monitor performance metrics

5. **Deploy and Monitor**
   - Save your agent configuration
   - Monitor execution analytics
   - Optimize based on performance data

### Using Templates

1. **Browse Templates**
   - Visit the template browser
   - Filter by category, rating, or popularity
   - Preview template details and complexity

2. **Customize Templates**
   - Load a template as starting point
   - Modify nodes and connections
   - Adjust parameters for your use case

3. **Share Templates**
   - Export your agent as a template
   - Rate and review community templates
   - Contribute to the template library

## 🔧 Configuration

### Environment Variables

#### Backend
```env
DATABASE_URL=postgresql://user:password@localhost/dbname
REDIS_URL=redis://localhost:6379
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
```

#### Frontend
```env
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000
```

### Feature Flags
Configure features in the application settings:
- Template Browser
- Agent Analytics
- Real-time Chat
- Batch Testing
- Workflow Export
- Collaborative Editing (coming soon)

## 🧪 Testing

### Backend Testing
```bash
cd backend
pytest tests/ -v
```

### Frontend Testing
```bash
cd frontend
npm run test
```

### Integration Testing
```bash
# Run both backend and frontend
npm run test:integration
```

## 📊 Monitoring & Analytics

### Built-in Analytics
- Execution success rates
- Average response times
- Node performance metrics
- Error breakdown and trends
- Token usage tracking

### Logging
- Structured logging with multiple levels
- Error tracking and reporting
- Performance monitoring
- User activity tracking

## 🔒 Security

### Authentication & Authorization
- JWT-based authentication
- Role-based access control
- API key management
- Rate limiting

### Data Protection
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- Secure configuration management

## 🚀 Deployment

### Docker Deployment
```bash
# Build and run with Docker Compose
docker-compose up -d
```

### Production Considerations
- Use environment-specific configurations
- Set up proper logging and monitoring
- Configure SSL/TLS certificates
- Implement backup strategies
- Set up health checks and alerts

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

### Development Guidelines
- Follow TypeScript/Python best practices
- Write comprehensive tests
- Document new features
- Follow the existing code style
- Update documentation as needed

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

### Documentation
- API Documentation: `/docs` endpoint
- Component Documentation: Storybook (coming soon)
- User Guide: In-app help system

### Community
- GitHub Issues for bug reports
- Discussions for feature requests
- Discord community (coming soon)

### Enterprise Support
Contact us for enterprise support, custom integrations, and professional services.

---

Built with ❤️ for the AI community. Create, test, and deploy intelligent agents with ease.
